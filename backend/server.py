from concurrent import futures
import grpc
import time
import sys
import datetime

# 强制输出不缓冲 - 解决PyCharm输出问题
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

# 设置环境变量来抑制gRPC警告
import os
os.environ['GRPC_VERBOSITY'] = 'ERROR'
os.environ['GRPC_TRACE'] = ''

# 添加即时输出测试
print("🚀 Python后端服务启动中...", flush=True)

# 导入统一日志系统
from subtitle.utils.unified_logger import setup_unified_logging, get_logger

# 导入新生成的 gRPC Python 代码
# Greeter service
from api_protos.v1.greeter import greeter_pb2
from api_protos.v1.greeter import greeter_pb2_grpc
# Subtitler service (needed for add_SubtitlerServicer_to_server)
from api_protos.v1.subtitler import subtitler_pb2_grpc
from api_protos.v1.ai_config import ai_config_service_pb2_grpc

# 导入新的服务实现
from subtitle_service import SubtitleServiceImpl
from ai_config_service import AIConfigurationServiceServicer

# 设置统一日志系统 - 强制启用控制台输出用于调试
print("📝 正在设置日志系统...", flush=True)

# 抑制absl日志警告
import logging
logging.getLogger('absl').setLevel(logging.ERROR)

setup_unified_logging(
    log_level="DEBUG",  # 设置日志级别为 DEBUG (大写)
    enable_console=True,  # 强制启用控制台输出
    enable_file=True
)
print("✅ 日志系统设置完成", flush=True)

# 获取日志器
logger = get_logger(__name__)
print("📊 日志器获取完成", flush=True)

class GreeterServicer(greeter_pb2_grpc.GreeterServicer): # 修改基类
    def SayHello(self, request, context): # 方法签名不变
        logger.info(f"收到SayHello请求", extra={'request_name': request.name, 'operation': 'SayHello'})
        
        original_name = request.name
        server_id_str = "Python Server" 
        current_timestamp_str = datetime.datetime.now().isoformat()

        # 您可以自定义问候消息的格式
        response_message_str = f"Hello {original_name} from {server_id_str}!"
       
        # 使用新的 greeter_pb2 来创建 Reply 对象
        reply = greeter_pb2.HelloReply(
            message=response_message_str,
            original_request=original_name,
            server_id=server_id_str,
            timestamp=current_timestamp_str
        )
        logger.info(f"准备响应", extra={
            'operation': 'SayHello',
            'response_message': reply.message,
            'original_request': reply.original_request,
            'server_id': reply.server_id,
            'timestamp': reply.timestamp
        })
        return reply

def serve():
    print("🔧 创建gRPC服务器...", flush=True)
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    
    print("📋 注册服务...", flush=True)
    # 使用新的 greeter_pb2_grpc 来注册 Greeter 服务
    greeter_pb2_grpc.add_GreeterServicer_to_server(GreeterServicer(), server)
    # 注册 Subtitler 服务
    subtitler_pb2_grpc.add_SubtitlerServicer_to_server(SubtitleServiceImpl(), server)
    ai_config_service_pb2_grpc.add_AIConfigurationServiceServicer_to_server(AIConfigurationServiceServicer(), server)
    
    port = '50051'
    server.add_insecure_port(f'[::]:{port}')
    print(f"🌐 服务器绑定到端口 {port}", flush=True)
    
    logger.info(f"gRPC服务器启动中", extra={'operation': 'server_start', 'port': port})
    print(f"⚡ 启动gRPC服务器... 端口: {port}", flush=True)
    server.start()
    logger.info(f"gRPC服务器已启动并监听", extra={'operation': 'server_started', 'port': port})
    print(f"✅ gRPC服务器已启动并监听端口 {port}", flush=True)
    
    try:
        while True:
            time.sleep(86400)  # 一天
    except KeyboardInterrupt:
        logger.info("gRPC服务器停止中", extra={'operation': 'server_stop'})
        server.stop(0)
        logger.info("gRPC服务器已停止", extra={'operation': 'server_stopped'})

if __name__ == '__main__':
    serve()