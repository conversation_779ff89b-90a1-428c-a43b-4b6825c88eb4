# 统一日志系统使用指南

## 📋 概述

该项目已实现了统一的日志系统，解决了之前日志分散、格式不一致的问题。

## 🎯 **日志系统特点**

### **智能环境检测**
系统会自动检测运行环境并选择合适的日志目录：

**开发环境**（Development）：
- 日志目录：`backend/AppData/logs/`
- 触发条件：当前目录包含 `electron-app` 或 `backend`

**生产环境**（Production）：
- 日志目录：`~/Library/Application Support/electron-python-grpc-pex-demo/logs/`
- 触发条件：检测到PEX环境或命令行包含 `pex`

### **日志文件结构**
无论哪种环境，都会生成以下日志文件：
- `subtitle_app.log` - 主应用日志（详细格式）
- `subtitle_error.log` - 错误专用日志
- `subtitle_structured.log` - JSON格式日志（便于分析）

### **多种格式支持**
- **控制台输出**：彩色格式，便于开发调试
- **文件输出**：详细格式，包含完整上下文信息
- **JSON输出**：结构化格式，便于日志分析和监控

### **自动日志轮转**
- 单个日志文件最大 10MB
- 保留 5 个备份文件
- 自动清理旧日志

## 🚀 **如何使用**

### **1. 基础使用**

```python
# 导入统一日志系统
from subtitle.utils.unified_logger import get_logger

# 获取日志器（会自动使用调用模块的名称）
logger = get_logger()

# 或者指定日志器名称
logger = get_logger(__name__)

# 基础日志记录
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
```

### **2. 增强功能使用**

```python
# 记录操作日志（带额外信息）
logger.info("字幕生成完成", extra={
    'operation': 'generate_subtitles',
    'duration': 12.5,
    'file_path': '/path/to/video.mp4',
    'success': True
})

# 记录错误（自动包含异常堆栈）
try:
    # 一些操作
    pass
except Exception as e:
    logger.error("字幕生成失败", exc_info=True, extra={
        'operation': 'generate_subtitles',
        'file_path': '/path/to/video.mp4',
        'error_code': 'SUBTITLE_001'
    })
```

### **3. 便捷函数使用**

```python
from subtitle.utils.unified_logger import log_operation, log_error

# 记录操作
log_operation(
    operation="video_processing",
    duration=15.2,
    success=True,
    file_size="125MB"
)

# 记录错误
try:
    # 一些操作
    pass
except Exception as e:
    log_error(e, operation="video_processing")
```

## 📊 **日志格式示例**

### **控制台输出（彩色）**
```
[INFO] 14:23:45 subtitle.transcriptThread - 字幕生成完成 | 操作: generate_subtitles | 耗时: 12.50s
[ERROR] 14:24:12 subtitle.core.split - 字幕分割失败 | 错误码: SPLIT_001
```

### **文件输出（详细）**
```
2025-01-06 14:23:45.123 - subtitle.transcriptThread.generate_subtitles_from_text:285 - INFO - 字幕生成完成 | operation:generate_subtitles | duration:12.50s | file_path:/path/to/video.mp4
```

### **JSON输出（结构化）**
```json
{
  "timestamp": "2025-01-06T14:23:45.123456",
  "level": "INFO",
  "logger": "subtitle.transcriptThread",
  "message": "字幕生成完成",
  "module": "transcriptThread",
  "function": "generate_subtitles_from_text",
  "line": 285,
  "extra": {
    "operation": "generate_subtitles",
    "duration": 12.5,
    "file_path": "/path/to/video.mp4",
    "success": true
  }
}
```

## ⚙️ **配置选项**

### **初始化配置**
```python
from subtitle.utils.unified_logger import setup_unified_logging

# 自定义配置
setup_unified_logging(
    log_level="DEBUG",           # 日志级别
    enable_console=True,         # 启用控制台输出
    enable_file=True,            # 启用文件输出
    max_file_size=20*1024*1024,  # 单文件最大20MB
    backup_count=10,             # 保留10个备份
    force_reinit=True            # 强制重新初始化
)
```

## 🔧 **迁移指南**

### **从旧日志系统迁移**

**替换前：**
```python
import logging
from subtitle.utils.logger import setup_logger

logger = setup_logger("my_module")
logger.info("操作完成")
```

**替换后：**
```python
from subtitle.utils.unified_logger import get_logger

logger = get_logger(__name__)
logger.info("操作完成", extra={'operation': 'my_operation'})
```

### **服务器日志迁移**

**替换前：**
```python
import logging
logging.basicConfig(...)
logging.info("服务器启动")
```

**替换后：**
```python
from subtitle.utils.unified_logger import setup_unified_logging, get_logger

setup_unified_logging()
logger = get_logger(__name__)
logger.info("服务器启动", extra={'operation': 'server_start'})
```

## 📈 **最佳实践**

### **1. 使用结构化日志**
```python
# 好的做法
logger.info("用户登录成功", extra={
    'operation': 'user_login',
    'user_id': 'user123',
    'ip_address': '***********',
    'duration': 0.5
})

# 避免的做法
logger.info(f"用户user123从***********登录成功，耗时0.5秒")
```

### **2. 适当的日志级别**
- `DEBUG`：详细的调试信息
- `INFO`：一般信息（操作成功、状态变更）
- `WARNING`：警告信息（非致命问题）
- `ERROR`：错误信息（失败操作、异常）
- `CRITICAL`：严重错误（系统级问题）

### **3. 错误日志包含上下文**
```python
try:
    process_video(video_path)
except Exception as e:
    logger.error("视频处理失败", exc_info=True, extra={
        'operation': 'process_video',
        'video_path': video_path,
        'video_size': get_file_size(video_path),
        'error_code': 'VIDEO_001'
    })
```

### **4. 性能敏感的操作**
```python
import time

start_time = time.time()
try:
    result = expensive_operation()
    duration = time.time() - start_time
    logger.info("操作完成", extra={
        'operation': 'expensive_operation',
        'duration': duration,
        'success': True
    })
except Exception as e:
    duration = time.time() - start_time
    logger.error("操作失败", exc_info=True, extra={
        'operation': 'expensive_operation',
        'duration': duration,
        'success': False
    })
```

## 🛠️ **故障排查**

### **查看最新日志**

**开发环境**：
```bash
# 查看主应用日志
tail -f backend/AppData/logs/subtitle_app.log

# 查看错误日志
tail -f backend/AppData/logs/subtitle_error.log

# 查看结构化日志（可用于分析）
tail -f backend/AppData/logs/subtitle_structured.log
```

**生产环境**：
```bash
# 查看主应用日志
tail -f ~/Library/Application\ Support/electron-python-grpc-pex-demo/logs/subtitle_app.log

# 查看错误日志
tail -f ~/Library/Application\ Support/electron-python-grpc-pex-demo/logs/subtitle_error.log

# 查看结构化日志
tail -f ~/Library/Application\ Support/electron-python-grpc-pex-demo/logs/subtitle_structured.log
```

### **日志分析**
使用JSON格式的日志可以轻松进行分析：

**开发环境**：
```bash
# 查找特定操作的日志
grep '"operation":"generate_subtitles"' backend/AppData/logs/subtitle_structured.log

# 查找错误日志
grep '"level":"ERROR"' backend/AppData/logs/subtitle_structured.log
```

**生产环境**：
```bash
# 查找特定操作的日志
grep '"operation":"generate_subtitles"' ~/Library/Application\ Support/electron-python-grpc-pex-demo/logs/subtitle_structured.log

# 查找错误日志
grep '"level":"ERROR"' ~/Library/Application\ Support/electron-python-grpc-pex-demo/logs/subtitle_structured.log
```

## 🔗 **相关文件**

- `backend/subtitle/utils/unified_logger.py` - 统一日志系统实现
- `backend/subtitle/config.py` - 日志配置
- `backend/server.py` - 服务器日志示例
- `backend/subtitle/transcriptThread.py` - 字幕处理日志示例

---

通过使用统一日志系统，可以：
- ✅ 集中管理所有日志输出
- ✅ 保持一致的日志格式
- ✅ 便于问题排查和性能监控
- ✅ 支持日志分析和可视化 