# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from api_protos.v1.subtitler import subtitler_pb2 as api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in api_protos/v1/subtitler/subtitler_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class SubtitlerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.VideoToAudio = channel.unary_stream(
                '/monkeyfx.api.v1.subtitler.Subtitler/VideoToAudio',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.VideoToAudioRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
                _registered_method=True)
        self.AudioToText = channel.unary_stream(
                '/monkeyfx.api.v1.subtitler.Subtitler/AudioToText',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.AudioToTextRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
                _registered_method=True)
        self.GenerateSubtitles = channel.unary_stream(
                '/monkeyfx.api.v1.subtitler.Subtitler/GenerateSubtitles',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.GenerateSubtitlesRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
                _registered_method=True)
        self.TranslateSubtitles = channel.unary_stream(
                '/monkeyfx.api.v1.subtitler.Subtitler/TranslateSubtitles',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.TranslateSubtitlesRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
                _registered_method=True)
        self.ProcessVideoToTranslatedSubtitles = channel.unary_stream(
                '/monkeyfx.api.v1.subtitler.Subtitler/ProcessVideoToTranslatedSubtitles',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProcessVideoToTranslatedSubtitlesRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
                _registered_method=True)
        self.SaveSubtitle = channel.unary_unary(
                '/monkeyfx.api.v1.subtitler.Subtitler/SaveSubtitle',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.SaveSubtitleRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.SaveSubtitleResponse.FromString,
                _registered_method=True)
        self.BatchSaveSubtitle = channel.unary_unary(
                '/monkeyfx.api.v1.subtitler.Subtitler/BatchSaveSubtitle',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.BatchSaveSubtitleRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.BatchSaveSubtitleResponse.FromString,
                _registered_method=True)
        self.ClearCache = channel.unary_unary(
                '/monkeyfx.api.v1.subtitler.Subtitler/ClearCache',
                request_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ClearCacheRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ClearCacheResponse.FromString,
                _registered_method=True)


class SubtitlerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def VideoToAudio(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AudioToText(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateSubtitles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TranslateSubtitles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProcessVideoToTranslatedSubtitles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveSubtitle(self, request, context):
        """新增：字幕保存服务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchSaveSubtitle(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearCache(self, request, context):
        """新增：缓存管理服务
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SubtitlerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'VideoToAudio': grpc.unary_stream_rpc_method_handler(
                    servicer.VideoToAudio,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.VideoToAudioRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.SerializeToString,
            ),
            'AudioToText': grpc.unary_stream_rpc_method_handler(
                    servicer.AudioToText,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.AudioToTextRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.SerializeToString,
            ),
            'GenerateSubtitles': grpc.unary_stream_rpc_method_handler(
                    servicer.GenerateSubtitles,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.GenerateSubtitlesRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.SerializeToString,
            ),
            'TranslateSubtitles': grpc.unary_stream_rpc_method_handler(
                    servicer.TranslateSubtitles,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.TranslateSubtitlesRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.SerializeToString,
            ),
            'ProcessVideoToTranslatedSubtitles': grpc.unary_stream_rpc_method_handler(
                    servicer.ProcessVideoToTranslatedSubtitles,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProcessVideoToTranslatedSubtitlesRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.SerializeToString,
            ),
            'SaveSubtitle': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveSubtitle,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.SaveSubtitleRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.SaveSubtitleResponse.SerializeToString,
            ),
            'BatchSaveSubtitle': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchSaveSubtitle,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.BatchSaveSubtitleRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.BatchSaveSubtitleResponse.SerializeToString,
            ),
            'ClearCache': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearCache,
                    request_deserializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ClearCacheRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ClearCacheResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'monkeyfx.api.v1.subtitler.Subtitler', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('monkeyfx.api.v1.subtitler.Subtitler', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class Subtitler(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def VideoToAudio(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/VideoToAudio',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.VideoToAudioRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AudioToText(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/AudioToText',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.AudioToTextRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenerateSubtitles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/GenerateSubtitles',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.GenerateSubtitlesRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TranslateSubtitles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/TranslateSubtitles',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.TranslateSubtitlesRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ProcessVideoToTranslatedSubtitles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/ProcessVideoToTranslatedSubtitles',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProcessVideoToTranslatedSubtitlesRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ProgressUpdate.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SaveSubtitle(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/SaveSubtitle',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.SaveSubtitleRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.SaveSubtitleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchSaveSubtitle(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/BatchSaveSubtitle',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.BatchSaveSubtitleRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.BatchSaveSubtitleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ClearCache(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/monkeyfx.api.v1.subtitler.Subtitler/ClearCache',
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ClearCacheRequest.SerializeToString,
            api__protos_dot_v1_dot_subtitler_dot_subtitler__pb2.ClearCacheResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
