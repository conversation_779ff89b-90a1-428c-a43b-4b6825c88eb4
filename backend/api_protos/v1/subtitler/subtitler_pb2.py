# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api-protos/v1/subtitler/subtitler.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'api-protos/v1/subtitler/subtitler.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'api-protos/v1/subtitler/subtitler.proto\x12\x19monkeyfx.api.v1.subtitler\x1a\x1cgoogle/protobuf/struct.proto\x1a\x19google/protobuf/any.proto\"\xc8\x01\n\x0b\x45rrorDetail\x12\x12\n\nerror_code\x18\x01 \x01(\t\x12\x19\n\x11technical_message\x18\x02 \x01(\t\x12\x14\n\x0cuser_message\x18\x03 \x01(\t\x12\x44\n\x07\x63ontext\x18\x04 \x03(\x0b\x32\x33.monkeyfx.api.v1.subtitler.ErrorDetail.ContextEntry\x1a.\n\x0c\x43ontextEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x9c\x06\n\x0eProgressUpdate\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x12\x12\n\nstage_name\x18\x02 \x01(\t\x12\x12\n\npercentage\x18\x03 \x01(\x05\x12\x0f\n\x07message\x18\x04 \x01(\t\x12:\n\x06status\x18\x05 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x06 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\x12\"\n\x04\x64\x61ta\x18\x07 \x01(\x0b\x32\x14.google.protobuf.Any\x12\x14\n\x08is_error\x18\x08 \x01(\x08\x42\x02\x18\x01\x12\x19\n\rerror_message\x18\t \x01(\tB\x02\x18\x01\x12R\n\x17video_to_audio_response\x18\n \x01(\x0b\x32/.monkeyfx.api.v1.subtitler.VideoToAudioResponseH\x00\x12P\n\x16\x61udio_to_text_response\x18\x0b \x01(\x0b\x32..monkeyfx.api.v1.subtitler.AudioToTextResponseH\x00\x12[\n\x1bgenerate_subtitles_response\x18\x0c \x01(\x0b\x32\x34.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponseH\x00\x12]\n\x1ctranslate_subtitles_response\x18\r \x01(\x0b\x32\x35.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponseH\x00\x12~\n.process_video_to_translated_subtitles_response\x18\x0e \x01(\x0b\x32\x44.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponseH\x00\x42\x0e\n\x0c\x66inal_result\";\n\x13VideoToAudioRequest\x12\x12\n\nvideo_path\x18\x01 \x01(\t\x12\x10\n\x08trace_id\x18\x02 \x01(\t\"P\n\x14VideoToAudioResponse\x12\x12\n\naudio_path\x18\x01 \x01(\t\x12\x12\n\naudio_data\x18\x02 \x01(\x0c\x12\x10\n\x08trace_id\x18\x03 \x01(\t\"\x83\x01\n\x12\x41udioToTextRequest\x12\x12\n\naudio_path\x18\x01 \x01(\t\x12\x12\n\naudio_data\x18\x02 \x01(\x0c\x12\x1f\n\x17request_word_timestamps\x18\x03 \x01(\x08\x12\x12\n\nskip_cache\x18\x04 \x01(\x08\x12\x10\n\x08trace_id\x18\x05 \x01(\t\"\xcc\x01\n\x16TimestampedTextSegment\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x15\n\rstart_time_ms\x18\x02 \x01(\x03\x12\x13\n\x0b\x65nd_time_ms\x18\x03 \x01(\x03\x12:\n\x06status\x18\x04 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x05 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\xc4\x01\n\x13\x41udioToTextResponse\x12\x43\n\x08segments\x18\x01 \x03(\x0b\x32\x31.monkeyfx.api.v1.subtitler.TimestampedTextSegment\x12\x10\n\x08trace_id\x18\x02 \x01(\t\x12 \n\x18total_segments_processed\x18\x03 \x01(\x05\x12\x1b\n\x13successful_segments\x18\x04 \x01(\x05\x12\x17\n\x0f\x66\x61iled_segments\x18\x05 \x01(\x05\"b\n\x18GenerateSubtitlesRequest\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x12\n\naudio_path\x18\x02 \x01(\t\x12\x12\n\nskip_cache\x18\x03 \x01(\x08\x12\x10\n\x08trace_id\x18\x04 \x01(\t\"W\n\x19GenerateSubtitlesResponse\x12\x13\n\x0bsrt_content\x18\x01 \x01(\t\x12\x13\n\x0b\x61ss_content\x18\x02 \x01(\t\x12\x10\n\x08trace_id\x18\x03 \x01(\t\"t\n\x19TranslateSubtitlesRequest\x12\x18\n\x10subtitle_content\x18\x01 \x01(\t\x12\x17\n\x0ftarget_language\x18\x02 \x01(\t\x12\x12\n\nskip_cache\x18\x03 \x01(\x08\x12\x10\n\x08trace_id\x18\x04 \x01(\t\"\xd7\x01\n\x17TranslatedSegmentResult\x12\x12\n\nsegment_id\x18\x01 \x01(\t\x12\x15\n\roriginal_text\x18\x02 \x01(\t\x12\x17\n\x0ftranslated_text\x18\x03 \x01(\t\x12:\n\x06status\x18\x04 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x05 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\xf8\x01\n\x1aTranslateSubtitlesResponse\x12#\n\x1btranslated_subtitle_content\x18\x01 \x01(\t\x12\x10\n\x08trace_id\x18\x02 \x01(\t\x12K\n\x0fsegment_results\x18\x03 \x03(\x0b\x32\x32.monkeyfx.api.v1.subtitler.TranslatedSegmentResult\x12 \n\x18total_segments_processed\x18\x04 \x01(\x05\x12\x1b\n\x13successful_segments\x18\x05 \x01(\x05\x12\x17\n\x0f\x66\x61iled_segments\x18\x06 \x01(\x05\"i\n(ProcessVideoToTranslatedSubtitlesRequest\x12\x12\n\nvideo_path\x18\x01 \x01(\t\x12\x17\n\x0ftarget_language\x18\x02 \x01(\t\x12\x10\n\x08trace_id\x18\x03 \x01(\t\"b\n)ProcessVideoToTranslatedSubtitlesResponse\x12#\n\x1btranslated_subtitle_content\x18\x01 \x01(\t\x12\x10\n\x08trace_id\x18\x02 \x01(\t\"\xd5\x02\n\x13SaveSubtitleRequest\x12\x18\n\x10subtitle_content\x18\x01 \x01(\t\x12\x0e\n\x06\x66ormat\x18\x02 \x01(\t\x12\x0e\n\x06layout\x18\x03 \x01(\t\x12\x11\n\tfile_name\x18\x04 \x01(\t\x12\x18\n\x10original_content\x18\x05 \x01(\t\x12\x1a\n\x12translated_content\x18\x06 \x01(\t\x12<\n\x08segments\x18\x07 \x03(\x0b\x32*.monkeyfx.api.v1.subtitler.SubtitleSegment\x12\x1c\n\x14\x61uto_save_to_default\x18\x08 \x01(\x08\x12\x37\n\x11\x61ss_style_options\x18\t \x01(\x0b\x32\x17.google.protobuf.StructH\x00\x88\x01\x01\x12\x10\n\x08trace_id\x18\n \x01(\tB\x14\n\x12_ass_style_options\"\xe4\x02\n\x14SaveSubtitleResponse\x12\x11\n\tfile_path\x18\x01 \x01(\t\x12\x11\n\tfile_data\x18\x02 \x01(\x0c\x12\x11\n\tfile_name\x18\x03 \x01(\t\x12\x11\n\tfile_size\x18\x04 \x01(\x03\x12\x18\n\x10saved_to_default\x18\x05 \x01(\x08\x12\x0e\n\x06\x66ormat\x18\x06 \x01(\t\x12\x0e\n\x06layout\x18\x07 \x01(\t\x12\x16\n\x0e\x63ontent_source\x18\x08 \x01(\t\x12\"\n\x1aoriginal_filename_or_title\x18\t \x01(\t\x12\x10\n\x08trace_id\x18\n \x01(\t\x12:\n\x06status\x18\x0b \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x0c \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\xe1\x01\n\x0fSubtitleSegment\x12\x12\n\nstart_time\x18\x01 \x01(\x05\x12\x10\n\x08\x65nd_time\x18\x02 \x01(\x05\x12\x15\n\roriginal_text\x18\x03 \x01(\t\x12\x17\n\x0ftranslated_text\x18\x04 \x01(\t\x12:\n\x06status\x18\x05 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x06 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"\x81\x03\n\x18\x42\x61tchSaveSubtitleRequest\x12\x0f\n\x07\x66ormats\x18\x01 \x03(\t\x12\x0f\n\x07layouts\x18\x02 \x03(\t\x12\x17\n\x0f\x63ontent_sources\x18\x03 \x03(\t\x12\x18\n\x10\x66ile_name_prefix\x18\x04 \x01(\t\x12\x18\n\x10original_content\x18\x05 \x01(\t\x12\x1a\n\x12translated_content\x18\x06 \x01(\t\x12<\n\x08segments\x18\x07 \x03(\x0b\x32*.monkeyfx.api.v1.subtitler.SubtitleSegment\x12\x1c\n\x14\x61uto_save_to_default\x18\x08 \x01(\x08\x12\x1d\n\x15translation_requested\x18\t \x01(\x08\x12\x37\n\x11\x61ss_style_options\x18\n \x01(\x0b\x32\x17.google.protobuf.StructH\x00\x88\x01\x01\x12\x10\n\x08trace_id\x18\x0b \x01(\tB\x14\n\x12_ass_style_options\"\xe7\x01\n\x19\x42\x61tchSaveSubtitleResponse\x12>\n\x05\x66iles\x18\x01 \x03(\x0b\x32/.monkeyfx.api.v1.subtitler.SaveSubtitleResponse\x12\x10\n\x08trace_id\x18\x02 \x01(\t\x12:\n\x06status\x18\x03 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x04 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail\"9\n\x11\x43learCacheRequest\x12\x12\n\ncache_type\x18\x01 \x01(\t\x12\x10\n\x08trace_id\x18\x02 \x01(\t\"\xc2\x01\n\x12\x43learCacheResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x10\n\x08trace_id\x18\x03 \x01(\t\x12:\n\x06status\x18\x04 \x01(\x0e\x32*.monkeyfx.api.v1.subtitler.OperationStatus\x12<\n\x0c\x65rror_detail\x18\x05 \x01(\x0b\x32&.monkeyfx.api.v1.subtitler.ErrorDetail*\xb5\x01\n\x0fOperationStatus\x12 \n\x1cOPERATION_STATUS_UNSPECIFIED\x10\x00\x12\x1c\n\x18OPERATION_STATUS_SUCCESS\x10\x01\x12\x1a\n\x16OPERATION_STATUS_ERROR\x10\x02\x12$\n OPERATION_STATUS_PARTIAL_SUCCESS\x10\x03\x12 \n\x1cOPERATION_STATUS_IN_PROGRESS\x10\x04\x32\xc7\x07\n\tSubtitler\x12k\n\x0cVideoToAudio\x12..monkeyfx.api.v1.subtitler.VideoToAudioRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12i\n\x0b\x41udioToText\x12-.monkeyfx.api.v1.subtitler.AudioToTextRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12u\n\x11GenerateSubtitles\x12\x33.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12w\n\x12TranslateSubtitles\x12\x34.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12\x95\x01\n!ProcessVideoToTranslatedSubtitles\x12\x43.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest\x1a).monkeyfx.api.v1.subtitler.ProgressUpdate0\x01\x12o\n\x0cSaveSubtitle\x12..monkeyfx.api.v1.subtitler.SaveSubtitleRequest\x1a/.monkeyfx.api.v1.subtitler.SaveSubtitleResponse\x12~\n\x11\x42\x61tchSaveSubtitle\x12\x33.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest\x1a\x34.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse\x12i\n\nClearCache\x12,.monkeyfx.api.v1.subtitler.ClearCacheRequest\x1a-.monkeyfx.api.v1.subtitler.ClearCacheResponseBy\n\"com.monkeyfx.grpc.api.v1.subtitlerB\x0eSubtitlerProtoP\x01ZAgithub.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/subtitlerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_protos.v1.subtitler.subtitler_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.monkeyfx.grpc.api.v1.subtitlerB\016SubtitlerProtoP\001ZAgithub.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/subtitler'
  _globals['_ERRORDETAIL_CONTEXTENTRY']._loaded_options = None
  _globals['_ERRORDETAIL_CONTEXTENTRY']._serialized_options = b'8\001'
  _globals['_PROGRESSUPDATE'].fields_by_name['is_error']._loaded_options = None
  _globals['_PROGRESSUPDATE'].fields_by_name['is_error']._serialized_options = b'\030\001'
  _globals['_PROGRESSUPDATE'].fields_by_name['error_message']._loaded_options = None
  _globals['_PROGRESSUPDATE'].fields_by_name['error_message']._serialized_options = b'\030\001'
  _globals['_OPERATIONSTATUS']._serialized_start=4605
  _globals['_OPERATIONSTATUS']._serialized_end=4786
  _globals['_ERRORDETAIL']._serialized_start=128
  _globals['_ERRORDETAIL']._serialized_end=328
  _globals['_ERRORDETAIL_CONTEXTENTRY']._serialized_start=282
  _globals['_ERRORDETAIL_CONTEXTENTRY']._serialized_end=328
  _globals['_PROGRESSUPDATE']._serialized_start=331
  _globals['_PROGRESSUPDATE']._serialized_end=1127
  _globals['_VIDEOTOAUDIOREQUEST']._serialized_start=1129
  _globals['_VIDEOTOAUDIOREQUEST']._serialized_end=1188
  _globals['_VIDEOTOAUDIORESPONSE']._serialized_start=1190
  _globals['_VIDEOTOAUDIORESPONSE']._serialized_end=1270
  _globals['_AUDIOTOTEXTREQUEST']._serialized_start=1273
  _globals['_AUDIOTOTEXTREQUEST']._serialized_end=1404
  _globals['_TIMESTAMPEDTEXTSEGMENT']._serialized_start=1407
  _globals['_TIMESTAMPEDTEXTSEGMENT']._serialized_end=1611
  _globals['_AUDIOTOTEXTRESPONSE']._serialized_start=1614
  _globals['_AUDIOTOTEXTRESPONSE']._serialized_end=1810
  _globals['_GENERATESUBTITLESREQUEST']._serialized_start=1812
  _globals['_GENERATESUBTITLESREQUEST']._serialized_end=1910
  _globals['_GENERATESUBTITLESRESPONSE']._serialized_start=1912
  _globals['_GENERATESUBTITLESRESPONSE']._serialized_end=1999
  _globals['_TRANSLATESUBTITLESREQUEST']._serialized_start=2001
  _globals['_TRANSLATESUBTITLESREQUEST']._serialized_end=2117
  _globals['_TRANSLATEDSEGMENTRESULT']._serialized_start=2120
  _globals['_TRANSLATEDSEGMENTRESULT']._serialized_end=2335
  _globals['_TRANSLATESUBTITLESRESPONSE']._serialized_start=2338
  _globals['_TRANSLATESUBTITLESRESPONSE']._serialized_end=2586
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST']._serialized_start=2588
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESREQUEST']._serialized_end=2693
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESRESPONSE']._serialized_start=2695
  _globals['_PROCESSVIDEOTOTRANSLATEDSUBTITLESRESPONSE']._serialized_end=2793
  _globals['_SAVESUBTITLEREQUEST']._serialized_start=2796
  _globals['_SAVESUBTITLEREQUEST']._serialized_end=3137
  _globals['_SAVESUBTITLERESPONSE']._serialized_start=3140
  _globals['_SAVESUBTITLERESPONSE']._serialized_end=3496
  _globals['_SUBTITLESEGMENT']._serialized_start=3499
  _globals['_SUBTITLESEGMENT']._serialized_end=3724
  _globals['_BATCHSAVESUBTITLEREQUEST']._serialized_start=3727
  _globals['_BATCHSAVESUBTITLEREQUEST']._serialized_end=4112
  _globals['_BATCHSAVESUBTITLERESPONSE']._serialized_start=4115
  _globals['_BATCHSAVESUBTITLERESPONSE']._serialized_end=4346
  _globals['_CLEARCACHEREQUEST']._serialized_start=4348
  _globals['_CLEARCACHEREQUEST']._serialized_end=4405
  _globals['_CLEARCACHERESPONSE']._serialized_start=4408
  _globals['_CLEARCACHERESPONSE']._serialized_end=4602
  _globals['_SUBTITLER']._serialized_start=4789
  _globals['_SUBTITLER']._serialized_end=5756
# @@protoc_insertion_point(module_scope)
