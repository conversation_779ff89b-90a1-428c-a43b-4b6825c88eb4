#!/usr/bin/env python3
"""
简单的测试服务器 - 用于调试
"""

import sys
import os

print("🚀 测试脚本开始执行...")
print(f"Python 版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python 路径: {sys.path}")

try:
    print("📦 测试导入...")
    
    # 测试基础导入
    import grpc
    print("✅ grpc 导入成功")
    
    # 测试 proto 导入
    from api_protos.v1.greeter import greeter_pb2
    print("✅ greeter_pb2 导入成功")
    
    from api_protos.v1.greeter import greeter_pb2_grpc
    print("✅ greeter_pb2_grpc 导入成功")
    
    from api_protos.v1.subtitler import subtitler_pb2_grpc
    print("✅ subtitler_pb2_grpc 导入成功")
    
    # 测试服务实现导入
    from subtitle_service import SubtitleServiceImpl
    print("✅ SubtitleServiceImpl 导入成功")
    
    # 测试日志系统
    from subtitle.utils.unified_logger import setup_unified_logging, get_logger
    print("✅ 日志系统导入成功")
    
    # 设置日志
    setup_unified_logging(
        log_level="debug",
        enable_console=True,
        enable_file=False  # 暂时禁用文件日志
    )
    
    logger = get_logger(__name__)
    logger.info("🎉 日志系统测试成功！")
    
    print("🎯 所有导入测试通过！")
    
    # 测试我们的调试代码
    print("\n🔍 测试调试代码...")
    
    # 导入我们修改的模块
    from subtitle.workflow.transcriptThread import TranscriptThread
    print("✅ TranscriptThread 导入成功")
    
    from subtitle.workflow.audio_processor import AudioProcessor
    print("✅ AudioProcessor 导入成功")
    
    from subtitle.async_stream_processor import AsyncStreamProcessor
    print("✅ AsyncStreamProcessor 导入成功")
    
    # 测试创建实例
    audio_processor = AudioProcessor()
    print("✅ AudioProcessor 实例创建成功")
    
    transcript_thread = TranscriptThread()
    print("✅ TranscriptThread 实例创建成功")
    
    print("\n🎉 所有测试通过！现在启动 gRPC 服务器...")
    
    # 启动简单的 gRPC 服务器
    from concurrent import futures
    import time
    
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    
    # 注册服务
    from api_protos.v1.greeter import greeter_pb2_grpc
    from api_protos.v1.subtitler import subtitler_pb2_grpc
    
    class SimpleGreeter(greeter_pb2_grpc.GreeterServicer):
        def SayHello(self, request, context):
            print(f"🔥 收到 SayHello 请求: {request.name}")
            return greeter_pb2.HelloReply(
                message=f"Hello {request.name} from Test Server!",
                original_request=request.name,
                server_id="Test Server",
                timestamp="2024-01-01T00:00:00"
            )
    
    greeter_pb2_grpc.add_GreeterServicer_to_server(SimpleGreeter(), server)
    subtitler_pb2_grpc.add_SubtitlerServicer_to_server(SubtitleServiceImpl(), server)
    
    port = '50051'
    server.add_insecure_port(f'[::]:{port}')
    
    print(f"🚀 启动 gRPC 服务器，监听端口 {port}...")
    server.start()
    print(f"✅ gRPC 服务器已启动并监听端口 {port}")
    
    try:
        while True:
            time.sleep(1)
            print("💓 服务器运行中...")
            time.sleep(9)  # 每10秒打印一次心跳
    except KeyboardInterrupt:
        print("🛑 收到停止信号，关闭服务器...")
        server.stop(0)
        print("✅ 服务器已停止")

except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
