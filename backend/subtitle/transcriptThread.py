"""
重构后的TranscriptThread主类

使用模块化组件来处理不同的功能，提高代码的可维护性和可读性。
"""

import logging
import traceback
from pathlib import Path
from typing import Generator, Dict, Optional, Any

# 导入重构后的模块
from .workflow.progress_manager import _create_progress, default_progress_manager
from .workflow.audio_processor import AudioProcessor
from .workflow.transcription_processor import TranscriptionProcessor
from .utils.retry_manager import retry_manager, OperationMetrics

# 导入标准化常量和工厂
from .constants import (
    OperationStatus, ErrorCode, StageNames, LogMessages,
    DefaultValues, ResponseFieldNames, ProgressKeys, FileFormats
)
from .utils.progress_factory import (
    create_file_not_found_error,
    create_success_progress,
    create_error_progress,
    create_in_progress_update,
    create_validation_error
)

logger = logging.getLogger(__name__)


class TranscriptThread:
    """
    重构后的TranscriptThread主类
    
    负责协调各个处理模块，管理整体工作流程。
    """
    
    def __init__(self):
        """初始化TranscriptThread"""
        # 初始化各个处理器
        self.audio_processor = AudioProcessor()
        self.transcription_processor = TranscriptionProcessor()
        
        # 路径管理
        self.video_path = None
        self.audio_path = None
        self.raw_srt_output_path = None
        
        # 回调函数
        self._current_progress_yield_callback = None
        
        # 进度管理
        self.progress_manager = default_progress_manager
        
        logger.info("TranscriptThread 重构版本初始化完成")
        
    def _setup_paths_for_video(self, video_path_str: str):
        """设置视频相关的文件路径"""
        self.video_path = Path(video_path_str).resolve()
        
        # 设置输出路径
        video_stem = self.video_path.stem
        output_dir = self.video_path.parent / f"{video_stem}_subtitle_output"
        
        self.raw_srt_output_path = output_dir / f"{video_stem}_raw{FileFormats.SUBTITLE_SRT.value}"
        
        # 为处理器设置路径
        self.transcription_processor.set_raw_srt_output_path(self.raw_srt_output_path)
        
    def _internal_progress_callback(self, value: float, message: str, stage_prefix: str = ""):
        """内部进度回调函数"""
        if self._current_progress_yield_callback:
            self._current_progress_yield_callback(value, f"{stage_prefix}{message}")
            
    def interrupt_operation(self, operation_id: str):
        """中断指定的操作"""
        try:
            retry_manager.interrupt_operation(operation_id)
            logger.info(f"操作已中断: {operation_id}")
        except Exception as e:
            logger.error(f"中断操作失败: {e}")
            
    def get_operation_metrics(self, operation_id: str = None) -> Dict[str, OperationMetrics]:
        """获取操作指标"""
        return retry_manager.get_all_metrics() if operation_id is None else retry_manager.get_operation_metrics(operation_id)

    def extract_audio_from_video(self, video_path_str: str, trace_id: str = None) -> Generator[Dict, None, None]:
        """
        从视频中提取音频
        
        Args:
            video_path_str: 视频文件路径
            trace_id: 追踪ID
            
        Yields:
            进度更新字典
        """
        logger.info(f"开始音频提取: {video_path_str}, trace_id: {trace_id}")
        
        self._setup_paths_for_video(video_path_str)
        
        # 使用音频处理器执行提取
        yield from self.audio_processor.extract_audio_from_video(video_path_str, trace_id)
        
        # 更新音频路径
        self.audio_path = self.audio_processor.get_audio_path()

    def transcribe_audio(self, audio_path: str = None, request_word_timestamps: bool = None, 
                        trace_id: str = None) -> Generator[Dict, None, None]:
        """
        转录音频为文本
        
        Args:
            audio_path: 音频文件路径（可选，默认使用提取的音频）
            request_word_timestamps: 是否请求词级时间戳
            trace_id: 追踪ID
            
        Yields:
            进度更新字典
        """
        logger.info(f"开始音频转录: {audio_path}, trace_id: {trace_id}")
        
        # 确定音频路径
        if audio_path is None:
            audio_path = str(self.audio_path) if self.audio_path else None
            
        if not audio_path:
            error_msg = "没有可用的音频文件进行转录"
            logger.error(error_msg)
            yield create_validation_error(
                stage_name=StageNames.TRANSCRIBE_AUDIO,
                validation_message=error_msg,
                trace_id=trace_id,
                context={"audio_path": audio_path}
            )
            return
        
        # 使用转录处理器执行转录
        yield from self.transcription_processor.transcribe_audio(
            audio_path=audio_path,
            request_word_timestamps=request_word_timestamps,
            trace_id=trace_id
        )

    def generate_subtitles_from_text(self, text: str = None, audio_path: str = None, 
                                   skip_cache: bool = False, trace_id: str = None) -> Generator[Dict, None, None]:
        """
        从文本生成字幕
        
        Args:
            text: 输入文本
            audio_path: 音频路径
            skip_cache: 是否跳过缓存
            trace_id: 追踪ID
            
        Yields:
            进度更新字典
        """
        stage_name = StageNames.GENERATE_SUBTITLES
        logger.info(f"调用 generate_subtitles_from_text，文本长度: {len(text) if text else 0}, 音频路径: {audio_path}, trace_id: {trace_id}")
        
        try:
            # 验证输入
            if not text and not self.transcription_processor.has_transcription_data():
                err_msg = "必须提供文本内容或先运行音频转文本步骤"
                logger.error(f"{stage_name}: {err_msg}")
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=0,
                    message=err_msg,
                    is_error=True,
                    error_message=err_msg,
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "INVALID_INPUT",
                        "technical_message": "缺少文本内容且未运行音频转文本步骤",
                        "user_message": "请提供文本内容或先完成音频转文本步骤",
                        "context": {}
                    }
                )
                return

            # 如果没有提供文本，使用转录结果
            if not text and self.transcription_processor.has_transcription_data():
                asr_data = self.transcription_processor.get_asr_data()
                srt_content = asr_data.to_srt()
                ass_content = asr_data.to_ass()
                
                final_payload = {
                    "srt_content": srt_content,
                    "ass_content": ass_content
                }
                
                yield create_success_progress(
                    stage_name=stage_name,
                    message="字幕生成完成",
                    trace_id=trace_id,
                    data=final_payload,
                    final_result={"generate_subtitles_response": final_payload}
                )
                return

            # 如果提供了文本，使用文本分割器生成字幕
            if text:
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=20,
                    message="开始分割文本...",
                    trace_id=trace_id,
                    status="IN_PROGRESS"
                )
                
                # TODO: 实现文本分割器
                # 这里应该调用SubtitleSplitter来分割文本
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=0,
                    message="从纯文本生成字幕功能需要实现文本分割器",
                    is_error=True,
                    error_message="文本分割器尚未集成",
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "SPLITTER_NOT_IMPLEMENTED",
                        "technical_message": "SubtitleSplitter尚未在重构后的模块中集成",
                        "user_message": "从文本生成字幕功能正在开发中",
                        "context": {"text_length": len(text)}
                    }
                )
                return
            
        except Exception as e:
            tb_str = traceback.format_exc()
            err_msg = f"字幕生成失败: {e}"
            logger.error(f"{stage_name}: {err_msg}\n{tb_str}")
            yield _create_progress(
                stage_name=stage_name,
                percentage=0,
                message=err_msg,
                is_error=True,
                error_message=f"{e}\n{tb_str}",
                trace_id=trace_id,
                status="ERROR",
                error_detail={
                    "error_code": "UNEXPECTED_ERROR",
                    "technical_message": str(e),
                    "user_message": "字幕生成过程中发生意外错误",
                    "context": {"exception_type": type(e).__name__}
                }
            )

    def translate_subtitle_content(self, subtitle_content: str, target_language: str, 
                                 input_format: str = "srt", skip_cache: bool = False, 
                                 trace_id: str = None) -> Generator[Dict, None, None]:
        """
        翻译字幕内容
        
        Args:
            subtitle_content: 字幕内容
            target_language: 目标语言
            input_format: 输入格式
            skip_cache: 是否跳过缓存
            trace_id: 追踪ID
            
        Yields:
            进度更新字典
        """
        stage_name = StageNames.TRANSLATE_SUBTITLES
        logger.info(f"开始翻译字幕，目标语言: {target_language}, trace_id: {trace_id}")
        
        try:
            # 验证输入
            if not subtitle_content:
                err_msg = "字幕内容不能为空"
                logger.error(f"{stage_name}: {err_msg}")
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=0,
                    message=err_msg,
                    is_error=True,
                    error_message=err_msg,
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "INVALID_INPUT",
                        "technical_message": "字幕内容为空",
                        "user_message": "请提供有效的字幕内容",
                        "context": {}
                    }
                )
                return
            
            if not target_language:
                err_msg = "目标语言不能为空"
                logger.error(f"{stage_name}: {err_msg}")
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=0,
                    message=err_msg,
                    is_error=True,
                    error_message=err_msg,
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "INVALID_INPUT",
                        "technical_message": "目标语言为空",
                        "user_message": "请指定翻译的目标语言",
                        "context": {}
                    }
                )
                return
            
            # TODO: 实现字幕翻译功能
            # 这里需要集成translate.py中的翻译器
            yield _create_progress(
                stage_name=stage_name,
                percentage=0,
                message="字幕翻译功能需要集成翻译器模块",
                is_error=True,
                error_message="翻译器模块尚未集成",
                trace_id=trace_id,
                status="ERROR",
                error_detail={
                    "error_code": "TRANSLATOR_NOT_IMPLEMENTED",
                    "technical_message": "翻译器模块尚未在重构后的模块中集成",
                    "user_message": "字幕翻译功能正在开发中",
                    "context": {"target_language": target_language}
                }
            )
            
        except Exception as e:
            tb_str = traceback.format_exc()
            err_msg = f"字幕翻译失败: {e}"
            logger.error(f"{stage_name}: {err_msg}\n{tb_str}")
            yield _create_progress(
                stage_name=stage_name,
                percentage=0,
                message=err_msg,
                is_error=True,
                error_message=f"{e}\n{tb_str}",
                trace_id=trace_id,
                status="ERROR",
                error_detail={
                    "error_code": "UNEXPECTED_ERROR",
                    "technical_message": str(e),
                    "user_message": "字幕翻译过程中发生意外错误",
                    "context": {"exception_type": type(e).__name__}
                }
            )

    def process_video_to_translated_subtitles(self, video_path_str: str, target_language: str, 
                                            request_word_timestamps: bool = None, 
                                            trace_id: str = None) -> Generator[Dict, None, None]:
        """
        完整的视频到翻译字幕的处理流程
        
        Args:
            video_path_str: 视频文件路径
            target_language: 目标语言
            request_word_timestamps: 是否请求词级时间戳
            trace_id: 追踪ID
            
        Yields:
            进度更新字典
        """
        stage_name = StageNames.PROCESS_VIDEO
        logger.info(f"开始完整工作流程: {video_path_str} -> {target_language}, trace_id: {trace_id}")
        
        try:
            # 1. 提取音频 (0-25%)
            yield _create_progress(
                stage_name=stage_name,
                percentage=5,
                message="开始提取音频...",
                trace_id=trace_id,
                status="IN_PROGRESS"
            )
            
            audio_extracted = False
            for progress_update in self.extract_audio_from_video(video_path_str, trace_id):
                # 重新映射进度到0-25%范围
                adjusted_percentage = int(progress_update.get("percentage", 0) * 0.25)
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=adjusted_percentage,
                    message=f"音频提取: {progress_update.get('message', '')}",
                    trace_id=trace_id,
                    status=progress_update.get("status", "IN_PROGRESS"),
                    is_error=progress_update.get("is_error", False),
                    error_message=progress_update.get("error_message", ""),
                    error_detail=progress_update.get("error_detail")
                )
                
                if progress_update.get("percentage") >= 100 and not progress_update.get("is_error"):
                    audio_extracted = True
                elif progress_update.get("is_error"):
                    return  # 早期终止
            
            if not audio_extracted:
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=0,
                    message="音频提取失败，中止流程",
                    is_error=True,
                    error_message="音频提取未成功完成",
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "AUDIO_EXTRACTION_FAILED",
                        "technical_message": "音频提取未成功完成",
                        "user_message": "无法从视频中提取音频，请检查视频文件",
                        "context": {"video_path": video_path_str}
                    }
                )
                return
            
            # 2. 转录音频 (25-70%)
            yield _create_progress(
                stage_name=stage_name,
                percentage=25,
                message="开始音频转录...",
                trace_id=trace_id,
                status="IN_PROGRESS"
            )
            
            transcription_completed = False
            for progress_update in self.transcribe_audio(request_word_timestamps=request_word_timestamps, trace_id=trace_id):
                # 重新映射进度到25-70%范围
                adjusted_percentage = 25 + int(progress_update.get("percentage", 0) * 0.45)
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=adjusted_percentage,
                    message=f"音频转录: {progress_update.get('message', '')}",
                    trace_id=trace_id,
                    status=progress_update.get("status", "IN_PROGRESS"),
                    is_error=progress_update.get("is_error", False),
                    error_message=progress_update.get("error_message", ""),
                    error_detail=progress_update.get("error_detail")
                )
                
                if progress_update.get("percentage") >= 100 and not progress_update.get("is_error"):
                    transcription_completed = True
                elif progress_update.get("is_error"):
                    return  # 早期终止
            
            if not transcription_completed:
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=25,
                    message="音频转录失败，中止流程",
                    is_error=True,
                    error_message="音频转录未成功完成",
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "TRANSCRIPTION_FAILED",
                        "technical_message": "音频转录未成功完成",
                        "user_message": "音频转文字失败，请检查音频质量",
                        "context": {}
                    }
                )
                return
            
            # 3. 生成字幕 (70-85%)
            yield _create_progress(
                stage_name=stage_name,
                percentage=70,
                message="开始生成字幕...",
                trace_id=trace_id,
                status="IN_PROGRESS"
            )
            
            subtitle_generated = False
            for progress_update in self.generate_subtitles_from_text(trace_id=trace_id):
                # 重新映射进度到70-85%范围
                adjusted_percentage = 70 + int(progress_update.get("percentage", 0) * 0.15)
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=adjusted_percentage,
                    message=f"字幕生成: {progress_update.get('message', '')}",
                    trace_id=trace_id,
                    status=progress_update.get("status", "IN_PROGRESS"),
                    is_error=progress_update.get("is_error", False),
                    error_message=progress_update.get("error_message", ""),
                    error_detail=progress_update.get("error_detail")
                )
                
                if progress_update.get("percentage") >= 100 and not progress_update.get("is_error"):
                    subtitle_generated = True
                elif progress_update.get("is_error"):
                    return  # 早期终止
            
            if not subtitle_generated:
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=70,
                    message="字幕生成失败，中止流程",
                    is_error=True,
                    error_message="字幕生成未成功完成",
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "SUBTITLE_GENERATION_FAILED",
                        "technical_message": "字幕生成未成功完成",
                        "user_message": "字幕生成失败，无法继续",
                        "context": {}
                    }
                )
                return
            
            # 4. 翻译字幕 (85-100%) - 如果需要
            if target_language and target_language.lower() != 'zh':
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=85,
                    message="开始字幕翻译...",
                    trace_id=trace_id,
                    status="IN_PROGRESS"
                )
                
                # 获取生成的字幕内容
                if self.transcription_processor.has_transcription_data():
                    asr_data = self.transcription_processor.get_asr_data()
                    srt_content = asr_data.to_srt()
                    
                    translation_completed = False
                    for progress_update in self.translate_subtitle_content(srt_content, target_language, trace_id=trace_id):
                        # 重新映射进度到85-100%范围
                        adjusted_percentage = 85 + int(progress_update.get("percentage", 0) * 0.15)
                        yield _create_progress(
                            stage_name=stage_name,
                            percentage=adjusted_percentage,
                            message=f"字幕翻译: {progress_update.get('message', '')}",
                            trace_id=trace_id,
                            status=progress_update.get("status", "IN_PROGRESS"),
                            is_error=progress_update.get("is_error", False),
                            error_message=progress_update.get("error_message", ""),
                            error_detail=progress_update.get("error_detail")
                        )
                        
                        if progress_update.get("percentage") >= 100 and not progress_update.get("is_error"):
                            translation_completed = True
                        elif progress_update.get("is_error"):
                            # 翻译失败但不中止，使用原文
                            logger.warning("字幕翻译失败，将使用原文")
                            break
            else:
                # 跳过翻译
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=100,
                    message="目标语言为中文，跳过翻译步骤",
                    trace_id=trace_id,
                    status="IN_PROGRESS"
                )
            
            # 最终完成
            yield _create_progress(
                stage_name=stage_name,
                percentage=100,
                message="完整工作流程已完成",
                trace_id=trace_id,
                status="SUCCESS",
                data={
                    "video_path": video_path_str,
                    "target_language": target_language,
                    "audio_extracted": True,
                    "transcription_completed": True,
                    "subtitle_generated": True
                }
            )
            
        except Exception as e:
            tb_str = traceback.format_exc()
            err_msg = f"完整工作流程失败: {e}"
            logger.error(f"{stage_name}: {err_msg}\n{tb_str}")
            yield _create_progress(
                stage_name=stage_name,
                percentage=0,
                message=err_msg,
                is_error=True,
                error_message=f"{e}\n{tb_str}",
                trace_id=trace_id,
                status="ERROR",
                error_detail={
                    "error_code": "WORKFLOW_ERROR",
                    "technical_message": str(e),
                    "user_message": "工作流程执行过程中发生错误",
                    "context": {"exception_type": type(e).__name__}
                }
            )

    def cleanup(self):
        """清理临时文件和资源"""
        try:
            self.audio_processor.cleanup()
            logger.info("TranscriptThread清理完成")
        except Exception as e:
            logger.warning(f"TranscriptThread清理时发生错误: {e}")


# 导出主要功能
__all__ = ['TranscriptThread', '_create_progress'] 