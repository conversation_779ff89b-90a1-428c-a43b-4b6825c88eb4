# -*- coding: utf-8 -*-
"""
异步流式处理器

提供流式数据处理、实时进度更新和并发流处理功能。
支持gRPC流式响应和WebSocket实时通信。
"""
import asyncio
import json
import time
from typing import AsyncGenerator, Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid
import weakref

from .logging_manager import get_logger
from .async_processor import get_async_task_manager, TaskPriority, TaskResult
from .performance_monitor import monitor_performance
from .exceptions import SubtitleProcessingError

logger = get_logger(__name__)


class StreamStatus(Enum):
    """流状态枚举"""
    INITIALIZING = "initializing"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"


@dataclass
class StreamProgress:
    """流进度数据类"""
    stream_id: str
    stage_name: str
    percentage: float
    message: str
    is_error: bool = False
    error_message: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "stream_id": self.stream_id,
            "stage_name": self.stage_name,
            "percentage": self.percentage,
            "message": self.message,
            "is_error": self.is_error,
            "error_message": self.error_message,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        }


class AsyncStreamProcessor:
    """异步流式处理器"""
    
    def __init__(self):
        self.active_streams: Dict[str, Dict[str, Any]] = {}
        self.stream_callbacks: Dict[str, List[Callable]] = {}
        self.task_manager = get_async_task_manager()
        
        logger.info("异步流式处理器初始化完成")
    
    async def create_stream(
        self,
        stream_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建新的处理流"""
        if stream_id is None:
            stream_id = str(uuid.uuid4())
        
        self.active_streams[stream_id] = {
            "status": StreamStatus.INITIALIZING,
            "created_time": time.time(),
            "last_update": time.time(),
            "progress": 0.0,
            "current_stage": "初始化",
            "metadata": metadata or {},
            "task_ids": [],
            "results": []
        }
        
        self.stream_callbacks[stream_id] = []
        
        logger.info(f"创建处理流: {stream_id}")
        return stream_id
    
    def add_stream_callback(self, stream_id: str, callback: Callable):
        """添加流回调函数"""
        if stream_id in self.stream_callbacks:
            self.stream_callbacks[stream_id].append(callback)
    
    async def update_stream_progress(
        self,
        stream_id: str,
        stage_name: str,
        percentage: float,
        message: str,
        is_error: bool = False,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """更新流进度"""
        if stream_id not in self.active_streams:
            return
        
        # 更新流状态
        stream_info = self.active_streams[stream_id]
        stream_info["last_update"] = time.time()
        stream_info["progress"] = percentage
        stream_info["current_stage"] = stage_name
        
        if is_error:
            stream_info["status"] = StreamStatus.ERROR
        elif percentage >= 100.0:
            stream_info["status"] = StreamStatus.COMPLETED
        else:
            stream_info["status"] = StreamStatus.PROCESSING
        
        # 创建进度对象
        progress = StreamProgress(
            stream_id=stream_id,
            stage_name=stage_name,
            percentage=percentage,
            message=message,
            is_error=is_error,
            error_message=error_message,
            metadata=metadata or {}
        )
        
        # 调用回调函数
        callbacks = self.stream_callbacks.get(stream_id, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(progress)
                else:
                    callback(progress)
            except Exception as e:
                logger.error(f"流回调函数执行失败: {e}", exc_info=True)
        
        logger.debug(f"流进度更新: {stream_id} - {stage_name} ({percentage}%)")
    
    async def process_video_to_audio_stream(
        self,
        stream_id: str,
        video_path: str,
        config: Dict[str, Any]
    ) -> AsyncGenerator[StreamProgress, None]:
        """异步视频转音频流处理"""

        logger.error("🌟🌟🌟 [ASYNC_STREAM_PROCESSOR_DEBUG] process_video_to_audio_stream 方法被调用了！🌟🌟🌟")

        try:
            await self.update_stream_progress(
                stream_id, "视频分析", 10, "正在分析视频文件..."
            )
            yield StreamProgress(stream_id, "视频分析", 10, "正在分析视频文件...")
            
            # 模拟异步处理
            await asyncio.sleep(1)
            
            await self.update_stream_progress(
                stream_id, "音频提取", 50, "正在提取音频轨道..."
            )
            yield StreamProgress(stream_id, "音频提取", 50, "正在提取音频轨道...")
            
            await asyncio.sleep(2)
            
            await self.update_stream_progress(
                stream_id, "格式转换", 80, "正在转换音频格式..."
            )
            yield StreamProgress(stream_id, "格式转换", 80, "正在转换音频格式...")
            
            await asyncio.sleep(1)
            
            # 模拟结果
            audio_path = video_path.replace('.mp4', '.wav')
            result = {
                "audio_path": audio_path,
                "duration": 120.5,
                "sample_rate": 44100,
                "channels": 2
            }
            
            await self.update_stream_progress(
                stream_id, "提取完成", 100, "音频提取完成",
                metadata={"result": result}
            )
            yield StreamProgress(
                stream_id, "提取完成", 100, "音频提取完成",
                metadata={"result": result}
            )
            
        except Exception as e:
            await self.update_stream_progress(
                stream_id, "提取错误", 0, f"音频提取失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
            yield StreamProgress(
                stream_id, "提取错误", 0, f"音频提取失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
    
    async def process_audio_transcription_stream(
        self,
        stream_id: str,
        audio_path: str,
        config: Dict[str, Any]
    ) -> AsyncGenerator[StreamProgress, None]:
        """异步音频转录流处理"""
        
        try:
            # 检查缓存
            await self.update_stream_progress(
                stream_id, "缓存检查", 5, "检查转录缓存..."
            )
            yield StreamProgress(stream_id, "缓存检查", 5, "检查转录缓存...")
            
            await asyncio.sleep(0.5)
            
            # 模拟缓存未命中，开始转录
            await self.update_stream_progress(
                stream_id, "音频预处理", 15, "正在预处理音频文件..."
            )
            yield StreamProgress(stream_id, "音频预处理", 15, "正在预处理音频文件...")
            
            await asyncio.sleep(1)
            
            await self.update_stream_progress(
                stream_id, "语音识别", 40, "正在进行语音识别..."
            )
            yield StreamProgress(stream_id, "语音识别", 40, "正在进行语音识别...")
            
            # 模拟长时间处理
            for progress in range(45, 85, 5):
                await asyncio.sleep(0.8)
                await self.update_stream_progress(
                    stream_id, "语音识别", progress, f"识别进度 {progress}%..."
                )
                yield StreamProgress(stream_id, "语音识别", progress, f"识别进度 {progress}%...")
            
            await self.update_stream_progress(
                stream_id, "文本后处理", 90, "正在处理识别结果..."
            )
            yield StreamProgress(stream_id, "文本后处理", 90, "正在处理识别结果...")
            
            await asyncio.sleep(1)
            
            # 模拟转录结果
            transcription_result = {
                "segments": [
                    {
                        "text": "这是一个示例转录文本",
                        "start_time": 0,
                        "end_time": 3000,
                        "confidence": 0.95
                    },
                    {
                        "text": "展示异步流式处理功能",
                        "start_time": 3000,
                        "end_time": 6000,
                        "confidence": 0.92
                    }
                ],
                "language": config.get("language", "zh"),
                "model": config.get("model", "JIANYING")
            }
            
            await self.update_stream_progress(
                stream_id, "转录完成", 100, "音频转录完成",
                metadata={"result": transcription_result}
            )
            yield StreamProgress(
                stream_id, "转录完成", 100, "音频转录完成",
                metadata={"result": transcription_result}
            )
            
        except Exception as e:
            await self.update_stream_progress(
                stream_id, "转录错误", 0, f"音频转录失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
            yield StreamProgress(
                stream_id, "转录错误", 0, f"音频转录失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
    
    async def process_subtitle_optimization_stream(
        self,
        stream_id: str,
        subtitle_content: str,
        config: Dict[str, Any]
    ) -> AsyncGenerator[StreamProgress, None]:
        """异步字幕优化流处理"""
        
        try:
            await self.update_stream_progress(
                stream_id, "内容分析", 20, "正在分析字幕内容..."
            )
            yield StreamProgress(stream_id, "内容分析", 20, "正在分析字幕内容...")
            
            await asyncio.sleep(1)
            
            await self.update_stream_progress(
                stream_id, "语法优化", 50, "正在优化语法结构..."
            )
            yield StreamProgress(stream_id, "语法优化", 50, "正在优化语法结构...")
            
            await asyncio.sleep(2)
            
            await self.update_stream_progress(
                stream_id, "断句优化", 80, "正在优化断句..."
            )
            yield StreamProgress(stream_id, "断句优化", 80, "正在优化断句...")
            
            await asyncio.sleep(1)
            
            # 模拟优化结果
            optimized_content = f"[优化] {subtitle_content}"
            result = {
                "optimized_content": optimized_content,
                "original_length": len(subtitle_content),
                "optimized_length": len(optimized_content),
                "improvement_score": 0.85
            }
            
            await self.update_stream_progress(
                stream_id, "优化完成", 100, "字幕优化完成",
                metadata={"result": result}
            )
            yield StreamProgress(
                stream_id, "优化完成", 100, "字幕优化完成",
                metadata={"result": result}
            )
            
        except Exception as e:
            await self.update_stream_progress(
                stream_id, "优化错误", 0, f"字幕优化失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
            yield StreamProgress(
                stream_id, "优化错误", 0, f"字幕优化失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
    
    async def process_translation_stream(
        self,
        stream_id: str,
        content: str,
        target_language: str,
        config: Dict[str, Any]
    ) -> AsyncGenerator[StreamProgress, None]:
        """异步翻译流处理"""
        
        try:
            await self.update_stream_progress(
                stream_id, "翻译准备", 10, "正在准备翻译..."
            )
            yield StreamProgress(stream_id, "翻译准备", 10, "正在准备翻译...")
            
            await asyncio.sleep(0.5)
            
            await self.update_stream_progress(
                stream_id, "语言检测", 25, "正在检测源语言..."
            )
            yield StreamProgress(stream_id, "语言检测", 25, "正在检测源语言...")
            
            await asyncio.sleep(1)
            
            await self.update_stream_progress(
                stream_id, "翻译处理", 60, f"正在翻译到{target_language}..."
            )
            yield StreamProgress(stream_id, "翻译处理", 60, f"正在翻译到{target_language}...")
            
            await asyncio.sleep(2)
            
            await self.update_stream_progress(
                stream_id, "后处理", 90, "正在优化翻译结果..."
            )
            yield StreamProgress(stream_id, "后处理", 90, "正在优化翻译结果...")
            
            await asyncio.sleep(1)
            
            # 模拟翻译结果
            translated_content = f"[翻译到{target_language}] {content}"
            result = {
                "translated_content": translated_content,
                "source_language": "auto",
                "target_language": target_language,
                "confidence": 0.92
            }
            
            await self.update_stream_progress(
                stream_id, "翻译完成", 100, "翻译完成",
                metadata={"result": result}
            )
            yield StreamProgress(
                stream_id, "翻译完成", 100, "翻译完成",
                metadata={"result": result}
            )
            
        except Exception as e:
            await self.update_stream_progress(
                stream_id, "翻译错误", 0, f"翻译失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
            yield StreamProgress(
                stream_id, "翻译错误", 0, f"翻译失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
    
    async def process_complete_workflow_stream(
        self,
        stream_id: str,
        video_path: str,
        config: Dict[str, Any]
    ) -> AsyncGenerator[StreamProgress, None]:
        """完整工作流异步流处理"""
        
        try:
            # 步骤1: 视频转音频
            async for progress in self.process_video_to_audio_stream(stream_id, video_path, config):
                yield progress
                if progress.is_error:
                    return
            
            # 步骤2: 音频转录
            audio_path = video_path.replace('.mp4', '.wav')
            async for progress in self.process_audio_transcription_stream(stream_id, audio_path, config):
                yield progress
                if progress.is_error:
                    return
            
            # 步骤3: 字幕优化
            subtitle_content = "示例字幕内容"
            async for progress in self.process_subtitle_optimization_stream(stream_id, subtitle_content, config):
                yield progress
                if progress.is_error:
                    return
            
            # 步骤4: 翻译（如果需要）
            if config.get("enable_translation", False):
                target_language = config.get("target_language", "English")
                async for progress in self.process_translation_stream(stream_id, subtitle_content, target_language, config):
                    yield progress
                    if progress.is_error:
                        return
            
            # 工作流完成
            await self.update_stream_progress(
                stream_id, "工作流完成", 100, "所有处理步骤已完成"
            )
            yield StreamProgress(stream_id, "工作流完成", 100, "所有处理步骤已完成")
            
        except Exception as e:
            await self.update_stream_progress(
                stream_id, "工作流错误", 0, f"工作流处理失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
            yield StreamProgress(
                stream_id, "工作流错误", 0, f"工作流处理失败: {str(e)}",
                is_error=True, error_message=str(e)
            )
    
    def get_stream_info(self, stream_id: str) -> Optional[Dict[str, Any]]:
        """获取流信息"""
        return self.active_streams.get(stream_id)
    
    def get_active_streams(self) -> Dict[str, Dict[str, Any]]:
        """获取所有活跃流"""
        return self.active_streams.copy()
    
    async def cancel_stream(self, stream_id: str) -> bool:
        """取消流处理"""
        if stream_id in self.active_streams:
            stream_info = self.active_streams[stream_id]
            stream_info["status"] = StreamStatus.CANCELLED
            
            # 取消相关任务
            for task_id in stream_info.get("task_ids", []):
                await self.task_manager.cancel_task(task_id)
            
            await self.update_stream_progress(
                stream_id, "已取消", 0, "流处理已取消"
            )
            
            logger.info(f"流处理已取消: {stream_id}")
            return True
        
        return False
    
    def cleanup_completed_streams(self, max_age_hours: int = 24):
        """清理已完成的流"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        streams_to_remove = []
        for stream_id, stream_info in self.active_streams.items():
            if (stream_info["status"] in [StreamStatus.COMPLETED, StreamStatus.ERROR, StreamStatus.CANCELLED] and
                (current_time - stream_info["last_update"]) > max_age_seconds):
                streams_to_remove.append(stream_id)
        
        for stream_id in streams_to_remove:
            self.active_streams.pop(stream_id, None)
            self.stream_callbacks.pop(stream_id, None)
        
        if streams_to_remove:
            logger.info(f"清理了 {len(streams_to_remove)} 个过期流")


# 全局异步流处理器
_async_stream_processor = None


def get_async_stream_processor() -> AsyncStreamProcessor:
    """获取全局异步流处理器"""
    global _async_stream_processor
    if _async_stream_processor is None:
        _async_stream_processor = AsyncStreamProcessor()
    return _async_stream_processor
