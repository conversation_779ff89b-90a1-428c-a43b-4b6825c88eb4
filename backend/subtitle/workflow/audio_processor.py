"""
音频处理模块

负责从视频文件中提取音频，支持多种视频格式。
"""

import logging
import traceback
from pathlib import Path
from typing import Generator, Dict, Optional, Callable

from subtitle.utils.video_utils import video2audio
from subtitle.utils.retry_manager import retry_manager, RetryConfig, RetryStrategy
from subtitle.constants import (
    StageNames, ErrorCode, OperationStatus, FileFormats,
    LogMessages, DefaultValues, ResponseFieldNames
)
from subtitle.utils.progress_factory import (
    create_file_not_found_error,
    create_success_progress,
    create_error_progress,
    create_in_progress_update
)

logger = logging.getLogger(__name__)


class AudioProcessor:
    """音频处理器"""
    
    def __init__(self, progress_callback: Optional[Callable] = None):
        self.progress_callback = progress_callback
        self.audio_path = None
        
    def extract_audio_from_video(self, video_path_str: str, trace_id: str = None) -> Generator[Dict, None, None]:
        """
        从视频文件中提取音频
        
        Args:
            video_path_str: 视频文件路径
            trace_id: 追踪ID
            
        Yields:
            进度更新字典
        """
        stage_name = StageNames.EXTRACT_AUDIO
        logger.info(LogMessages.AUDIO_EXTRACTION_START.format(
            video_path=video_path_str, trace_id=trace_id
        ))
        
        try:
            video_path = Path(video_path_str).resolve()
            if not video_path.exists():
                logger.error(f"{stage_name}: 视频文件不存在: {video_path}")
                yield create_file_not_found_error(
                    stage_name=stage_name,
                    file_path=str(video_path),
                    trace_id=trace_id
                )
                return

            yield create_in_progress_update(
                stage_name=stage_name,
                percentage=5,
                message="正在初始化音频提取器...",
                trace_id=trace_id
            )

            # 设置输出路径
            self.audio_path = video_path.parent / f"{video_path.stem}{FileFormats.AUDIO_WAV.value}"
            
            yield create_in_progress_update(
                stage_name=stage_name,
                percentage=10,
                message=f"准备提取音频到: {self.audio_path.name}",
                trace_id=trace_id
            )



            # 发送开始进度
            yield create_in_progress_update(
                stage_name=stage_name,
                percentage=10,
                message="开始音频提取...",
                trace_id=trace_id
            )

            # 发送中间进度
            yield create_in_progress_update(
                stage_name=stage_name,
                percentage=30,
                message="正在转换音频格式...",
                trace_id=trace_id
            )

            # 执行音频提取（简化版，不等待进度回调）
            def _extract_audio():
                """实际的音频提取逻辑"""
                success = video2audio(
                    input_file=str(video_path),
                    output=str(self.audio_path)
                    # 不传递 progress_callback，让处理更快
                )
                if not success:
                    raise RuntimeError("音频提取失败")

            # 使用重试管理器执行提取
            config = RetryConfig(
                max_attempts=DefaultValues.DEFAULT_RETRY_ATTEMPTS,
                strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
                base_delay=1.0
            )

            retry_manager.retry_with_config(
                func=_extract_audio,
                config=config
            )

            # 发送接近完成的进度
            yield create_in_progress_update(
                stage_name=stage_name,
                percentage=90,
                message="音频转换即将完成...",
                trace_id=trace_id
            )

            # 验证输出文件
            if not self.audio_path.exists():
                error_msg = f"音频提取完成但输出文件不存在: {self.audio_path}"
                logger.error(f"{stage_name}: {error_msg}")
                yield create_error_progress(
                    stage_name=stage_name,
                    error_message=error_msg,
                    trace_id=trace_id,
                    error_code=ErrorCode.OUTPUT_FILE_MISSING,
                    error_detail={
                        "error_code": ErrorCode.OUTPUT_FILE_MISSING.value,
                        "technical_message": f"音频文件提取后不存在: {self.audio_path}",
                        "user_message": "音频提取失败，输出文件未生成",
                        "context": {"audio_path": str(self.audio_path)}
                    }
                )
                return

            # 最终成功
            # 只包含 VideoToAudioResponse protobuf 支持的字段
            final_payload = {
                "audio_path": str(self.audio_path),
                # audio_data 字段暂时为空，如果需要可以读取文件内容
                # "audio_data": b"",
                # trace_id 会在 subtitle_service.py 中单独设置
            }

            # 额外的数据信息放在 data 字段中
            data_payload = {
                "audio_path": str(self.audio_path),
                "video_path": str(video_path),
                "audio_size": self.audio_path.stat().st_size
            }

            logger.info(LogMessages.AUDIO_EXTRACTION_SUCCESS.format(
                audio_path=str(self.audio_path)
            ))

            yield create_success_progress(
                stage_name=stage_name,
                message=f"音频提取完成: {self.audio_path.name}",
                trace_id=trace_id,
                data=data_payload,
                final_result={ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE: final_payload}
            )

        except Exception as e:
            tb_str = traceback.format_exc()
            error_msg = f"音频提取失败: {e}"
            logger.error(LogMessages.AUDIO_EXTRACTION_FAILED.format(error=error_msg))
            logger.error(f"{stage_name}: {error_msg}\n{tb_str}")
            
            yield create_error_progress(
                stage_name=stage_name,
                error_message=f"{e}\n{tb_str}",
                trace_id=trace_id,
                error_code=ErrorCode.UNEXPECTED_ERROR,
                error_detail={
                    "error_code": ErrorCode.UNEXPECTED_ERROR.value,
                    "technical_message": str(e),
                    "user_message": "音频提取过程中发生意外错误",
                    "context": {"exception_type": type(e).__name__}
                }
            )

    def get_audio_path(self) -> Optional[Path]:
        """获取音频文件路径"""
        return self.audio_path
    
    def cleanup(self):
        """清理临时文件"""
        if self.audio_path and self.audio_path.exists():
            try:
                self.audio_path.unlink()
                logger.info(f"已清理音频文件: {self.audio_path}")
            except Exception as e:
                logger.warning(f"清理音频文件失败: {e}") 