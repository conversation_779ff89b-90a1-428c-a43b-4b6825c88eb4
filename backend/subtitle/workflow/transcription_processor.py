"""
转录处理模块

负责将音频转换为文本，支持多种转录服务。
"""

import logging
import traceback
from pathlib import Path
from typing import Generator, Dict, Optional, Callable, Any
from subtitle.core.transcribe import transcribe
from subtitle.entities import TranscribeConfig, TranscribeModelEnum
from .progress_manager import _create_progress

logger = logging.getLogger(__name__)


class TranscriptionProcessor:
    """转录处理器"""
    
    def __init__(self, progress_callback: Optional[Callable] = None):
        self.progress_callback = progress_callback
        self.asr_data = None
        self.raw_srt_output_path = None
        
    def transcribe_audio(self, audio_path: str, request_word_timestamps: bool = None, 
                        trace_id: str = None, transcriptor_type: str = "whisper",
                        api_key: str = None, base_url: str = None, model: str = None) -> Generator[Dict, None, None]:
        """
        转录音频为文本
        
        Args:
            audio_path: 音频文件路径
            request_word_timestamps: 是否请求词级时间戳
            trace_id: 追踪ID
            transcriptor_type: 转录器类型
            api_key: API密钥
            base_url: API基础URL
            model: 模型名称
            
        Yields:
            进度更新字典
        """
        stage_name = "AudioTranscription"
        logger.info(f"开始转录音频: {audio_path}, trace_id: {trace_id}")
        
        try:
            audio_path_obj = Path(audio_path)
            if not audio_path_obj.exists():
                err_msg = f"音频文件不存在: {audio_path}"
                logger.error(f"{stage_name}: {err_msg}")
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=0,
                    message=err_msg,
                    is_error=True,
                    error_message=err_msg,
                    trace_id=trace_id,
                    status="ERROR",
                    error_detail={
                        "error_code": "FILE_NOT_FOUND",
                        "technical_message": f"音频文件路径不存在: {audio_path}",
                        "user_message": "找不到指定的音频文件",
                        "context": {"audio_path": audio_path}
                    }
                )
                return

            yield _create_progress(
                stage_name=stage_name,
                percentage=5,
                message="正在初始化转录器...",
                trace_id=trace_id,
                status="IN_PROGRESS"
            )
            
            # 创建进度队列
            progress_updates_queue = []
            
            # 进度回调函数
            def progress_callback(percent, message):
                progress_updates_queue.append({
                    "stage_name": stage_name,
                    "percentage": percent,
                    "message": message,
                    "trace_id": trace_id,
                    "status": "IN_PROGRESS" if percent < 100 else "SUCCESS"
                })
            
            # 创建转录配置
            config = TranscribeConfig(
                transcribe_model=TranscribeModelEnum.JIANYING,  # 默认使用剪映
                transcribe_language="zh",
                use_asr_cache=True,
                need_word_time_stamp=request_word_timestamps if request_word_timestamps is not None else True
            )
            
            # 执行转录
            self.asr_data = transcribe(str(audio_path), config, callback=progress_callback)
            
            # 处理队列中的进度更新
            for progress_update in progress_updates_queue:
                yield _create_progress(
                    stage_name=progress_update["stage_name"],
                    percentage=progress_update["percentage"],
                    message=progress_update["message"],
                    trace_id=trace_id,
                    status=progress_update["status"]
                )
            
            # 保存原始SRT文件
            if hasattr(self, 'raw_srt_output_path') and self.raw_srt_output_path:
                self.raw_srt_output_path.parent.mkdir(parents=True, exist_ok=True)
                self.asr_data.save(save_path=str(self.raw_srt_output_path)) 
                logger.info(f"{stage_name}: 原始SRT文件已保存到: {self.raw_srt_output_path}")
                yield _create_progress(
                    stage_name=stage_name,
                    percentage=90,
                    message=f"原始SRT已保存: {self.raw_srt_output_path.name}",
                    trace_id=trace_id,
                    status="IN_PROGRESS"
                )
            
            # 构建段落结果
            segments_list = []
            successful_segments = 0
            failed_segments = 0
            
            for i, seg in enumerate(self.asr_data.segments):
                segment_status = "SUCCESS"
                segment_error = None
                
                # 检查转录是否成功
                if not seg.text or seg.text.strip() == "":
                    segment_status = "ERROR"
                    segment_error = {
                        "error_code": "EMPTY_TRANSCRIPTION",
                        "technical_message": "转录结果为空",
                        "user_message": "此段落转录失败",
                        "context": {"segment_index": str(i)}
                    }
                    failed_segments += 1
                else:
                    successful_segments += 1
                
                segments_list.append({
                    "text": seg.text,
                    "start_time": seg.start_time,
                    "end_time": seg.end_time,
                    "status": segment_status,
                    "error_detail": segment_error
                })
            
            # 生成最终响应
            final_status = "SUCCESS"
            if failed_segments > 0:
                final_status = "PARTIAL_SUCCESS" if successful_segments > 0 else "ERROR"
            
            final_payload = {
                "segments": segments_list,
                "total_segments_processed": len(segments_list),
                "successful_segments": successful_segments,
                "failed_segments": failed_segments,
                "audio_path": str(audio_path)
            }
            
            # 使用新的进度工厂函数创建成功进度
            from ..utils.progress_factory import create_success_progress

            yield create_success_progress(
                stage_name=stage_name,
                message=f"转录完成，共 {len(segments_list)} 段，成功 {successful_segments} 段，失败 {failed_segments} 段",
                trace_id=trace_id,
                data=final_payload,
                final_result={"audio_to_text_response": final_payload}
            )
            
        except Exception as e:
            tb_str = traceback.format_exc()
            err_msg = f"音频转录失败: {e}"
            logger.error(f"{stage_name}: {err_msg}\n{tb_str}")
            yield _create_progress(
                stage_name=stage_name,
                percentage=0,
                message=err_msg,
                is_error=True,
                error_message=f"{e}\n{tb_str}",
                trace_id=trace_id,
                status="ERROR",
                error_detail={
                    "error_code": "UNEXPECTED_ERROR",
                    "technical_message": str(e),
                    "user_message": "音频转录过程中发生意外错误",
                    "context": {"exception_type": type(e).__name__}
                }
            )

    def get_asr_data(self):
        """获取转录数据"""
        return self.asr_data
    
    def set_raw_srt_output_path(self, path: Path):
        """设置原始SRT输出路径"""
        self.raw_srt_output_path = path
    
    def has_transcription_data(self) -> bool:
        """检查是否有转录数据"""
        return self.asr_data is not None and hasattr(self.asr_data, 'segments') and self.asr_data.segments 