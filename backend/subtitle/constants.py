"""
统一的常量和枚举定义

该文件包含所有应该标准化的常量、枚举和魔法字符串，
避免在代码中重复定义和硬编码。
"""

from enum import Enum


class OperationStatus(Enum):
    """操作状态枚举 - 统一状态管理"""
    UNSPECIFIED = "UNSPECIFIED"
    IN_PROGRESS = "IN_PROGRESS"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    PARTIAL_SUCCESS = "PARTIAL_SUCCESS"
    CANCELLED = "CANCELLED"
    PENDING = "PENDING"


class ErrorCode(Enum):
    """错误代码枚举 - 统一错误分类"""
    FILE_NOT_FOUND = "FILE_NOT_FOUND"
    UNEXPECTED_ERROR = "UNEXPECTED_ERROR"
    SERVICE_ERROR = "SERVICE_ERROR"
    OUTPUT_FILE_MISSING = "OUTPUT_FILE_MISSING"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    NETWORK_ERROR = "NETWORK_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    PERMISSION_ERROR = "PERMISSION_ERROR"
    QUOTA_EXCEEDED = "QUOTA_EXCEEDED"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    # TranscriptThread专用错误代码
    INVALID_INPUT = "INVALID_INPUT"
    SPLITTER_NOT_IMPLEMENTED = "SPLITTER_NOT_IMPLEMENTED"
    TRANSLATOR_NOT_IMPLEMENTED = "TRANSLATOR_NOT_IMPLEMENTED"
    AUDIO_EXTRACTION_FAILED = "AUDIO_EXTRACTION_FAILED"
    TRANSCRIPTION_FAILED = "TRANSCRIPTION_FAILED"
    SUBTITLE_GENERATION_FAILED = "SUBTITLE_GENERATION_FAILED"
    WORKFLOW_ERROR = "WORKFLOW_ERROR"


class FileFormats(Enum):
    """文件格式枚举 - 统一文件扩展名管理"""
    # 音频格式
    AUDIO_WAV = ".wav"
    AUDIO_MP3 = ".mp3"
    AUDIO_FLAC = ".flac"
    AUDIO_AAC = ".aac"
    AUDIO_OGG = ".ogg"
    
    # 视频格式
    VIDEO_MP4 = ".mp4"
    VIDEO_AVI = ".avi"
    VIDEO_MOV = ".mov"
    VIDEO_MKV = ".mkv"
    VIDEO_FLV = ".flv"
    VIDEO_WMV = ".wmv"
    
    # 字幕格式
    SUBTITLE_SRT = ".srt"
    SUBTITLE_ASS = ".ass"
    SUBTITLE_VTT = ".vtt"
    SUBTITLE_JSON = ".json"
    SUBTITLE_TXT = ".txt"


class ProgressKeys:
    """进度字典键名常量 - 统一字典键值管理"""
    STAGE_NAME = "stage_name"
    PERCENTAGE = "percentage"
    MESSAGE = "message"
    IS_ERROR = "is_error"
    ERROR_MESSAGE = "error_message"
    TRACE_ID = "trace_id"
    STATUS = "status"
    ERROR_DETAIL = "error_detail"
    DATA = "data"
    FINAL_RESULT = "final_result"


class StageNames:
    """处理阶段名称常量 - 统一阶段命名"""
    EXTRACT_AUDIO = "ExtractAudio"
    TRANSCRIBE_AUDIO = "TranscribeAudio"
    GENERATE_SUBTITLES = "GenerateSubtitles"
    TRANSLATE_SUBTITLES = "TranslateSubtitles"
    SAVE_SUBTITLES = "SaveSubtitles"
    PROCESS_VIDEO = "ProcessVideo"
    VALIDATE_INPUT = "ValidateInput"
    CLEANUP = "Cleanup"


class LogMessages:
    """日志消息模板 - 统一日志格式"""
    # 音频处理
    AUDIO_EXTRACTION_START = "开始从视频提取音频: {video_path}, trace_id: {trace_id}"
    AUDIO_EXTRACTION_SUCCESS = "音频提取完成: {audio_path}"
    AUDIO_EXTRACTION_FAILED = "音频提取失败: {error}"
    
    # 语音识别
    TRANSCRIPTION_START = "开始音频转录: {audio_path}, trace_id: {trace_id}"
    TRANSCRIPTION_SUCCESS = "音频转录完成，片段数: {segment_count}"
    TRANSCRIPTION_FAILED = "音频转录失败: {error}"
    
    # 字幕生成
    SUBTITLE_GENERATION_START = "开始字幕生成，文本长度: {text_length}"
    SUBTITLE_GENERATION_SUCCESS = "字幕生成完成: {subtitle_path}"
    SUBTITLE_GENERATION_FAILED = "字幕生成失败: {error}"
    
    # 翻译
    TRANSLATION_START = "开始翻译字幕，目标语言: {target_language}, trace_id: {trace_id}"
    TRANSLATION_SUCCESS = "字幕翻译完成"
    TRANSLATION_FAILED = "字幕翻译失败: {error}"
    
    # 服务层
    SERVICE_REQUEST_RECEIVED = "收到 {service_name} 请求"
    SERVICE_PROCESSING_ERROR = "{service_name} 处理过程中发生错误: {error}"
    SERVICE_RESPONSE_SUCCESS = "{service_name} 成功完成"


class DefaultValues:
    """默认值常量 - 统一默认设置"""
    DEFAULT_RETRY_ATTEMPTS = 3
    DEFAULT_TIMEOUT_SECONDS = 300
    DEFAULT_TRACE_ID = ""
    DEFAULT_PERCENTAGE = 0
    DEFAULT_MESSAGE = ""
    
    # 音频设置
    DEFAULT_AUDIO_FORMAT = FileFormats.AUDIO_WAV.value
    DEFAULT_VIDEO_FORMAT = FileFormats.VIDEO_MP4.value
    DEFAULT_SUBTITLE_FORMAT = FileFormats.SUBTITLE_SRT.value


class ResponseFieldNames:
    """响应字段名称常量 - protobuf响应字段统一管理"""
    VIDEO_TO_AUDIO_RESPONSE = "video_to_audio_response"
    AUDIO_TO_TEXT_RESPONSE = "audio_to_text_response"
    GENERATE_SUBTITLES_RESPONSE = "generate_subtitles_response"
    TRANSLATE_SUBTITLES_RESPONSE = "translate_subtitles_response"
    PROCESS_VIDEO_TO_TRANSLATED_SUBTITLES_RESPONSE = "process_video_to_translated_subtitles_response"


class HttpStatusCodes:
    """HTTP状态码常量"""
    OK = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_SERVER_ERROR = 500
    SERVICE_UNAVAILABLE = 503


class RetryConfig:
    """重试配置常量"""
    MAX_RETRIES = 3
    BASE_DELAY = 1.0
    MAX_DELAY = 60.0
    BACKOFF_MULTIPLIER = 2.0


# 向后兼容的字符串常量 (逐步迁移用)
class LegacyConstants:
    """遗留常量 - 用于向后兼容，逐步迁移到枚举"""
    SUCCESS = OperationStatus.SUCCESS.value
    ERROR = OperationStatus.ERROR.value
    IN_PROGRESS = OperationStatus.IN_PROGRESS.value
    PARTIAL_SUCCESS = OperationStatus.PARTIAL_SUCCESS.value 