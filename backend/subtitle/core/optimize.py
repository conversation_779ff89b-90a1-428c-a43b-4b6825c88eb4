import json
import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from typing import Callable, Dict, List, Optional, Union

from openai import OpenAI

from subtitle.config import CACHE_PATH
from subtitle.core.alignment import SubtitleAligner
from subtitle.core.asr_data import ASRData, ASRDataSeg
from subtitle.core.prompt import OPTIMIZER_PROMPT
from subtitle.storage import CacheManager
from subtitle.utils.unified_logger import get_logger
from subtitle.utils import json_repair


logger = get_logger(__name__)


class SubtitleOptimizer:
    """字幕优化器,支持缓存功能"""

    def __init__(
        self,
        thread_num: int = 5,
        batch_num: int = 10,
        model: str = "gpt-4o-mini",
        custom_prompt: str = "",
        temperature: float = 0.7,
        timeout: int = 60,
        retry_times: int = 1,
        update_callback: Optional[Callable] = None,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        use_cache: bool = True,
    ):
        # 首先设置所有属性
        self.api_key = api_key
        self.base_url = base_url
        self.thread_num = thread_num
        self.batch_num = batch_num
        self.model = model
        self.custom_prompt = custom_prompt
        self.temperature = temperature
        self.timeout = timeout
        self.retry_times = retry_times
        self.is_running = True
        self.update_callback = update_callback
        self.use_cache = use_cache
        
        # 记录配置信息
        logger.info(f"SubtitleOptimizer初始化: thread_num={thread_num}, batch_num={batch_num}, model={model}")
        logger.info(f"SubtitleOptimizer配置: temperature={temperature}, timeout={timeout}, retry_times={retry_times}, use_cache={use_cache}")
        
        # 强制检查批处理和线程数设置
        if self.thread_num > 1:
            logger.warning(f"线程数设置过高，可能导致进度更新不平滑。当前值: {self.thread_num}，建议值: 1")
        if self.batch_num > 1:
            logger.warning(f"批处理大小设置过高，可能导致进度更新不平滑。当前值: {self.batch_num}，建议值: 1")
        
        # 然后初始化客户端（依赖于上面的属性）
        self._init_client()
        self._init_thread_pool()
        self.cache_manager = CacheManager(str(CACHE_PATH))

    def _init_client(self):
        """初始化OpenAI客户端"""
        # 优先使用传入的参数，否则使用环境变量
        base_url = self.base_url or os.getenv("OPENAI_BASE_URL")
        api_key = self.api_key or os.getenv("OPENAI_API_KEY")

        if not api_key:
            raise ValueError("API Key 必须通过参数或环境变量 OPENAI_API_KEY 提供")

        if not base_url:
            raise ValueError("Base URL 必须通过参数或环境变量 OPENAI_BASE_URL 提供")

        self.client = OpenAI(base_url=base_url, api_key=api_key)
        logger.info(f"优化器已初始化，使用模型: {self.model}, Base URL: {base_url}")

    def _init_thread_pool(self):
        """初始化线程池"""
        logger.info(f"初始化线程池，线程数: {self.thread_num}")
        self.executor = ThreadPoolExecutor(max_workers=self.thread_num)
        # 不再使用 atexit.register，避免在进程退出时调用 stop 方法
        # 改为在对象销毁时清理资源
        import weakref
        self._finalizer = weakref.finalize(self, self._cleanup_resources, self.executor)

    def _cleanup_resources(self, executor):
        """清理资源，由 weakref finalizer 调用"""
        if executor is not None:
            try:
                executor.shutdown(wait=False)
            except Exception:
                pass  # 忽略任何错误

    def optimize_subtitle(self, subtitle_data: Union[str, ASRData]) -> ASRData:
        """优化字幕文件"""
        try:
            stage_name = "字幕优化"
            # 报告开始进度
            if self.update_callback:
                try:
                    logger.info(f"[优化器进度] 调用进度回调: 开始优化 5%")
                    # 使用统一的 (stage_name, percentage, message) 格式
                    self.update_callback(stage_name, 5, "开始优化字幕")
                except Exception as e:
                    logger.warning(f"优化器开始进度回调失败: {e}")
            else:
                logger.warning(f"[优化器进度] update_callback不可用: {self.update_callback}")
            
            # 读取字幕文件
            if isinstance(subtitle_data, str):
                asr_data = ASRData.from_subtitle_file(subtitle_data)
            else:
                asr_data = subtitle_data

            # 将ASRData转换为字典格式
            subtitle_dict = {
                str(i): seg.text for i, seg in enumerate(asr_data.segments, 1)
            }

            if self.update_callback:
                try:
                    # 使用统一的 (stage_name, percentage, message) 格式
                    self.update_callback(stage_name, 15, "数据预处理完成")
                except Exception as e:
                    logger.warning(f"优化器预处理进度回调失败: {e}")

            # 分批处理字幕
            chunks = self._split_chunks(subtitle_dict)

            if self.update_callback:
                try:
                    # 使用统一的 (stage_name, percentage, message) 格式
                    self.update_callback(stage_name, 25, f"将字幕分为{len(chunks)}批")
                except Exception as e:
                    logger.warning(f"优化器分批进度回调失败: {e}")

            # 多线程优化
            optimized_dict = self._parallel_optimize(chunks)

            if self.update_callback:
                try:
                    # 使用统一的 (stage_name, percentage, message) 格式
                    self.update_callback(stage_name, 95, "正在整理优化结果")
                except Exception as e:
                    logger.warning(f"优化器整理进度回调失败: {e}")

            # 创建新的ASRDataSeg列表
            new_segments = self._create_segments(asr_data.segments, optimized_dict)

            if self.update_callback:
                try:
                    # 使用统一的 (stage_name, percentage, message) 格式
                    self.update_callback(stage_name, 100, "优化完成")
                except Exception as e:
                    logger.warning(f"优化器完成进度回调失败: {e}")

            return ASRData(new_segments)
        except Exception as e:
            logger.error(f"优化失败：{str(e)}")
            if self.update_callback:
                try:
                    # 使用统一的 (stage_name, percentage, message) 格式
                    self.update_callback(stage_name, 0, f"优化失败: {str(e)}")
                except Exception as e2:
                    logger.warning(f"优化器错误进度回调失败: {e2}")
            raise RuntimeError(f"优化失败：{str(e)}")

    def _split_chunks(self, subtitle_dict: Dict[str, str]) -> List[Dict[str, str]]:
        """将字幕分割成块"""
        items = list(subtitle_dict.items())
        chunks = [
            dict(items[i : i + self.batch_num])
            for i in range(0, len(items), self.batch_num)
        ]
        logger.info(f"字幕分割为 {len(chunks)} 个块，每块大小: {self.batch_num}")
        return chunks

    def _parallel_optimize(self, chunks: List[Dict[str, str]]) -> Dict[str, str]:
        """并行优化所有块"""
        futures = []
        optimized_dict = {}
        total_chunks = len(chunks)
        completed_count = 0

        logger.info(f"开始并行优化 {total_chunks} 个块，线程数: {self.thread_num}")
        for chunk in chunks:
            if not self.executor:
                raise ValueError("线程池未初始化")
            future = self.executor.submit(self._safe_optimize_chunk, chunk)
            futures.append(future)

        # 进度范围：25% -> 95%，总共70%的进度范围
        progress_start = 25
        progress_range = 70

        for future in as_completed(futures):
            if not self.is_running:
                logger.info("优化器已停止运行，退出优化")
                break
            try:
                result = future.result()
                optimized_dict.update(result)
                completed_count += 1
                
                # 计算在25%-95%范围内的进度
                chunk_progress = completed_count / total_chunks
                actual_progress = progress_start + int(chunk_progress * progress_range)
                
                logger.info(f"字幕优化进度: {completed_count}/{total_chunks} 块 ({actual_progress}%)，当前块大小: {len(result)} 段")
                
                # 如果有回调函数，调用它 - 使用统一的 (stage_name, percentage, message) 格式
                if self.update_callback:
                    try:
                        # 使用统一的 (stage_name, percentage, message) 格式
                        stage_name = "字幕优化"
                        message = f"已优化 {completed_count}/{total_chunks} 批 ({len(result)} 段)"
                        self.update_callback(stage_name, actual_progress, message)
                        
                        # 记录回调调用日志
                        logger.info(f"[优化器进度] 调用进度回调: {stage_name} {actual_progress}% - {message}")
                    except Exception as e:
                        logger.warning(f"优化器进度回调执行失败: {e}")
                        
            except Exception as e:
                logger.error(f"优化块失败：{str(e)}")
                completed_count += 1
                # 对于失败的块，保留原文
                for k, v in chunk.items():
                    optimized_dict[k] = v

        logger.info(f"并行优化完成，总共处理 {completed_count}/{total_chunks} 块")
        return optimized_dict

    def _safe_optimize_chunk(self, chunk: Dict[str, str]) -> Dict[str, str]:
        """安全的优化块，包含重试逻辑"""
        for i in range(self.retry_times):
            try:
                return self._optimize_chunk(chunk)
            except Exception as e:
                if i == self.retry_times - 1:
                    raise
                logger.warning(f"优化重试 {i+1}/{self.retry_times}: {str(e)}")
        return chunk

    def _optimize_chunk(self, subtitle_chunk: Dict[str, str]) -> Dict[str, str]:
        """优化字幕块"""
        logger.info(
            f"[+]正在优化字幕：{next(iter(subtitle_chunk))} - {next(reversed(subtitle_chunk))}"
        )
        user_prompt = f"Correct the following subtitles. Keep the original language, do not translate:\n<input_subtitle>{str(subtitle_chunk)}</input_subtitle>"
        if self.custom_prompt:
            user_prompt += (
                f"\nReference content:\n<prompt>{self.custom_prompt}</prompt>"
            )

        # 检查缓存
        cache_params = {
            "temperature": self.temperature,
            "model": self.model,
        }
        # 构建缓存key
        cache_key = f"{len(OPTIMIZER_PROMPT)}_{user_prompt}"
        
        if self.use_cache:
            cache_result = self.cache_manager.get_llm_result(
                cache_key, self.model, **cache_params
            )

            if cache_result:
                logger.info("使用缓存的优化结果")
                return json.loads(cache_result)
        else:
            logger.info("跳过缓存，强制重新优化")

        # 构建提示词
        messages = [
            {"role": "system", "content": OPTIMIZER_PROMPT},
            {
                "role": "user",
                "content": user_prompt,
            },
        ]

        # 调用API优化
        response = self.client.chat.completions.create(
            model=self.model,
            messages=messages,  # type: ignore
            temperature=self.temperature,
            timeout=self.timeout,
        )

        # 解析结果
        result: Dict[str, str] = json_repair.loads(response.choices[0].message.content)  # type: ignore

        # 修复字幕对齐
        aligned_result = self._repair_subtitle(subtitle_chunk, result)

        # 保存到缓存
        if self.use_cache:
            self.cache_manager.set_llm_result(
                cache_key,
                json.dumps(aligned_result, ensure_ascii=False),
                self.model,
                **cache_params,
            )

        # 不在这里调用回调，所有进度更新都在_parallel_optimize中处理

        return aligned_result

    @staticmethod
    def _repair_subtitle(
        original: Dict[str, str], optimized: Dict[str, str]
    ) -> Dict[str, str]:
        """修复字幕对齐问题"""
        aligner = SubtitleAligner()
        original_list = list(original.values())
        optimized_list = list(optimized.values())

        aligned_source, aligned_target = aligner.align_texts(
            original_list, optimized_list
        )

        if len(aligned_source) != len(aligned_target):
            raise ValueError("对齐后字幕长度不一致")

        # 构建对齐后的字典
        start_id = next(iter(original.keys()))
        return {str(int(start_id) + i): text for i, text in enumerate(aligned_target)}

    @staticmethod
    def _create_segments(
        original_segments: List[ASRDataSeg],
        optimized_dict: Dict[str, str],
    ) -> List[ASRDataSeg]:
        """创建新的字幕段"""
        return [
            ASRDataSeg(
                text=optimized_dict.get(str(i), seg.text),
                start_time=seg.start_time,
                end_time=seg.end_time,
            )
            for i, seg in enumerate(original_segments, 1)
        ]

    def stop(self):
        """停止优化器，清理资源"""
        try:
            if hasattr(self, 'executor') and self.executor:
                self.executor.shutdown(wait=True)
                self.executor = None
            # 安全地记录日志，检查处理器是否可用
            self._safe_log("优化器已停止")
        except Exception as e:
            self._safe_log(f"停止优化器时发生错误: {e}", level="error")
        finally:
            self.executor = None

    def _safe_log(self, message, level="info"):
        """安全地记录日志，避免写入已关闭的处理器"""
        try:
            # 检查 logger 是否有有效的处理器
            if logger.handlers and any(not getattr(h, 'closed', False) for h in logger.handlers):
                if level == "error":
                    logger.error(message)
                else:
                    logger.info(message)
        except (ValueError, OSError, AttributeError):
            # 忽略任何日志相关的错误
            pass
