import hashlib
from string import Template
from typing import Callable, Dict, Optional, List, Any, Union
import os
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from abc import ABC, abstractmethod
from enum import Enum
from openai import OpenAI
import json
import requests
import re
import inspect

from subtitle.config import CACHE_PATH
from subtitle.core.asr_data import ASRData, ASRDataSeg
from subtitle.core.prompt import REFLECT_TRANSLATE_PROMPT, TRANSLATE_PROMPT, SINGLE_TRANSLATE_PROMPT
from subtitle.storage import CacheManager
from subtitle.utils import json_repair
from subtitle.utils.unified_logger import get_logger

logger = get_logger(__name__)


class TranslatorType(Enum):
    """翻译器类型"""

    OPENAI = "openai"
    DEEPLX = "deeplx"


class BaseTranslator(ABC):
    """翻译器基类"""

    def __init__(
        self,
        thread_num: int = 10,
        batch_num: int = 20,
        target_language: str = "Chinese",
        retry_times: int = 1,
        timeout: int = 60,
        update_callback: Optional[Callable] = None,
        custom_prompt: Optional[str] = None, # Retained for potential use by subclasses
        use_cache: bool = True,
    ):
        self.thread_num = thread_num
        self.batch_num = batch_num
        self.target_language = target_language
        self.retry_times = retry_times
        self.timeout = timeout
        self.is_running = True
        self.update_callback = update_callback
        self.custom_prompt = custom_prompt 
        self.use_cache = use_cache
        
        # 记录配置信息
        logger.info(f"BaseTranslator初始化: thread_num={thread_num}, batch_num={batch_num}, target_language={target_language}")
        logger.info(f"BaseTranslator配置: timeout={timeout}, retry_times={retry_times}, use_cache={use_cache}")
        
        # 强制检查批处理和线程数设置
        if self.thread_num > 1:
            logger.warning(f"线程数设置过高，可能导致进度更新不平滑。当前值: {self.thread_num}，建议值: 1")
        if self.batch_num > 1:
            logger.warning(f"批处理大小设置过高，可能导致进度更新不平滑。当前值: {self.batch_num}，建议值: 1")
            
        self._init_thread_pool()
        self.cache_manager = CacheManager(CACHE_PATH)

    def _init_thread_pool(self):
        """初始化线程池"""
        logger.info(f"初始化线程池，线程数: {self.thread_num}")
        self.executor = ThreadPoolExecutor(max_workers=self.thread_num)
        import weakref
        self._finalizer = weakref.finalize(self, self._cleanup_resources, self.executor)

    def _cleanup_resources(self, executor):
        """清理资源，由 weakref finalizer 调用"""
        if executor is not None:
            try:
                executor.shutdown(wait=False)
            except Exception:
                pass 

    def translate_subtitle(self, subtitle_data: Union[str, ASRData], trace_id: str = None) -> ASRData:
        """翻译字幕文件"""
        try:
            # 报告开始进度
            if self.update_callback:
                try:
                    if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                        # 新格式回调
                        self.update_callback(
                            "开始翻译", 
                            5, 
                            "准备翻译数据"
                        )
                    else:
                        # 旧格式回调
                        self.update_callback({"stage": "开始翻译", "percentage": 5})
                except Exception as e:
                    logger.warning(f"翻译器开始进度回调失败: {e}")
            
            if isinstance(subtitle_data, str):
                asr_data = ASRData.from_subtitle_file(subtitle_data)
            else:
                asr_data = subtitle_data

            subtitle_dict = {
                str(i): seg.text for i, seg in enumerate(asr_data.segments, 1)
            }

            if self.update_callback:
                try:
                    if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                        # 新格式回调
                        self.update_callback(
                            "数据预处理", 
                            15, 
                            f"处理 {len(subtitle_dict)} 个字幕段落"
                        )
                    else:
                        # 旧格式回调
                        self.update_callback({"stage": "数据预处理", "percentage": 15})
                except Exception as e:
                    logger.warning(f"翻译器预处理进度回调失败: {e}")

            chunks = self._split_chunks(subtitle_dict)

            if self.update_callback:
                try:
                    if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                        # 新格式回调
                        self.update_callback(
                            "分批处理", 
                            25, 
                            f"分为 {len(chunks)} 批"
                        )
                    else:
                        # 旧格式回调
                        self.update_callback({"stage": f"分为{len(chunks)}批", "percentage": 25})
                except Exception as e:
                    logger.warning(f"翻译器分批进度回调失败: {e}")

            translated_dict = self._parallel_translate(chunks, trace_id)

            if self.update_callback:
                try:
                    if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                        # 新格式回调
                        self.update_callback(
                            "整理结果", 
                            95, 
                            "合并翻译结果"
                        )
                    else:
                        # 旧格式回调
                        self.update_callback({"stage": "整理结果", "percentage": 95})
                except Exception as e:
                    logger.warning(f"翻译器整理进度回调失败: {e}")

            new_segments = self._create_segments(asr_data.segments, translated_dict)

            if self.update_callback:
                try:
                    if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                        # 新格式回调
                        self.update_callback(
                            "翻译完成", 
                            100, 
                            f"成功翻译 {len(new_segments)} 个段落"
                        )
                    else:
                        # 旧格式回调
                        self.update_callback({"stage": "翻译完成", "percentage": 100})
                except Exception as e:
                    logger.warning(f"翻译器完成进度回调失败: {e}")

            return ASRData(new_segments)
        except Exception as e:
            logger.error(f"翻译失败：{str(e)}")
            if self.update_callback:
                try:
                    if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                        # 新格式回调
                        self.update_callback(
                            "翻译失败", 
                            0, 
                            f"错误: {str(e)}"
                        )
                    else:
                        # 旧格式回调
                        self.update_callback({"stage": f"翻译失败: {str(e)}", "percentage": 0})
                except Exception as e2:
                    logger.warning(f"翻译器错误进度回调失败: {e2}")
            raise RuntimeError(f"翻译失败：{str(e)}")

    def _split_chunks(self, subtitle_dict: Dict[str, str]) -> List[Dict[str, str]]:
        """将字幕分割成块"""
        items = list(subtitle_dict.items())
        chunks = [
            dict(items[i : i + self.batch_num])
            for i in range(0, len(items), self.batch_num)
        ]
        logger.info(f"字幕分割为 {len(chunks)} 个块，每块大小: {self.batch_num}")
        return chunks

    def _parallel_translate(self, chunks: List[Dict[str, str]], trace_id: str = None) -> Dict[str, str]:
        """并行翻译所有块"""
        futures = []
        translated_dict = {}
        total_chunks = len(chunks)
        completed_count = 0
        successful_count = 0
        failed_count = 0
        segment_results = []

        logger.info(f"开始并行翻译 {total_chunks} 个块，线程数: {self.thread_num}")
        for chunk in chunks:
            future = self.executor.submit(self._safe_translate_chunk, chunk, trace_id)
            futures.append(future)

        # 进度范围：25% -> 95%，总共70%的进度范围
        progress_start = 25
        progress_range = 70

        for future in as_completed(futures):
            if not self.is_running:
                logger.info("翻译器已停止运行，退出翻译")
                break
            try:
                result = future.result()
                
                # 处理结果，更新统计信息
                if isinstance(result, dict):
                    # 处理正常结果
                    translated_dict.update(result)
                    
                    # 更新段落结果
                    for segment_id, translated_text in result.items():
                        segment_results.append({
                            "segment_id": segment_id,
                            "original_text": chunks[completed_count].get(segment_id, ""),
                            "translated_text": translated_text,
                            "status": "SUCCESS"
                        })
                    
                    successful_count += len(result)
                
                completed_count += 1
                
                # 计算在25%-95%范围内的进度
                chunk_progress = completed_count / total_chunks
                actual_progress = progress_start + int(chunk_progress * progress_range)
                
                logger.info(f"字幕翻译进度: {completed_count}/{total_chunks} 块 ({actual_progress}%)，当前块大小: {len(result) if isinstance(result, dict) else 0} 段")
                
                # 如果有回调函数，调用它
                if self.update_callback:
                    try:
                        # 使用统一的格式，如果回调支持的话
                        if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                            stage_name = "字幕翻译"
                            message = f"已翻译 {completed_count}/{total_chunks} 批 ({len(result) if isinstance(result, dict) else 0} 段)"
                            self.update_callback(stage_name, actual_progress, message)
                            logger.info(f"[翻译器进度] 调用进度回调(新格式): {stage_name} {actual_progress}% - {message}")
                        else:
                            # 旧格式
                            self.update_callback({
                                "stage": "字幕翻译",
                                "percentage": actual_progress,
                                "message": f"已翻译 {completed_count}/{total_chunks} 批",
                                "status": "IN_PROGRESS",
                                "trace_id": trace_id,
                                "data": {
                                    "completed_chunks": completed_count,
                                    "total_chunks": total_chunks,
                                    "successful_segments": successful_count,
                                    "failed_segments": failed_count,
                                    "segment_results": segment_results
                                }
                            })
                            logger.info(f"[翻译器进度] 调用进度回调(旧格式): {completed_count}/{total_chunks}")
                    except Exception as e:
                        logger.warning(f"翻译器进度回调执行失败: {e}")
                        
            except Exception as e:
                logger.error(f"翻译块失败：{str(e)}")
                failed_count += 1
                
                # 添加失败的段落结果
                for segment_id in chunks[completed_count]:
                    segment_results.append({
                        "segment_id": segment_id,
                        "original_text": chunks[completed_count].get(segment_id, ""),
                        "translated_text": "",
                        "status": "ERROR",
                        "error_detail": {
                            "error_code": "TRANSLATION_ERROR",
                            "technical_message": str(e),
                            "user_message": "翻译失败",
                            "context": {
                                "chunk_index": str(completed_count)
                            }
                        }
                    })
                
                completed_count += 1

        # 最终状态
        status = "SUCCESS"
        if failed_count > 0:
            status = "PARTIAL_SUCCESS" if successful_count > 0 else "ERROR"
        
        # 最终回调
        if self.update_callback and self.is_running:
            try:
                if callable(self.update_callback) and len(inspect.signature(self.update_callback).parameters) == 3:
                    self.update_callback(
                        "翻译完成", 
                        95, 
                        f"完成 {completed_count}/{total_chunks} 批，成功: {successful_count}，失败: {failed_count}"
                    )
                else:
                    self.update_callback({
                        "stage": "翻译完成",
                        "percentage": 95,
                        "message": f"完成 {completed_count}/{total_chunks} 批，成功: {successful_count}，失败: {failed_count}",
                        "status": status,
                        "trace_id": trace_id,
                        "data": {
                            "completed_chunks": completed_count,
                            "total_chunks": total_chunks,
                            "successful_segments": successful_count,
                            "failed_segments": failed_count,
                            "segment_results": segment_results,
                            "total_segments_processed": successful_count + failed_count
                        }
                    })
            except Exception as e:
                logger.warning(f"翻译器最终进度回调执行失败: {e}")
        
        return translated_dict

    def _safe_translate_chunk(self, chunk: Dict[str, str], trace_id: str = None) -> Dict[str, str]:
        """安全的翻译块，包含重试逻辑"""
        for i in range(self.retry_times + 1): # Corrected retry logic
            try:
                result = self._translate_chunk(chunk, trace_id)
                if self.update_callback:
                    self.update_callback(result)
                return result
            except Exception as e:
                if i == self.retry_times: # Last attempt failed
                    logger.error(f"翻译块最终失败 (尝试 {i+1} 次): {str(e)}")
                    raise # Re-raise the last exception
                logger.warning(f"翻译重试 {i+1}/{self.retry_times+1}: {str(e)}")
        return {} # Should not be reached if retry_times >= 0

    @staticmethod
    def _create_segments(
        original_segments: List[ASRDataSeg], translated_dict: Dict[str, str]
    ) -> List[ASRDataSeg]:
        """创建新的字幕段"""
        for i, seg in enumerate(original_segments, 1):
            try:
                seg.translated_text = translated_dict[str(i)] 
            except Exception as e:
                logger.error(f"创建新的字幕段失败 for segment {i}: {str(e)}")
                seg.translated_text = seg.text # Fallback to original text
        return original_segments

    @abstractmethod
    def _translate_chunk(self, subtitle_chunk: Dict[str, str], trace_id: str = None) -> Dict[str, str]:
        """翻译字幕块"""
        pass

    def stop(self):
        """停止翻译器，清理资源"""
        try:
            if hasattr(self, 'executor') and self.executor:
                self.executor.shutdown(wait=True) # Changed to wait=True for cleaner shutdown
                self.executor = None
            self._safe_log("翻译器已停止")
        except Exception as e:
            self._safe_log(f"停止翻译器时发生错误: {e}", level="error")
        finally:
            self.executor = None # Ensure it's None even if shutdown fails

    def _safe_log(self, message, level="info"):
        """安全地记录日志，避免写入已关闭的处理器"""
        try:
            if logger.handlers and any(not getattr(h, 'closed', False) for h in logger.handlers):
                if level == "error":
                    logger.error(message)
                else:
                    logger.info(message)
        except (ValueError, OSError, AttributeError):
            pass


class OpenAITranslator(BaseTranslator):
    """OpenAI翻译器"""

    def __init__(
        self,
        thread_num: int = 10,
        batch_num: int = 20,
        target_language: str = "Chinese",
        model: str = "gpt-4o-mini",
        custom_prompt: str = "",
        is_reflect: bool = False,
        temperature: float = 0.7,
        timeout: int = 60,
        retry_times: int = 1,
        update_callback: Optional[Callable] = None,
        api_key: Optional[str] = None, # New parameter
        base_url: Optional[str] = None, # New parameter
        use_cache: bool = True, # New parameter for cache control
    ):
        super().__init__(
            thread_num=thread_num,
            batch_num=batch_num,
            target_language=target_language,
            retry_times=retry_times,
            timeout=timeout,
            update_callback=update_callback,
            custom_prompt=custom_prompt, # Pass custom_prompt to BaseTranslator
            use_cache=use_cache # Pass use_cache to BaseTranslator
        )
        
        # 首先设置所有属性
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.is_reflect = is_reflect
        self.temperature = temperature
        
        # 然后初始化客户端（依赖于上面的属性）
        self._init_client() # Call after all attributes are set

    def _init_client(self):
        """初始化OpenAI客户端"""
        if not self.api_key:
            raise ValueError("OpenAI API key 必须为 OpenAITranslator 提供。")
        
        # self.base_url 可以为 None, OpenAI 客户端会处理
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)
        logger.info(f"OpenAITranslator client initialized. Model: {self.model}")
        if self.base_url:
            logger.info(f"Using custom OpenAI base URL for translator: {self.base_url}")


    def _translate_chunk(self, subtitle_chunk: Dict[str, str], trace_id: str = None) -> Dict[str, str]:
        """翻译字幕块"""
        logger.info(
            f"[+]正在翻译字幕：{next(iter(subtitle_chunk))} - {next(reversed(subtitle_chunk))}"
        )

        if self.is_reflect:
            prompt = REFLECT_TRANSLATE_PROMPT
        else:
            prompt = TRANSLATE_PROMPT
        
        # Use self.custom_prompt which was set in __init__
        prompt = Template(prompt).safe_substitute(
            target_language=self.target_language, custom_prompt=self.custom_prompt 
        )
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()

        try:
            cache_params = {
                "target_language": self.target_language,
                "is_reflect": self.is_reflect,
                "temperature": self.temperature,
                "prompt_hash": prompt_hash,
            }
            cache_key = f"{json.dumps(subtitle_chunk, ensure_ascii=False)}"
            result = {}
            if self.use_cache:
                cache_result = self.cache_manager.get_llm_result(
                    cache_key,
                    self.model,
                    **cache_params,
                )
                if cache_result:
                    result = json.loads(cache_result)
            else:
                logger.info("跳过缓存，强制重新翻译")
            
            if not result:
                response = self._call_api(
                    prompt, json.dumps(subtitle_chunk, ensure_ascii=False)
                )
                result = json_repair.loads(response.choices[0].message.content)
                if len(result) != len(subtitle_chunk):
                    logger.warning(f"翻译结果数量不匹配，将使用单条翻译模式重试")
                    return self._translate_chunk_single(subtitle_chunk)
                if self.use_cache:
                    self.cache_manager.set_llm_result(
                        cache_key,
                        json.dumps(result, ensure_ascii=False),
                        self.model,
                        **cache_params,
                    )

            if self.is_reflect:
                result = {k: f"{v['revised_translation']}" for k, v in result.items()}
            else:
                result = {k: f"{v}" for k, v in result.items()}

            return result
        except Exception as e:
            logger.warning(f"批量翻译块失败，尝试单条翻译: {str(e)}") # Changed to warning
            try:
                return self._translate_chunk_single(subtitle_chunk)
            except Exception as final_e: # Renamed to avoid confusion
                logger.error(f"单条翻译也失败：{str(final_e)}")
                # Return original text for the chunk if all fails, to avoid crashing
                # This part needs careful consideration for error propagation vs. partial success
                return {k: v for k,v in subtitle_chunk.items()}  # 返回原文，不添加错误标记


    def _translate_chunk_single(self, subtitle_chunk: Dict[str, str]) -> Dict[str, str]:
        """单条翻译模式"""
        result = {}
        single_prompt = Template(SINGLE_TRANSLATE_PROMPT).safe_substitute(
            target_language=self.target_language
        )
        prompt_hash = hashlib.md5(single_prompt.encode()).hexdigest()
        for idx, text in subtitle_chunk.items():
            try:
                cache_params = {
                    "target_language": self.target_language,
                    "is_reflect": self.is_reflect, # is_reflect might not apply to single prompt
                    "temperature": self.temperature,
                    "prompt_hash": prompt_hash,
                }
                if self.use_cache:
                    cache_result = self.cache_manager.get_llm_result(
                        f"{text}", self.model, **cache_params
                    )

                    if cache_result:
                        result[idx] = cache_result
                        continue

                response = self._call_api(single_prompt, text)
                translated_text = response.choices[0].message.content.strip()

                translated_text = re.sub(
                    r"<think>.*?</think>", "", translated_text, flags=re.DOTALL
                )
                translated_text = translated_text.strip()

                if self.use_cache:
                    self.cache_manager.set_llm_result(
                        f"{text}",
                        translated_text,
                        self.model,
                        **cache_params,
                    )
                result[idx] = translated_text
            except Exception as e:
                logger.error(f"单条翻译失败 {idx}: {str(e)}")
                result[idx] = text  # 返回原文，不添加错误标记 
        return result

    def _call_api(self, prompt: str, user_content: str) -> Any: # Changed user_content to str for single
        """调用OpenAI API"""
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": user_content},
        ]

        return self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            temperature=self.temperature,
            timeout=self.timeout,
        )

    # _parse_response is not directly used if json_repair.loads is used in _translate_chunk
    # def _parse_response(self, response: Any) -> Dict[str, str]:
    #     """解析API响应"""
    #     try:
    #         result = json_repair.loads(response.choices[0].message.content)
    #         if self.is_reflect:
    #             return {k: v["revised_translation"] for k, v in result.items()}
    #         return result
    #     except Exception as e:
    #         raise ValueError(f"解析翻译结果失败：{str(e)}")


class DeepLXTranslator(BaseTranslator):
    """DeepLX翻译器"""

    def __init__(
        self,
        thread_num: int = 10,
        batch_num: int = 20,
        target_language: str = "Chinese",
        retry_times: int = 1,
        timeout: int = 20,
        update_callback: Optional[Callable] = None,
        # New parameters for potential future use, matching OpenAI for consistency
        api_key: Optional[str] = None, # Could be used for endpoint or auth if DeepLX changes
        base_url: Optional[str] = None, # Could be used as endpoint
        use_cache: bool = True, # New parameter for cache control
    ):
        super().__init__(
            thread_num=thread_num,
            batch_num=batch_num,
            target_language=target_language,
            retry_times=retry_times,
            timeout=timeout,
            update_callback=update_callback,
            use_cache=use_cache, # Pass use_cache to BaseTranslator
        )
        self.session = requests.Session()
        # Prioritize base_url if provided by new config system, else fallback to env var
        self.endpoint = base_url if base_url else os.getenv("DEEPLX_ENDPOINT", "https://api.deeplx.org/translate")
        if not self.endpoint: # Final check if neither base_url nor env var is set
             raise ValueError("DeepLX endpoint must be provided either via configuration or DEEPLX_ENDPOINT environment variable.")
        logger.info(f"DeepLXTranslator initialized. Endpoint: {self.endpoint}")

        self.lang_map = {
            "简体中文": "zh", "繁体中文": "zh-TW", "英语": "en", "日本語": "ja", "韩语": "ko",
            "法语": "fr", "德语": "de", "西班牙语": "es", "俄语": "ru", "葡萄牙语": "pt",
            "土耳其语": "tr", "Chinese": "zh", "English": "en", "Japanese": "ja", "Korean": "ko",
            "French": "fr", "German": "de", "Spanish": "es", "Russian": "ru",
        }

    def _translate_chunk(self, subtitle_chunk: Dict[str, str], trace_id: str = None) -> Dict[str, str]:
        """翻译字幕块"""
        result = {}
        if self.target_language in self.lang_map.values():
            target_lang = self.target_language
        else:
            target_lang = self.lang_map.get(self.target_language, "zh").lower()

        for idx, text in subtitle_chunk.items():
            try:
                cache_params = {
                    "target_language": target_lang,
                    "endpoint": self.endpoint,
                }
                if self.use_cache:
                    cache_result = self.cache_manager.get_translation(
                        text, TranslatorType.DEEPLX.value, **cache_params
                    )

                    if cache_result:
                        result[idx] = cache_result
                        logger.info(f"使用缓存的DeepLX翻译结果：{idx}")
                        continue

                response = self.session.post(
                    self.endpoint,
                    json={
                        "text": text,
                        "source_lang": "auto",
                        "target_lang": target_lang,
                    },
                    timeout=self.timeout,
                )
                response.raise_for_status()
                translated_text = response.json()["data"]

                if self.use_cache:
                    self.cache_manager.set_translation(
                        text, translated_text, TranslatorType.DEEPLX.value, **cache_params
                    )
                result[idx] = translated_text
            except Exception as e:
                logger.error(f"DeepLX翻译失败 {idx}: {str(e)}")
                result[idx] = text  # 返回原文，不添加错误标记
        return result


class TranslatorFactory:
    """翻译器工厂类"""

    @staticmethod
    def create_translator(
        translator_type: TranslatorType,
        thread_num: int = 5,
        batch_num: int = 10,
        target_language: str = "Chinese",
        model: str = "gpt-4o-mini", # Default for OpenAI, ignored by DeepLX
        custom_prompt: str = "",    # OpenAI specific
        temperature: float = 0.7,   # OpenAI specific
        is_reflect: bool = False,   # OpenAI specific
        update_callback: Optional[Callable] = None,
        api_key: Optional[str] = None,    # New
        base_url: Optional[str] = None,   # New (can serve as endpoint for DeepLX)
        use_cache: bool = True,     # New: cache control
    ) -> BaseTranslator:
        """创建翻译器实例"""
        try:
            if translator_type == TranslatorType.OPENAI:
                return OpenAITranslator(
                    thread_num=thread_num,
                    batch_num=batch_num,
                    target_language=target_language,
                    model=model,
                    custom_prompt=custom_prompt,
                    is_reflect=is_reflect,
                    temperature=temperature,
                    update_callback=update_callback,
                    api_key=api_key,      
                    base_url=base_url,
                    use_cache=use_cache
                )
            elif translator_type == TranslatorType.DEEPLX:
                # DeepLX batch_num can be different, e.g. 5 as in original code
                # Pass base_url as potential endpoint, api_key might be unused or for future auth
                return DeepLXTranslator(
                    thread_num=thread_num,
                    batch_num=5, # Or pass batch_num if it should be configurable
                    target_language=target_language,
                    update_callback=update_callback,
                    api_key=api_key, # Pass along, DeepLXTranslator can ignore if not needed
                    base_url=base_url,  # This will be used as endpoint if provided
                    use_cache=use_cache
                )
            else:
                raise ValueError(f"不支持的翻译器类型：{translator_type}")
        except Exception as e:
            logger.error(f"创建翻译器失败：{str(e)}")
            raise
