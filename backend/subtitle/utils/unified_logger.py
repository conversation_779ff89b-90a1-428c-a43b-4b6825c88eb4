"""
统一日志系统 - 支持开发和生产环境

这个模块提供了项目的统一日志配置，解决日志分散和格式不一致的问题。
根据运行环境自动选择不同的日志输出目录：
- 开发环境：项目目录 backend/AppData/logs/
- 生产环境：~/Library/Application Support/electron-python-grpc-pex-demo/logs/
"""

import logging
import logging.handlers
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any, Union
import threading
import traceback

# 导入配置
try:
    from ..config import LOG_PATH, LOG_LEVEL
except ImportError:
    LOG_PATH = Path(__file__).parent.parent.parent / "AppData" / "logs"
    LOG_LEVEL = logging.INFO


def _detect_environment():
    """检测当前运行环境"""
    import sys
    import os
    
    # 检测是否为PEX环境（生产环境）
    if hasattr(sys, '_MEIPASS') or 'pex' in sys.argv[0].lower():
        return 'production'
    
    # 检测是否为开发环境
    if 'electron-app' in os.getcwd() or 'backend' in os.getcwd():
        return 'development'
    
    # 检测环境变量（npm run dev时设置）
    if os.getenv('NODE_ENV') == 'development' or os.getenv('VITE_DEV_SERVER_URL'):
        return 'development'
    
    # 默认为开发环境
    return 'development'


def _should_enable_console():
    """根据环境决定是否启用控制台输出"""
    env = _detect_environment()
    
    # 开发环境启用控制台输出，生产环境禁用
    return env == 'development'


def _get_log_directory():
    """根据环境获取日志目录"""
    env = _detect_environment()
    
    if env == 'production':
        # 生产环境：使用系统应用支持目录
        app_support = Path.home() / "Library" / "Application Support" / "electron-python-grpc-pex-demo"
        log_dir = app_support / "logs"
    else:
        # 开发环境：使用项目目录
        try:
            from ..config import LOG_PATH
            log_dir = Path(LOG_PATH)
        except ImportError:
            # 回退到项目目录
            project_root = Path(__file__).parent.parent.parent
            log_dir = project_root / "AppData" / "logs"
    
    return log_dir


class UnifiedFormatter(logging.Formatter):
    """统一的日志格式化器，支持不同输出目标的格式优化"""
    
    def __init__(self, format_type: str = "detailed", include_extra: bool = True):
        """
        Args:
            format_type: 格式类型 ("simple", "detailed", "json", "colored")
            include_extra: 是否包含额外字段
        """
        super().__init__()
        self.format_type = format_type
        self.include_extra = include_extra
        
        # 颜色定义
        self.colors = {
            'DEBUG': '\033[36m',     # 青色
            'INFO': '\033[32m',      # 绿色
            'WARNING': '\033[33m',   # 黄色
            'ERROR': '\033[31m',     # 红色
            'CRITICAL': '\033[35m',  # 紫色
            'RESET': '\033[0m'       # 重置
        }
    
    def format(self, record: logging.LogRecord) -> str:
        """根据格式类型格式化日志记录"""
        if self.format_type == "json":
            return self._format_json(record)
        elif self.format_type == "colored":
            return self._format_colored(record)
        elif self.format_type == "simple":
            return self._format_simple(record)
        else:  # detailed
            return self._format_detailed(record)
    
    def _format_simple(self, record: logging.LogRecord) -> str:
        """简单格式：时间 - 级别 - 消息"""
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        message = record.getMessage()
        
        # 添加常见的额外信息
        extras = []
        if hasattr(record, 'operation'):
            extras.append(f"操作:{record.operation}")
        if hasattr(record, 'duration'):
            extras.append(f"耗时:{record.duration:.2f}s")
        if hasattr(record, 'error_code'):
            extras.append(f"错误码:{record.error_code}")
        
        extra_str = f" [{','.join(extras)}]" if extras else ""
        
        result = f"{timestamp} - {record.levelname} - {message}{extra_str}"
        
        if record.exc_info:
            result += f"\n异常: {record.exc_info[1]}"
        
        return result
    
    def _format_detailed(self, record: logging.LogRecord) -> str:
        """详细格式：完整的时间戳和模块信息"""
        timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        module_info = f"{record.name}"
        if hasattr(record, 'funcName') and record.funcName != '<module>':
            module_info += f".{record.funcName}:{record.lineno}"
        
        message = record.getMessage()
        
        # 构建基础消息
        result = f"{timestamp} - {module_info} - {record.levelname} - {message}"
        
        # 添加额外信息
        if self.include_extra:
            extras = []
            for attr in ['operation', 'duration', 'error_code', 'user_id', 'request_id']:
                if hasattr(record, attr):
                    value = getattr(record, attr)
                    if attr == 'duration':
                        extras.append(f"{attr}:{value:.2f}s")
                    else:
                        extras.append(f"{attr}:{value}")
            
            if extras:
                result += f" | {' | '.join(extras)}"
        
        # 添加异常信息
        if record.exc_info:
            result += f"\n异常详情:\n{''.join(traceback.format_exception(*record.exc_info))}"
        
        return result
    
    def _format_colored(self, record: logging.LogRecord) -> str:
        """彩色格式：适用于控制台输出"""
        color = self.colors.get(record.levelname, self.colors['RESET'])
        reset = self.colors['RESET']
        
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # 构建彩色消息
        result = f"{color}[{record.levelname}]{reset} {timestamp} {record.name} - {record.getMessage()}"
        
        # 添加额外信息
        if hasattr(record, 'operation'):
            result += f" | {color}操作:{reset} {record.operation}"
        if hasattr(record, 'duration'):
            result += f" | {color}耗时:{reset} {record.duration:.2f}s"
        if hasattr(record, 'error_code'):
            result += f" | {color}错误码:{reset} {record.error_code}"
        
        # 添加异常信息
        if record.exc_info:
            result += f"\n{color}异常:{reset} {record.exc_info[1]}"
        
        return result
    
    def _format_json(self, record: logging.LogRecord) -> str:
        """JSON格式：结构化日志，便于分析"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread_id": record.thread,
            "thread_name": record.threadName,
            "process_id": record.process
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外字段
        if self.include_extra:
            extra_fields = {}
            standard_fields = {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info'
            }
            
            for key, value in record.__dict__.items():
                if key not in standard_fields:
                    try:
                        json.dumps(value)  # 测试是否可JSON序列化
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
            
            if extra_fields:
                log_data["extra"] = extra_fields
        
        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))


class UnifiedLoggerManager:
    """统一日志管理器，单例模式"""
    
    _instance = None
    _lock = threading.RLock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: list = []
        self._setup_complete = False
        
        # 默认配置
        self.log_level = LOG_LEVEL
        self.log_dir = _get_log_directory()  # 根据环境自动选择
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.backup_count = 5
        self.enable_console = _should_enable_console()  # 根据环境自动决定
        self.enable_file = True
        self.environment = _detect_environment()
    
    def setup(
        self,
        log_level: Union[int, str] = None,
        log_dir: Optional[Path] = None,
        enable_console: bool = None,  # 改为None，表示未指定
        enable_file: bool = True,
        max_file_size: int = 10 * 1024 * 1024,
        backup_count: int = 5,
        force_reinit: bool = False,
        **kwargs  # 添加kwargs参数
    ):
        """设置统一日志配置"""
        with self._lock:
            if self._setup_complete and not force_reinit:
                return
            
            # 更新配置
            if log_level is not None:
                if isinstance(log_level, str):
                    self.log_level = getattr(logging, log_level.upper())
                else:
                    self.log_level = log_level
            
            if log_dir is not None:
                self.log_dir = Path(log_dir)
            else:
                # 如果没有指定日志目录，重新检测环境
                self.log_dir = _get_log_directory()
                self.environment = _detect_environment()
            
            # 如果没有明确指定控制台输出，根据环境自动决定
            if enable_console is None:
                self.enable_console = _should_enable_console()
            else:
                self.enable_console = enable_console
            self.enable_file = enable_file
            self.max_file_size = max_file_size
            self.backup_count = backup_count
            
            # 创建日志目录
            self.log_dir.mkdir(parents=True, exist_ok=True)
            
            # 在首次初始化时记录环境信息
            env_logger = logging.getLogger('unified_logger')
            env_logger.info(f"日志系统初始化 | 环境:{self.environment} | 日志目录:{self.log_dir}")
            
            # 配置根日志器
            self._setup_root_logger()
            
            # 配置第三方库日志级别
            self._configure_third_party_loggers()
            
            self._setup_complete = True
    
    def _setup_root_logger(self):
        """配置根日志器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        self.handlers.clear()
        
        # 控制台处理器
        if self.enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.log_level)
            console_handler.setFormatter(UnifiedFormatter("colored"))
            root_logger.addHandler(console_handler)
            self.handlers.append(console_handler)
        
        # 文件处理器
        if self.enable_file:
            # 主应用日志
            app_log_file = self.log_dir / "subtitle_app.log"
            app_handler = logging.handlers.RotatingFileHandler(
                app_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            app_handler.setLevel(self.log_level)
            app_handler.setFormatter(UnifiedFormatter("detailed"))
            root_logger.addHandler(app_handler)
            self.handlers.append(app_handler)
            
            # 错误专用日志
            error_log_file = self.log_dir / "subtitle_error.log"
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(UnifiedFormatter("detailed"))
            root_logger.addHandler(error_handler)
            self.handlers.append(error_handler)
            
            # JSON格式日志（用于日志分析）
            json_log_file = self.log_dir / "subtitle_structured.log"
            json_handler = logging.handlers.RotatingFileHandler(
                json_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            json_handler.setLevel(self.log_level)
            json_handler.setFormatter(UnifiedFormatter("json"))
            root_logger.addHandler(json_handler)
            self.handlers.append(json_handler)
    
    def _configure_third_party_loggers(self):
        """配置第三方库的日志级别"""
        third_party_loggers = [
            "urllib3", "requests", "openai", "httpx", "httpcore",
            "ssl", "certifi", "grpc", "asyncio", "concurrent.futures"
        ]
        
        for logger_name in third_party_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.WARNING)  # 只显示警告和错误
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取或创建指定名称的日志器"""
        if not self._setup_complete:
            self.setup()
        
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def log_operation(
        self,
        logger_name: str,
        operation: str,
        level: str = "INFO",
        duration: Optional[float] = None,
        success: bool = True,
        error_code: Optional[str] = None,
        **kwargs
    ):
        """记录操作日志"""
        logger = self.get_logger(logger_name)
        
        # 构建消息
        if success:
            message = f"操作完成: {operation}"
        else:
            message = f"操作失败: {operation}"
        
        # 创建日志记录
        extra = {
            'operation': operation,
            'success': success,
            **kwargs
        }
        
        if duration is not None:
            extra['duration'] = duration
        
        if error_code is not None:
            extra['error_code'] = error_code
        
        # 记录日志
        log_level = getattr(logging, level.upper())
        logger.log(log_level, message, extra=extra)
    
    def log_error(
        self,
        logger_name: str,
        error: Exception,
        operation: Optional[str] = None,
        **kwargs
    ):
        """记录错误日志"""
        logger = self.get_logger(logger_name)
        
        message = f"发生错误"
        if operation:
            message += f" (操作: {operation})"
        
        message += f": {str(error)}"
        
        extra = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            **kwargs
        }
        
        if operation:
            extra['operation'] = operation
        
        logger.error(message, exc_info=True, extra=extra)
    
    def cleanup(self):
        """清理资源"""
        with self._lock:
            for handler in self.handlers:
                handler.close()
            self.handlers.clear()
            self.loggers.clear()
            self._setup_complete = False


# 全局实例
_logger_manager = UnifiedLoggerManager()


def setup_unified_logging(**kwargs):
    """设置统一日志系统的便捷函数"""
    _logger_manager.setup(**kwargs)


def get_logger(name: str = None) -> logging.Logger:
    """获取日志器的便捷函数"""
    if name is None:
        # 自动获取调用者的模块名
        import inspect
        frame = inspect.currentframe()
        try:
            caller_frame = frame.f_back
            module_name = caller_frame.f_globals.get('__name__', 'unknown')
            name = module_name
        finally:
            del frame
    
    return _logger_manager.get_logger(name)


def log_operation(operation: str, logger_name: str = None, **kwargs):
    """记录操作日志的便捷函数"""
    if logger_name is None:
        import inspect
        frame = inspect.currentframe()
        try:
            caller_frame = frame.f_back
            logger_name = caller_frame.f_globals.get('__name__', 'unknown')
        finally:
            del frame
    
    _logger_manager.log_operation(logger_name, operation, **kwargs)


def log_error(error: Exception, operation: str = None, logger_name: str = None, **kwargs):
    """记录错误日志的便捷函数"""
    if logger_name is None:
        import inspect
        frame = inspect.currentframe()
        try:
            caller_frame = frame.f_back
            logger_name = caller_frame.f_globals.get('__name__', 'unknown')
        finally:
            del frame
    
    _logger_manager.log_error(logger_name, error, operation, **kwargs)


# 确保在模块导入时进行基础设置
setup_unified_logging() 