"""
重试管理器模块。

提供重试机制、操作中断支持和性能指标收集功能。
"""
import time
import threading
from typing import Callable, Any, Dict, Optional, List
from dataclasses import dataclass, field
from enum import Enum
import uuid
from concurrent.futures import ThreadPoolExecutor, Future
import traceback
from subtitle.utils.unified_logger import get_logger

logger = get_logger(__name__)

class RetryStrategy(Enum):
    """重试策略枚举"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    FIXED_DELAY = "fixed_delay"
    IMMEDIATE = "immediate"

@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0  # 基础延迟时间（秒）
    max_delay: float = 60.0  # 最大延迟时间（秒）
    backoff_multiplier: float = 2.0  # 指数退避乘数
    exceptions_to_retry: tuple = (Exception,)  # 需要重试的异常类型
    exceptions_to_ignore: tuple = ()  # 不重试的异常类型

@dataclass
class OperationMetrics:
    """操作性能指标"""
    operation_id: str
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    attempts: int = 0
    success: bool = False
    error_message: str = ""
    total_duration: float = 0.0
    retry_delays: List[float] = field(default_factory=list)

class InterruptibleOperation:
    """可中断操作的上下文管理器"""
    
    def __init__(self, operation_id: str = None):
        self.operation_id = operation_id or str(uuid.uuid4())
        self._interrupted = threading.Event()
        self._started = False
        self._finished = False
        
    def interrupt(self):
        """中断操作"""
        logger.info(f"中断操作: {self.operation_id}")
        self._interrupted.set()
        
    def is_interrupted(self) -> bool:
        """检查是否已被中断"""
        return self._interrupted.is_set()
        
    def check_interruption(self):
        """检查中断状态，如果已中断则抛出异常"""
        if self.is_interrupted():
            raise InterruptedException(f"操作 {self.operation_id} 已被中断")
            
    def __enter__(self):
        self._started = True
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self._finished = True

class InterruptedException(Exception):
    """操作中断异常"""
    pass

class RetryManager:
    """重试管理器"""
    
    def __init__(self):
        self._operations: Dict[str, InterruptibleOperation] = {}
        self._metrics: Dict[str, OperationMetrics] = {}
        self._executor = ThreadPoolExecutor(max_workers=4)
        
    def create_operation(self, operation_name: str = "unknown") -> InterruptibleOperation:
        """创建可中断操作"""
        operation = InterruptibleOperation()
        self._operations[operation.operation_id] = operation
        
        # 初始化指标
        self._metrics[operation.operation_id] = OperationMetrics(
            operation_id=operation.operation_id,
            operation_name=operation_name,
            start_time=time.time()
        )
        
        return operation
        
    def interrupt_operation(self, operation_id: str):
        """中断指定操作"""
        if operation_id in self._operations:
            self._operations[operation_id].interrupt()
            logger.info(f"已发送中断信号给操作: {operation_id}")
        else:
            logger.warning(f"未找到操作: {operation_id}")
            
    def get_operation_metrics(self, operation_id: str) -> Optional[OperationMetrics]:
        """获取操作指标"""
        return self._metrics.get(operation_id)
        
    def get_all_metrics(self) -> Dict[str, OperationMetrics]:
        """获取所有操作指标"""
        return self._metrics.copy()
        
    def cleanup_operation(self, operation_id: str):
        """清理操作记录"""
        self._operations.pop(operation_id, None)
        # 保留指标用于分析，不删除
        
    def retry_with_config(
        self,
        func: Callable,
        config: RetryConfig,
        operation: InterruptibleOperation = None,
        *args,
        **kwargs
    ) -> Any:
        """
        使用指定配置执行重试操作
        
        Args:
            func: 要执行的函数
            config: 重试配置
            operation: 可中断操作实例
            *args, **kwargs: 传递给func的参数
            
        Returns:
            函数执行结果
            
        Raises:
            最后一次尝试的异常，或InterruptedException
        """
        if operation:
            metrics = self._metrics.get(operation.operation_id)
        else:
            metrics = None
            
        last_exception = None
        
        for attempt in range(1, config.max_attempts + 1):
            try:
                # 检查中断
                if operation:
                    operation.check_interruption()
                    
                if metrics:
                    metrics.attempts = attempt
                    
                logger.info(f"尝试执行操作 (第 {attempt}/{config.max_attempts} 次)")
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 成功
                if metrics:
                    metrics.success = True
                    metrics.end_time = time.time()
                    metrics.total_duration = metrics.end_time - metrics.start_time
                    
                logger.info(f"操作成功完成 (第 {attempt} 次尝试)")
                return result
                
            except config.exceptions_to_ignore:
                # 不重试的异常，直接抛出
                raise
                
            except config.exceptions_to_retry as e:
                last_exception = e
                
                if metrics:
                    metrics.error_message = str(e)
                    
                logger.warning(f"操作失败 (第 {attempt}/{config.max_attempts} 次): {e}")
                
                # 如果是最后一次尝试，不再重试
                if attempt >= config.max_attempts:
                    break
                    
                # 计算延迟时间
                delay = self._calculate_delay(config, attempt)
                
                if metrics:
                    metrics.retry_delays.append(delay)
                    
                logger.info(f"等待 {delay:.2f} 秒后重试...")
                
                # 在延迟期间检查中断
                if operation:
                    end_time = time.time() + delay
                    while time.time() < end_time:
                        operation.check_interruption()
                        time.sleep(min(0.1, end_time - time.time()))
                else:
                    time.sleep(delay)
                    
        # 所有重试都失败了
        if metrics:
            metrics.success = False
            metrics.end_time = time.time()
            metrics.total_duration = metrics.end_time - metrics.start_time
            
        logger.error(f"操作失败，已达到最大重试次数 ({config.max_attempts})")
        raise last_exception
        
    def _calculate_delay(self, config: RetryConfig, attempt: int) -> float:
        """计算延迟时间"""
        if config.strategy == RetryStrategy.IMMEDIATE:
            return 0.0
        elif config.strategy == RetryStrategy.FIXED_DELAY:
            return config.base_delay
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay * (config.backoff_multiplier ** (attempt - 1))
            return min(delay, config.max_delay)
        else:
            return config.base_delay
            
    def async_retry_with_config(
        self,
        func: Callable,
        config: RetryConfig,
        operation: InterruptibleOperation = None,
        *args,
        **kwargs
    ) -> Future:
        """
        异步执行重试操作
        
        Returns:
            Future对象，可用于获取结果或取消操作
        """
        return self._executor.submit(
            self.retry_with_config,
            func,
            config,
            operation,
            *args,
            **kwargs
        )
        
    def shutdown(self):
        """关闭重试管理器"""
        logger.info("正在关闭重试管理器...")
        
        # 中断所有正在进行的操作
        for operation in self._operations.values():
            operation.interrupt()
            
        # 关闭线程池
        self._executor.shutdown(wait=True)
        
        logger.info("重试管理器已关闭")

# 全局重试管理器实例
retry_manager = RetryManager()

def with_retry(
    max_attempts: int = 3,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
    base_delay: float = 1.0,
    exceptions_to_retry: tuple = (Exception,)
):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        strategy: 重试策略
        base_delay: 基础延迟时间
        exceptions_to_retry: 需要重试的异常类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            config = RetryConfig(
                max_attempts=max_attempts,
                strategy=strategy,
                base_delay=base_delay,
                exceptions_to_retry=exceptions_to_retry
            )
            return retry_manager.retry_with_config(func, config, None, *args, **kwargs)
        return wrapper
    return decorator 