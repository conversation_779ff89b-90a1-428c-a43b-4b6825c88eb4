"""
进度字典标准化工厂

提供统一的进度字典创建方法，确保所有进度更新的格式一致。
"""

from typing import Dict, Any, Optional, Union
from subtitle.constants import (
    OperationStatus, 
    ErrorCode, 
    ProgressKeys, 
    DefaultValues
)


def create_progress_dict(
    stage_name: str,
    percentage: int = DefaultValues.DEFAULT_PERCENTAGE,
    message: str = DefaultValues.DEFAULT_MESSAGE,
    trace_id: Optional[str] = None,
    status: Union[OperationStatus, str] = OperationStatus.IN_PROGRESS,
    is_error: bool = False,
    error_message: str = "",
    error_code: Optional[Union[ErrorCode, str]] = None,
    error_detail: Optional[Dict[str, Any]] = None,
    data: Optional[Dict[str, Any]] = None,
    final_result: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    创建标准化的进度字典
    
    Args:
        stage_name: 处理阶段名称
        percentage: 完成百分比 (0-100)
        message: 进度消息
        trace_id: 跟踪ID
        status: 操作状态 (枚举或字符串)
        is_error: 是否为错误状态 (向后兼容)
        error_message: 错误消息 (向后兼容)
        error_code: 错误代码
        error_detail: 详细错误信息
        data: 附加数据
        final_result: 最终结果
        
    Returns:
        标准化的进度字典
    """
    # 处理状态值
    if isinstance(status, OperationStatus):
        status_value = status.value
    else:
        status_value = str(status)
    
    # 构建基础进度字典
    progress_dict = {
        ProgressKeys.STAGE_NAME: stage_name,
        ProgressKeys.PERCENTAGE: max(0, min(100, percentage)),  # 确保在0-100范围内
        ProgressKeys.MESSAGE: message,
        ProgressKeys.STATUS: status_value,
        ProgressKeys.IS_ERROR: is_error,
        ProgressKeys.ERROR_MESSAGE: error_message
    }
    
    # 添加可选字段
    if trace_id is not None:
        progress_dict[ProgressKeys.TRACE_ID] = trace_id
    
    if error_detail:
        # 确保错误详情包含错误代码
        if error_code:
            if isinstance(error_code, ErrorCode):
                error_detail["error_code"] = error_code.value
            else:
                error_detail["error_code"] = str(error_code)
        progress_dict[ProgressKeys.ERROR_DETAIL] = error_detail
    
    if data:
        progress_dict[ProgressKeys.DATA] = data
    
    if final_result:
        progress_dict[ProgressKeys.FINAL_RESULT] = final_result
    
    return progress_dict


def create_success_progress(
    stage_name: str,
    message: str = "操作成功完成",
    trace_id: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None,
    final_result: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """创建成功状态的进度字典"""
    return create_progress_dict(
        stage_name=stage_name,
        percentage=100,
        message=message,
        trace_id=trace_id,
        status=OperationStatus.SUCCESS,
        data=data,
        final_result=final_result
    )


def create_error_progress(
    stage_name: str,
    error_message: str,
    trace_id: Optional[str] = None,
    error_code: Optional[Union[ErrorCode, str]] = ErrorCode.UNEXPECTED_ERROR,
    error_detail: Optional[Dict[str, Any]] = None,
    percentage: int = 0
) -> Dict[str, Any]:
    """创建错误状态的进度字典"""
    if not error_detail:
        error_detail = {
            "technical_message": error_message,
            "user_message": f"{stage_name}过程中发生错误",
            "context": {}
        }
    
    return create_progress_dict(
        stage_name=stage_name,
        percentage=percentage,
        message=f"错误: {error_message}",
        trace_id=trace_id,
        status=OperationStatus.ERROR,
        is_error=True,
        error_message=error_message,
        error_code=error_code,
        error_detail=error_detail
    )


def create_in_progress_update(
    stage_name: str,
    percentage: int,
    message: str,
    trace_id: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """创建进行中状态的进度字典"""
    return create_progress_dict(
        stage_name=stage_name,
        percentage=percentage,
        message=message,
        trace_id=trace_id,
        status=OperationStatus.IN_PROGRESS,
        data=data
    )


def create_file_not_found_error(
    stage_name: str,
    file_path: str,
    trace_id: Optional[str] = None
) -> Dict[str, Any]:
    """创建文件未找到错误的进度字典"""
    return create_error_progress(
        stage_name=stage_name,
        error_message=f"文件不存在: {file_path}",
        trace_id=trace_id,
        error_code=ErrorCode.FILE_NOT_FOUND,
        error_detail={
            "error_code": ErrorCode.FILE_NOT_FOUND.value,
            "technical_message": f"文件路径不存在: {file_path}",
            "user_message": "找不到指定的文件",
            "context": {"file_path": file_path}
        }
    )


def create_validation_error(
    stage_name: str,
    validation_message: str,
    trace_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """创建验证错误的进度字典"""
    return create_error_progress(
        stage_name=stage_name,
        error_message=validation_message,
        trace_id=trace_id,
        error_code=ErrorCode.VALIDATION_ERROR,
        error_detail={
            "error_code": ErrorCode.VALIDATION_ERROR.value,
            "technical_message": validation_message,
            "user_message": "输入验证失败",
            "context": context or {}
        }
    )


# 向后兼容的函数 (逐步迁移用)
def _create_progress(
    stage_name: str,
    percentage: int = 0,
    message: str = "",
    is_error: bool = False,
    error_message: str = "",
    trace_id: Optional[str] = None,
    status: Optional[str] = None,
    error_detail: Optional[Dict[str, Any]] = None,
    data: Optional[Dict[str, Any]] = None,
    final_result_payload: Optional[Dict[str, Any]] = None,
    result_field_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    向后兼容的进度创建函数
    
    Note: 这个函数是为了兼容现有代码，新代码应该使用 create_progress_dict
    """
    # 处理最终结果
    final_result = None
    if final_result_payload and result_field_name:
        final_result = {result_field_name: final_result_payload}
    
    # 自动推断状态
    if not status:
        if is_error:
            status = OperationStatus.ERROR.value
        elif percentage >= 100:
            status = OperationStatus.SUCCESS.value
        else:
            status = OperationStatus.IN_PROGRESS.value
    
    return create_progress_dict(
        stage_name=stage_name,
        percentage=percentage,
        message=message,
        trace_id=trace_id,
        status=status,
        is_error=is_error,
        error_message=error_message,
        error_detail=error_detail,
        data=data,
        final_result=final_result
    ) 