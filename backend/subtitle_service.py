# -*- coding: utf-8 -*-
"""
字幕服务实现模块。

该模块定义了 SubtitleServiceImpl 类，它实现了 gRPC 服务 SubtitlerServicer 中定义的所有 RPC 方法。
这些方法处理与字幕生成相关的各种任务，包括：
- 从视频中提取音频 (VideoToAudio)
- 将音频转换为文本 (AudioToText)
- 根据文本生成字幕 (GenerateSubtitles)
- 翻译字幕内容 (TranslateSubtitles)
- 处理从视频到翻译字幕的完整流程 (ProcessVideoToTranslatedSubtitles)
- 保存单个字幕文件 (SaveSubtitle)
- 批量保存字幕文件 (BatchSaveSubtitle)

服务依赖于 TranscriptThread 来执行核心的字幕处理逻辑，并通过 ProgressUpdate 消息流式返回处理进度和结果。
"""
import traceback # 用于捕获和格式化异常信息
import logging
import grpc
from pathlib import Path # 用于路径操作
import uuid
import json

from api_protos.v1.subtitler import subtitler_pb2
from api_protos.v1.subtitler import subtitler_pb2_grpc
from subtitle.workflow.transcriptThread import TranscriptThread
from subtitle_processor import SubtitleProcessor

# 导入新的错误处理系统
from subtitle.exceptions import (
    SubtitleProcessingError,
    FileProcessingError,
    APIError,
    ConfigurationError,
    create_file_error,
    create_api_error
)
from subtitle.error_handler import (
    handle_grpc_errors,
    handle_subtitle_errors,
    create_progress_error,
    error_reporter
)

# 导入标准化常量和工厂
from subtitle.constants import (
    OperationStatus, ErrorCode, StageNames, LogMessages,
    DefaultValues, ResponseFieldNames, ProgressKeys
)
from subtitle.utils.progress_factory import create_error_progress

# 获取当前模块的 logger 实例
logger = logging.getLogger(__name__)

def _convert_progress_dict_to_pb(progress_dict: dict) -> subtitler_pb2.ProgressUpdate:
    """
    将 TranscriptThread 返回的进度字典转换为 ProgressUpdate protobuf 消息。

    Args:
        progress_dict (dict): 包含进度信息的字典，通常包含以下键：
            - "stage_name" (str): 当前处理阶段的名称。
            - "percentage" (float): 当前阶段的完成百分比。
            - "message" (str): 描述当前进度的消息。
            - "status" (str): 操作状态 ("SUCCESS", "ERROR", "PARTIAL_SUCCESS", "IN_PROGRESS")
            - "trace_id" (str): 全链路追踪ID
            - "error_detail" (dict): 结构化错误信息
            - "data" (dict): 与回调相关的具体数据负载
            - "is_error" (bool): 是否发生错误（兼容旧版本）。
            - "error_message" (str): 错误信息（兼容旧版本）。
            - "final_result" (dict, optional): 包含最终结果的字典，其结构取决于具体的处理阶段。
                例如：{"video_to_audio_response": {"audio_path": "..."}}

    Returns:
        subtitler_pb2.ProgressUpdate: 转换后的 protobuf 消息。
    """
    # 创建基本的ProgressUpdate消息
    progress_update_msg = subtitler_pb2.ProgressUpdate(
        stage_name=progress_dict.get(ProgressKeys.STAGE_NAME, "未知阶段"),
        percentage=progress_dict.get(ProgressKeys.PERCENTAGE, DefaultValues.DEFAULT_PERCENTAGE),
        message=progress_dict.get(ProgressKeys.MESSAGE, DefaultValues.DEFAULT_MESSAGE),
    )
    
    # 添加trace_id
    if ProgressKeys.TRACE_ID in progress_dict:
        trace_id_value = progress_dict[ProgressKeys.TRACE_ID]
        progress_update_msg.trace_id = trace_id_value if trace_id_value is not None else DefaultValues.DEFAULT_TRACE_ID
    
    # 设置状态 - 使用枚举值映射
    status_str = progress_dict.get(ProgressKeys.STATUS)
    if status_str:
        if status_str == OperationStatus.SUCCESS.value:
            progress_update_msg.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_SUCCESS
        elif status_str == OperationStatus.ERROR.value:
            progress_update_msg.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_ERROR
        elif status_str == OperationStatus.PARTIAL_SUCCESS.value:
            progress_update_msg.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_PARTIAL_SUCCESS
        elif status_str == OperationStatus.IN_PROGRESS.value:
            progress_update_msg.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_IN_PROGRESS
        else:
            progress_update_msg.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_UNSPECIFIED
    
    # 兼容旧版本的错误标志
    progress_update_msg.is_error = progress_dict.get(ProgressKeys.IS_ERROR, False)
    progress_update_msg.error_message = progress_dict.get(ProgressKeys.ERROR_MESSAGE, "")
    
    # 处理结构化错误信息
    if ProgressKeys.ERROR_DETAIL in progress_dict and progress_dict[ProgressKeys.ERROR_DETAIL]:
        error_detail = progress_dict[ProgressKeys.ERROR_DETAIL]
        error_detail_pb = subtitler_pb2.ErrorDetail(
            error_code=error_detail.get("error_code", ""),
            technical_message=error_detail.get("technical_message", ""),
            user_message=error_detail.get("user_message", "")
        )
        # 添加上下文信息
        if "context" in error_detail and isinstance(error_detail["context"], dict):
            for key, value in error_detail["context"].items():
                if isinstance(value, str):
                    error_detail_pb.context[key] = value
                else:
                    error_detail_pb.context[key] = str(value)
        progress_update_msg.error_detail.CopyFrom(error_detail_pb)
    
    # 处理数据负载
    if ProgressKeys.DATA in progress_dict and progress_dict[ProgressKeys.DATA]:
        try:
            from google.protobuf.json_format import ParseDict
            from google.protobuf.struct_pb2 import Struct
            
            # 将字典转换为Struct
            data_struct = Struct()
            ParseDict(progress_dict[ProgressKeys.DATA], data_struct)
            
            # 将Struct打包到Any
            progress_update_msg.data.Pack(data_struct)
        except Exception as e:
            logger.error(f"将数据负载转换为protobuf Any时出错: {e}")

    # 如果进度字典中包含最终结果，则根据结果类型填充相应的 protobuf 消息字段
    if ProgressKeys.FINAL_RESULT in progress_dict and progress_dict[ProgressKeys.FINAL_RESULT]:
        # final_result 是一个单键字典，键是响应类型，值是响应负载
        field_name, payload_dict = next(iter(progress_dict[ProgressKeys.FINAL_RESULT].items()))

        if field_name == "video_to_audio_response":
            response_pb = subtitler_pb2.VideoToAudioResponse(**payload_dict)
            if "trace_id" in progress_dict:
                trace_id_value = progress_dict["trace_id"]
                response_pb.trace_id = trace_id_value if trace_id_value is not None else DefaultValues.DEFAULT_TRACE_ID
            progress_update_msg.video_to_audio_response.CopyFrom(response_pb)
        elif field_name == "audio_to_text_response":
            # payload_dict 预期结构: {"segments": [{"text": "...", "start_time_ms": ..., "end_time_ms": ...}, ...]}
            # 或者直接是 ASRData.to_json() 返回的字典结构。
            pb_segments = []
            asr_segments_data = payload_dict.get("segments")

            if isinstance(asr_segments_data, list): # 处理分段字典列表
                for seg_data in asr_segments_data:
                    segment_pb = subtitler_pb2.TimestampedTextSegment(
                        text=seg_data.get("text", ""),
                        start_time_ms=seg_data.get("start_time", 0), # 确保键存在，使用 get 获取
                        end_time_ms=seg_data.get("end_time", 0)    # 确保键存在，使用 get 获取
                    )
                    # 如果有状态信息，设置状态
                    if "status" in seg_data:
                        status_str = seg_data["status"]
                        if status_str == "SUCCESS":
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_SUCCESS
                        elif status_str == "ERROR":
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_ERROR
                        elif status_str == "PARTIAL_SUCCESS":
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_PARTIAL_SUCCESS
                        else:
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_UNSPECIFIED
                    
                    # 如果有错误详情，设置错误详情
                    if "error_detail" in seg_data and seg_data["error_detail"]:
                        error_detail = seg_data["error_detail"]
                        error_detail_pb = subtitler_pb2.ErrorDetail(
                            error_code=error_detail.get("error_code", ""),
                            technical_message=error_detail.get("technical_message", ""),
                            user_message=error_detail.get("user_message", "")
                        )
                        # 添加上下文信息
                        if "context" in error_detail and isinstance(error_detail["context"], dict):
                            for key, value in error_detail["context"].items():
                                if isinstance(value, str):
                                    error_detail_pb.context[key] = value
                                else:
                                    error_detail_pb.context[key] = str(value)
                        segment_pb.error_detail.CopyFrom(error_detail_pb)
                    
                    pb_segments.append(segment_pb)
            elif isinstance(asr_segments_data, dict): # 处理类似 ASRData.to_json() 的结构
                 for _key, seg_data in sorted(asr_segments_data.items(), key=lambda item: int(item[0])): # 按序号排序
                    segment_pb = subtitler_pb2.TimestampedTextSegment(
                        text=seg_data.get("original_subtitle", ""), # ASRData.to_json() 中的字段名
                        start_time_ms=seg_data.get("start_time", 0),
                        end_time_ms=seg_data.get("end_time", 0)
                    )
                    # 如果有状态信息，设置状态
                    if "status" in seg_data:
                        status_str = seg_data["status"]
                        if status_str == "SUCCESS":
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_SUCCESS
                        elif status_str == "ERROR":
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_ERROR
                        elif status_str == "PARTIAL_SUCCESS":
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_PARTIAL_SUCCESS
                        else:
                            segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_UNSPECIFIED
                    
                    # 如果有错误详情，设置错误详情
                    if "error_detail" in seg_data and seg_data["error_detail"]:
                        error_detail = seg_data["error_detail"]
                        error_detail_pb = subtitler_pb2.ErrorDetail(
                            error_code=error_detail.get("error_code", ""),
                            technical_message=error_detail.get("technical_message", ""),
                            user_message=error_detail.get("user_message", "")
                        )
                        # 添加上下文信息
                        if "context" in error_detail and isinstance(error_detail["context"], dict):
                            for key, value in error_detail["context"].items():
                                if isinstance(value, str):
                                    error_detail_pb.context[key] = value
                                else:
                                    error_detail_pb.context[key] = str(value)
                        segment_pb.error_detail.CopyFrom(error_detail_pb)
                    
                    pb_segments.append(segment_pb)

            response_pb = subtitler_pb2.AudioToTextResponse(segments=pb_segments)
            # 添加其他字段
            if "trace_id" in progress_dict:
                trace_id_value = progress_dict["trace_id"]
                response_pb.trace_id = trace_id_value if trace_id_value is not None else DefaultValues.DEFAULT_TRACE_ID
            if "total_segments_processed" in payload_dict:
                response_pb.total_segments_processed = payload_dict["total_segments_processed"]
            if "successful_segments" in payload_dict:
                response_pb.successful_segments = payload_dict["successful_segments"]
            if "failed_segments" in payload_dict:
                response_pb.failed_segments = payload_dict["failed_segments"]
            
            progress_update_msg.audio_to_text_response.CopyFrom(response_pb)
        elif field_name == "generate_subtitles_response":
            response_pb = subtitler_pb2.GenerateSubtitlesResponse(**payload_dict)
            if "trace_id" in progress_dict:
                trace_id_value = progress_dict["trace_id"]
                response_pb.trace_id = trace_id_value if trace_id_value is not None else DefaultValues.DEFAULT_TRACE_ID
            progress_update_msg.generate_subtitles_response.CopyFrom(response_pb)
        elif field_name == "translate_subtitles_response":
            # 处理翻译响应，包括段落级别的结果
            translated_content = payload_dict.get("translated_subtitle_content", "")
            segment_results = payload_dict.get("segment_results", [])
            
            response_pb = subtitler_pb2.TranslateSubtitlesResponse(
                translated_subtitle_content=translated_content
            )
            
            if "trace_id" in progress_dict:
                trace_id_value = progress_dict["trace_id"]
                response_pb.trace_id = trace_id_value if trace_id_value is not None else DefaultValues.DEFAULT_TRACE_ID
            if "total_segments_processed" in payload_dict:
                response_pb.total_segments_processed = payload_dict["total_segments_processed"]
            if "successful_segments" in payload_dict:
                response_pb.successful_segments = payload_dict["successful_segments"]
            if "failed_segments" in payload_dict:
                response_pb.failed_segments = payload_dict["failed_segments"]
                
            # 处理段落级别的结果
            if segment_results:
                for seg_result in segment_results:
                    segment_pb = subtitler_pb2.TranslatedSegmentResult(
                        segment_id=seg_result.get("segment_id", ""),
                        original_text=seg_result.get("original_text", ""),
                        translated_text=seg_result.get("translated_text", "")
                    )
                    
                    # 设置状态
                    status_str = seg_result.get("status")
                    if status_str == "SUCCESS":
                        segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_SUCCESS
                    elif status_str == "ERROR":
                        segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_ERROR
                    elif status_str == "PARTIAL_SUCCESS":
                        segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_PARTIAL_SUCCESS
                    else:
                        segment_pb.status = subtitler_pb2.OperationStatus.OPERATION_STATUS_UNSPECIFIED
                    
                    # 设置错误详情
                    if "error_detail" in seg_result and seg_result["error_detail"]:
                        error_detail = seg_result["error_detail"]
                        error_detail_pb = subtitler_pb2.ErrorDetail(
                            error_code=error_detail.get("error_code", ""),
                            technical_message=error_detail.get("technical_message", ""),
                            user_message=error_detail.get("user_message", "")
                        )
                        # 添加上下文信息
                        if "context" in error_detail and isinstance(error_detail["context"], dict):
                            for key, value in error_detail["context"].items():
                                if isinstance(value, str):
                                    error_detail_pb.context[key] = value
                                else:
                                    error_detail_pb.context[key] = str(value)
                        segment_pb.error_detail.CopyFrom(error_detail_pb)
                    
                    response_pb.segment_results.append(segment_pb)
            
            progress_update_msg.translate_subtitles_response.CopyFrom(response_pb)
        elif field_name == "process_video_to_translated_subtitles_response":
            response_pb = subtitler_pb2.ProcessVideoToTranslatedSubtitlesResponse(**payload_dict)
            if "trace_id" in progress_dict:
                trace_id_value = progress_dict["trace_id"]
                response_pb.trace_id = trace_id_value if trace_id_value is not None else DefaultValues.DEFAULT_TRACE_ID
            progress_update_msg.process_video_to_translated_subtitles_response.CopyFrom(response_pb)
        # 如果 TranscriptThread 添加了更多响应类型，在此处添加处理逻辑

    return progress_update_msg

class SubtitleServiceImpl(subtitler_pb2_grpc.SubtitlerServicer):
    """
    字幕服务 gRPC 实现类。

    实现了 SubtitlerServicer 接口中定义的所有方法，用于处理字幕相关的请求。
    """
    def __init__(self):
        """
        初始化 SubtitleServiceImpl。
        创建一个 SubtitleProcessor 实例用于处理字幕保存等逻辑。
        """
        self.processor = SubtitleProcessor(logger_instance=logger) # 使用全局 logger

    def _handle_streaming_request(self, request_handler_func, error_stage_name: str, trace_id: str = None):
        """
        通用的流式请求处理逻辑。

        Args:
            request_handler_func (Callable): 一个生成器函数，用于处理具体的请求逻辑并 yield 进度字典。
            error_stage_name (str): 当服务层发生意外错误时，用于 ProgressUpdate 的阶段名称。
            trace_id (str, optional): 请求的跟踪ID，用于关联请求和响应。

        Yields:
            subtitler_pb2.ProgressUpdate: 进度更新消息。
        """
        try:
            for progress_dict in request_handler_func():
                yield _convert_progress_dict_to_pb(progress_dict)
                if progress_dict.get(ProgressKeys.IS_ERROR):
                    logger.warning(f"处理流式请求时在阶段 '{progress_dict.get(ProgressKeys.STAGE_NAME)}' 遇到错误: {progress_dict.get(ProgressKeys.ERROR_MESSAGE)}")
                    return # 发生错误时停止流式传输
        except Exception as e:
            error_message = f"{error_stage_name} 服务层发生意外错误: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_message)
            
            # 使用标准化错误创建
            error_progress = create_error_progress(
                stage_name=error_stage_name,
                error_message=str(e),
                trace_id=trace_id,
                error_code=ErrorCode.SERVICE_ERROR,
                error_detail={
                    "error_code": ErrorCode.SERVICE_ERROR.value,
                    "technical_message": str(e),
                    "user_message": f"{error_stage_name}过程中发生服务错误",
                    "context": {"exception_type": type(e).__name__}
                }
            )
            
            yield _convert_progress_dict_to_pb(error_progress)

    def VideoToAudio(self, request: subtitler_pb2.VideoToAudioRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：从视频文件中提取音频。

        通过 TranscriptThread 执行实际的音频提取，并流式返回进度更新。

        Args:
            request (subtitler_pb2.VideoToAudioRequest): 包含视频路径的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含提取进度或结果的 protobuf 消息。
        """
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="VideoToAudio"))
        logger.info(f"视频路径: {request.video_path}")
        transcript_thread = TranscriptThread()

        def handler():
            for progress_dict in transcript_thread.extract_audio_from_video(request.video_path):
                is_final_success = (progress_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE) 
                                  and not progress_dict.get(ProgressKeys.IS_ERROR))
                if is_final_success:
                    final_path = progress_dict.get(ProgressKeys.FINAL_RESULT, {}).get(ResponseFieldNames.VIDEO_TO_AUDIO_RESPONSE, {}).get("audio_path")
                    logger.info(LogMessages.SERVICE_RESPONSE_SUCCESS.format(service_name="VideoToAudio"))
                    logger.info(f"音频路径: {final_path}")
                elif progress_dict.get(ProgressKeys.IS_ERROR):
                    logger.warning(LogMessages.SERVICE_PROCESSING_ERROR.format(
                        service_name="VideoToAudio", 
                        error=progress_dict.get(ProgressKeys.ERROR_MESSAGE)
                    ))
                else:
                    # 将关键进度信息改为info级别，便于观察
                    stage = progress_dict.get(ProgressKeys.STAGE_NAME, "未知阶段")
                    percentage = progress_dict.get(ProgressKeys.PERCENTAGE, 0)
                    message = progress_dict.get(ProgressKeys.MESSAGE, "")
                    logger.info(f"[VideoToAudio] {stage}: {percentage}% - {message}")
                yield progress_dict

        yield from self._handle_streaming_request(handler, StageNames.EXTRACT_AUDIO)


    def AudioToText(self, request: subtitler_pb2.AudioToTextRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：将音频转换为文本。

        可以接受音频文件路径或直接的音频数据。通过 TranscriptThread 执行实际的语音识别，
        并流式返回进度更新和识别结果。

        Args:
            request (subtitler_pb2.AudioToTextRequest): 包含音频路径或数据以及是否请求词级时间戳的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含转录进度或结果的 protobuf 消息。
        """
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="AudioToText"))
        logger.info(f"音频路径: '{request.audio_path}', 音频数据: {bool(request.audio_data)}, 词级时间戳: {request.request_word_timestamps}")
        transcript_thread = TranscriptThread()

        def handler():
            for progress_dict in transcript_thread.transcribe_audio(
                audio_path=request.audio_path,
                audio_data=request.audio_data,
                request_word_timestamps=request.request_word_timestamps,
                skip_cache=getattr(request, 'skip_cache', False)
            ):
                # 将关键进度信息改为info级别，便于观察
                stage = progress_dict.get(ProgressKeys.STAGE_NAME, "未知阶段")
                percentage = progress_dict.get(ProgressKeys.PERCENTAGE, 0)
                message = progress_dict.get(ProgressKeys.MESSAGE, "")
                logger.info(f"[AudioToText] {stage}: {percentage}% - {message}")
                yield progress_dict
        
        yield from self._handle_streaming_request(handler, StageNames.TRANSCRIBE_AUDIO)

    def GenerateSubtitles(self, request: subtitler_pb2.GenerateSubtitlesRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：根据文本内容生成字幕文件（通常是 SRT 格式）。

        Args:
            request (subtitler_pb2.GenerateSubtitlesRequest): 包含文本内容和可选音频路径的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含字幕生成进度或结果的 protobuf 消息。
        """
        # 生成或获取 trace_id
        trace_id = getattr(request, 'trace_id', None)
        if not trace_id:
            trace_id = str(uuid.uuid4())
            
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="GenerateSubtitles"))
        logger.info(f"文本长度: {len(request.text)}, 音频路径: '{request.audio_path}', trace_id: {trace_id}")
        transcript_thread = TranscriptThread()
        
        def handler():
            # TranscriptThread.generate_subtitles_from_text 需要文本，以及可选的 audio_path
            # 如果文本为 None 且路径已由先前调用设置（例如在完整流程中），它将从 self.raw_srt_output_path 加载
            # 对于直接的 GenerateSubtitles RPC，我们传递 request.text 和 request.audio_path。
            for progress_dict in transcript_thread.generate_subtitles_from_text(
                text=request.text, 
                audio_path=request.audio_path,
                skip_cache=getattr(request, 'skip_cache', False),
                trace_id=trace_id
            ):
                logger.debug(f"GenerateSubtitles 进度: {progress_dict}")
                yield progress_dict

        yield from self._handle_streaming_request(handler, StageNames.GENERATE_SUBTITLES, trace_id=trace_id)

    def TranslateSubtitles(self, request: subtitler_pb2.TranslateSubtitlesRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：翻译字幕内容。

        Args:
            request (subtitler_pb2.TranslateSubtitlesRequest): 包含待翻译的字幕内容（SRT字符串）、目标语言。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含翻译进度或结果的 protobuf 消息。
        """
        # 生成或获取 trace_id
        trace_id = getattr(request, 'trace_id', None)
        if not trace_id:
            trace_id = str(uuid.uuid4())
            
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="TranslateSubtitles"))
        logger.info(f"目标语言: '{request.target_language}', 字幕内容长度: {len(request.subtitle_content)}, trace_id: {trace_id}")
        transcript_thread = TranscriptThread()

        def handler():
            # 目前假设 subtitle_content 是 SRT 字符串。可以增强 TranscriptThread 以支持其他格式。
            for progress_dict in transcript_thread.translate_subtitle_content(
                subtitle_content=request.subtitle_content,
                target_language=request.target_language,
                input_format="srt", # 如果支持更多格式，可以从请求中确定
                skip_cache=getattr(request, 'skip_cache', False),
                trace_id=trace_id
            ):
                logger.debug(f"TranslateSubtitles 进度: {progress_dict}")
                yield progress_dict
        
        yield from self._handle_streaming_request(handler, StageNames.TRANSLATE_SUBTITLES, trace_id=trace_id)

    def ProcessVideoToTranslatedSubtitles(self, request: subtitler_pb2.ProcessVideoToTranslatedSubtitlesRequest, context) -> subtitler_pb2.ProgressUpdate:
        """
        gRPC 方法：处理从视频到翻译字幕的完整流程。

        这是一个复合操作，依次执行视频提取音频、音频转文字、生成字幕、翻译字幕等步骤。
        TranscriptThread.process_video_to_translated_subtitles 负责处理子步骤的进度报告。

        Args:
            request (subtitler_pb2.ProcessVideoToTranslatedSubtitlesRequest): 包含视频路径和目标翻译语言的请求。
            context: gRPC 上下文对象。

        Yields:
            subtitler_pb2.ProgressUpdate: 包含整个流程中各个阶段进度或最终结果的 protobuf 消息。
        """
        # 生成或获取 trace_id
        trace_id = getattr(request, 'trace_id', None)
        if not trace_id:
            trace_id = str(uuid.uuid4())
            
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="ProcessVideoToTranslatedSubtitles"))
        logger.info(f"视频路径: '{request.video_path}', 目标语言: '{request.target_language}', trace_id: {trace_id}")
        transcript_thread = TranscriptThread()

        def handler():
            for progress_dict in transcript_thread.process_video_to_translated_subtitles(
                video_path_str=request.video_path,
                target_language=request.target_language,
                trace_id=trace_id
                # request_word_timestamps 字段在此请求中不存在，已移除
            ):
                logger.debug(f"ProcessVideoToTranslatedSubtitles 进度: {progress_dict}")
                yield progress_dict

        yield from self._handle_streaming_request(handler, StageNames.PROCESS_VIDEO, trace_id=trace_id)

    @handle_grpc_errors
    def SaveSubtitle(self, request: subtitler_pb2.SaveSubtitleRequest, context) -> subtitler_pb2.SaveSubtitleResponse:
        """
        gRPC 方法：保存单个字幕文件。

        根据请求的格式、布局等参数，使用 SubtitleProcessor 处理并保存字幕。

        Args:
            request (subtitler_pb2.SaveSubtitleRequest): 包含字幕分段、文件名、目标格式、布局等信息的请求。
            context: gRPC 上下文对象。

        Returns:
            subtitler_pb2.SaveSubtitleResponse: 包含保存结果（如文件路径、数据等）的 protobuf 消息。
        """
        logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="SaveSubtitle"))
        logger.info(f"格式: {request.format}, 布局: {request.layout}, 文件名: {request.file_name}")

        # 验证请求参数
        if not request.file_name:
            raise create_file_error("文件名不能为空", "", "保存")

        if not request.format:
            raise ConfigurationError("字幕格式不能为空", config_key="format")

        if not request.segments:
            raise FileProcessingError("字幕分段数据为空", details={"segments_count": 0})

        try:
            ass_options_py = None
            # 检查 ass_style_options 是否设置且有字段
            if request.HasField('ass_style_options') and request.ass_style_options.ListFields():
                ass_options_py = {field.name: value for field, value in request.ass_style_options.ListFields()}

            package = self.processor.process_save_subtitle(
                segments_proto_list=list(request.segments),
                original_filename_or_title=request.file_name,
                target_format=request.format,
                target_layout=request.layout,
                auto_save_to_default=request.auto_save_to_default,
                ass_style_options=ass_options_py
            )

            if package:
                logger.info(LogMessages.SERVICE_RESPONSE_SUCCESS.format(service_name="SaveSubtitle"))
                logger.info(f"文件路径: {package.get('file_path')}")
                return subtitler_pb2.SaveSubtitleResponse(
                    file_path=package.get('file_path', ""),
                    file_data=package.get('file_data', b""),
                    file_name=package.get('file_name', ""),
                    file_size=package.get('file_size', 0),
                    saved_to_default=package.get('saved_to_default', False),
                    format=package.get('format', ""),
                    layout=package.get('layout', ""),
                    original_filename_or_title=package.get('original_filename_or_title', "")
                )
            else:
                error = FileProcessingError(
                    message="SubtitleProcessor 未能处理并保存字幕",
                    details={
                        "file_name": request.file_name,
                        "format": request.format,
                        "layout": request.layout
                    },
                    user_message="字幕保存失败，请检查文件格式和权限"
                )
                error_reporter.report_error(error, {"operation": "SaveSubtitle"})
                raise error

        except SubtitleProcessingError:
            # 重新抛出已知的字幕处理错误
            raise
        except Exception as e:
            # 转换未知错误
            error = FileProcessingError(
                message=f"保存字幕时发生未知错误: {str(e)}",
                details={
                    "file_name": request.file_name,
                    "format": request.format,
                    "original_exception": type(e).__name__
                },
                user_message="字幕保存过程中发生错误，请重试"
            )
            error_reporter.report_error(error, {"operation": "SaveSubtitle"})
            raise error


    def BatchSaveSubtitle(self, request: subtitler_pb2.BatchSaveSubtitleRequest, context) -> subtitler_pb2.BatchSaveSubtitleResponse:
        """
        gRPC 方法：批量保存字幕文件。

        根据请求的格式列表、布局列表等参数，为每个组合生成并保存字幕文件。

        Args:
            request (subtitler_pb2.BatchSaveSubtitleRequest): 包含字幕分段、文件名前缀、目标格式列表、
                                                            布局列表等信息的请求。
            context: gRPC 上下文对象。

        Returns:
            subtitler_pb2.BatchSaveSubtitleResponse: 包含多个 SaveSubtitleResponse 消息的列表。
        """
        try:
            logger.info(LogMessages.SERVICE_REQUEST_RECEIVED.format(service_name="BatchSaveSubtitle"))
            logger.info(f"文件名前缀: {request.file_name_prefix}, 格式数量: {len(request.formats)}, 布局数量: {len(request.layouts)}")

            ass_options_py = None
            if request.HasField('ass_style_options') and request.ass_style_options.ListFields():
                 ass_options_py = {field.name: value for field, value in request.ass_style_options.ListFields()}

            packages = self.processor.process_batch_save_subtitles(
                segments_proto_list=list(request.segments),
                original_filename_or_title=request.file_name_prefix, # 使用 proto 定义的 file_name_prefix
                formats_to_generate=list(request.formats),
                layouts_to_generate=list(request.layouts),
                translation_requested=request.translation_requested,
                auto_save_to_default=request.auto_save_to_default,
                ass_style_options=ass_options_py
            )

            response_files = []
            for package in packages:
                if package: # 确保 package 不是 None
                    response_files.append(subtitler_pb2.SaveSubtitleResponse(
                        file_path=package.get('file_path', ""),
                        file_data=package.get('file_data', b""),
                        file_name=package.get('file_name', ""),
                        file_size=package.get('file_size', 0),
                        saved_to_default=package.get('saved_to_default', False),
                        format=package.get('format', ""),
                        layout=package.get('layout', ""),
                        original_filename_or_title=package.get('original_filename_or_title', "")
                    ))
            
            logger.info(LogMessages.SERVICE_RESPONSE_SUCCESS.format(service_name="BatchSaveSubtitle"))
            logger.info(f"成功生成 {len(response_files)} 个文件")
            return subtitler_pb2.BatchSaveSubtitleResponse(files=response_files)

        except Exception as e:
            error_message = f"批量保存字幕时发生内部错误: {str(e)}"
            logger.error(f"BatchSaveSubtitle 服务层错误: {error_message}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(error_message)
            return subtitler_pb2.BatchSaveSubtitleResponse()

    def ClearCache(self, request: subtitler_pb2.ClearCacheRequest, context) -> subtitler_pb2.ClearCacheResponse:
        """
        gRPC 方法：清除所有AI模型缓存

        Args:
            request (subtitler_pb2.ClearCacheRequest): 清除缓存请求
            context: gRPC 上下文对象

        Returns:
            subtitler_pb2.ClearCacheResponse: 清除结果响应
        """
        logger.info("收到清除缓存请求")
        
        try:
            from subtitle.storage.cache_manager import CacheManager
            from subtitle.config import CACHE_PATH
            
            # 获取缓存管理器实例
            cache_manager = CacheManager(str(CACHE_PATH))
            
            # 清理所有缓存
            cache_manager.cleanup_old_cache()
            
            # 如果需要，还可以清除其他缓存
            # 例如：LLM结果缓存、翻译缓存等
            
            logger.info("所有AI模型缓存已清除")
            
            return subtitler_pb2.ClearCacheResponse(
                success=True,
                message="所有AI模型缓存已清除"
            )
            
        except Exception as e:
            error_message = f"清除缓存失败: {str(e)}"
            logger.error(error_message)
            
            return subtitler_pb2.ClearCacheResponse(
                success=False,
                message=error_message
            )
