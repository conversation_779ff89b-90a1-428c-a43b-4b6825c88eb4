#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度回调调试脚本

用于测试各个组件的进度回调是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from subtitle.core.split import SubtitleSplitter
from subtitle.core.optimize import SubtitleOptimizer
from subtitle.core.asr_data import ASRData, ASRDataSeg
from subtitle.utils.unified_logger import get_logger

logger = get_logger(__name__)

def test_subtitle_splitter_progress():
    """测试SubtitleSplitter的进度回调"""
    print("=" * 50)
    print("测试 SubtitleSplitter 进度回调")
    print("=" * 50)
    
    progress_calls = []
    
    def test_progress_callback(stage_name, percentage, message):
        progress_calls.append((stage_name, percentage, message))
        print(f"[测试回调] {stage_name}: {percentage}% - {message}")
    
    # 创建测试数据
    test_segments = [
        ASRDataSeg("这是一个测试句子。", 0, 1000),
        ASRDataSeg("这是另一个测试句子。", 1000, 2000),
        ASRDataSeg("最后一个测试句子。", 2000, 3000),
    ]
    test_asr_data = ASRData(test_segments)
    
    try:
        # 创建分割器实例
        splitter = SubtitleSplitter(
            thread_num=1,
            model="gpt-4o-mini",
            api_key="test_key",
            base_url="http://test.example.com",
            progress_callback=test_progress_callback,
            use_cache=False
        )
        
        print(f"分割器创建成功，progress_callback属性: {hasattr(splitter, 'progress_callback')}")
        print(f"progress_callback值: {splitter.progress_callback}")
        
        # 直接测试进度回调
        print("\n直接测试进度回调:")
        if hasattr(splitter, 'progress_callback') and splitter.progress_callback:
            splitter.progress_callback("测试阶段", 50, "测试消息")
        else:
            print("进度回调不可用")
        
        print(f"\n收到的进度回调: {progress_calls}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_subtitle_optimizer_progress():
    """测试SubtitleOptimizer的进度回调"""
    print("=" * 50)
    print("测试 SubtitleOptimizer 进度回调")
    print("=" * 50)
    
    progress_calls = []
    
    def test_update_callback(result_dict):
        progress_calls.append(result_dict)
        print(f"[测试回调] 收到优化器回调: {result_dict}")
    
    try:
        # 创建优化器实例
        optimizer = SubtitleOptimizer(
            thread_num=1,
            model="gpt-4o-mini",
            api_key="test_key",
            base_url="http://test.example.com",
            update_callback=test_update_callback,
            use_cache=False
        )
        
        print(f"优化器创建成功，update_callback属性: {hasattr(optimizer, 'update_callback')}")
        print(f"update_callback值: {optimizer.update_callback}")
        
        # 直接测试进度回调
        print("\n直接测试进度回调:")
        if hasattr(optimizer, 'update_callback') and optimizer.update_callback:
            optimizer.update_callback({"test": "data"})
        else:
            print("进度回调不可用")
        
        print(f"\n收到的进度回调: {progress_calls}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_simple_progress_creation():
    """测试_create_progress函数"""
    print("=" * 50)
    print("测试 _create_progress 函数")
    print("=" * 50)
    
    try:
        from subtitle.workflow.progress_manager import _create_progress
        
        test_progress = _create_progress("测试阶段", 75, "测试消息")
        print(f"创建的进度字典: {test_progress}")
        
        # 验证字典结构
        expected_keys = ["stage_name", "percentage", "message", "is_error", "error_message"]
        for key in expected_keys:
            if key in test_progress:
                print(f"  ✓ {key}: {test_progress[key]}")
            else:
                print(f"  ✗ 缺少键: {key}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始进度回调调试测试...\n")
    
    test_simple_progress_creation()
    test_subtitle_splitter_progress()
    test_subtitle_optimizer_progress()
    
    print("\n测试完成!") 