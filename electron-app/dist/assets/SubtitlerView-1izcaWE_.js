const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-5pfIJIcs.js","./index-D-BfnIXl.css"])))=>i.map(i=>d[i]);
import{_ as Je,d as Xe,s as Re,a as re,r as L,c as x,w as ge,n as fe,o as he,b as i,e as n,f as e,t as a,F as Y,g as ee,h as c,i as H,j as be,k as ue,l as q,v as Qe,m as Ee,p as Z,q as _e,u as te,x as Ne,y as ke,z as ae,A as le,B as ve,C as Ye,D as Ze,E as we,G as Fe,K as et}from"./index-5pfIJIcs.js";console.log("subtitler/extractors.js loaded");const ie={IDLE:"IDLE",PENDING:"PENDING",RUNNING:"RUNNING",PAUSED:"PAUSED",COMPLETED:"COMPLETED",ERROR:"ERROR",CANCELED:"CANCELED",TIMEOUT:"TIMEOUT"},tt={uploadedFile:null,currentStep:1,isLoading:!1,progressUpdates:[],currentProcessState:{traceId:null,workflowType:null,currentStageName:null,overallPercentage:0,currentMessage:"",status:null,errorDetail:null,isActive:!1,finalDataPayload:null},usePreviousAudioForTranscription:!0,useTranscriptionForEditing:!0,videoToAudioResult:null,videoToAudioProgress:"",videoToAudioError:null,audioToTextResult:null,audioToTextProgress:"",audioToTextError:null,generatedSubtitles:null,generateSubtitlesProgress:"",optimizedSubtitles:null,optimizeSubtitlesProgress:"",editableSegments:null,selectedSegmentId:null,editedSubtitlesSaved:!1,translationSettings:{targetLanguage:"en",quality:"balanced",style:"formal",customPrompt:""},translatedSubtitles:null,translationProgress:"",translationProgressPercent:0,exportFilename:"",exportFormat:"srt",exportLayout:"原文在上",exportLayouts:["原文在上"],exportContentSource:"editable_segments",exportContentSources:["editable_segments"],exportAutoSaveToDefault:!1,lastExportPath:null,exportResults:[],oneClickOperationInProgress:!1,oneClickOperationError:null,oneClickWorkflowType:"vid_to_srt_trans",oneClickTargetLanguage:"zh-CN",oneClickExportFormat:"srt",oneClickExportLayout:"原文在上",oneClickExportLayouts:["原文在上"],activeResultTab:"transcript"},st={isReadyForNextStep:s=>{switch(s.currentStep){case 1:return s.uploadedFile!==null;case 2:return s.videoToAudioResult!==null;case 3:return s.audioToTextResult!==null;case 4:return s.generatedSubtitles!==null&&s.generatedSubtitles.length>0;case 5:return s.optimizedSubtitles!==null||s.generatedSubtitles!==null;case 6:return s.editableSegments!==null&&s.editableSegments.length>0;case 7:return!0;case 8:return s.translatedSubtitles!==null;case 9:return!0;default:return!1}},getUploadedFileName:s=>s.uploadedFile?s.uploadedFile.name:"",getUploadedFileSize:s=>s.uploadedFile?s.uploadedFile.size:"",getUploadedFileType:s=>s.uploadedFile?s.uploadedFile.type:"",hasEditableSegments:s=>s.editableSegments&&s.editableSegments.length>0,selectedSegment:s=>!s.selectedSegmentId||!s.editableSegments?null:s.editableSegments.find(t=>t.id===s.selectedSegmentId)||null,getSegmentById:s=>t=>s.editableSegments&&s.editableSegments.find(o=>o.id===t)||null},ot={setActiveResultTab(s){this.activeResultTab=s},setUploadedFile(s){s?(this.uploadedFile={name:s.name,path:s.path,type:s.type,size:s.size},this.currentStep=1,this.progressUpdates=[],this.videoToAudioResult=null,this.audioToTextResult=null,this.audioToTextProgress="",this.audioToTextError=null,this.editableSegments=null,this.selectedSegmentId=null,this.editedSubtitlesSaved=!1):this.uploadedFile=null},setCurrentStep(s){typeof s=="number"&&s>0?this.currentStep=s:console.warn(`Invalid step number: ${s}`)},resetWorkflow(){this.uploadedFile=null,this.currentStep=1,this.isLoading=!1,this.progressUpdates=[],this.videoToAudioResult=null,this.audioToTextResult=null,this.audioToTextProgress="",this.audioToTextError=null,this.generatedSubtitles=null,this.generateSubtitlesProgress="",this.optimizedSubtitles=null,this.optimizeSubtitlesProgress="",this.editableSegments=null,this.selectedSegmentId=null,this.editedSubtitlesSaved=!1,this.translationSettings={targetLanguage:"en",quality:"balanced",style:"formal",customPrompt:""},this.translatedSubtitles=null,this.translationProgress="",this.translationProgressPercent=0,this.usePreviousAudioForTranscription=!0,this.useTranscriptionForEditing=!0,this.exportFilename="",this.exportFormat="srt",this.exportLayout="原文在上",this.exportContentSource="editable_segments",this.exportAutoSaveToDefault=!1,this.lastExportPath=null,console.log("Subtitler workflow reset.")},setUsePreviousAudioForTranscription(s){this.usePreviousAudioForTranscription=!!s,s||(this.audioToTextResult=null,this.editableSegments=null)},setUseTranscriptionForEditing(s){this.useTranscriptionForEditing=!!s,s&&this.audioToTextResult?this.initializeEditableSegments():s||(this.editableSegments=null)},setSelectedSegmentId(s){this.selectedSegmentId=s},updateSegmentText({id:s,newText:t}){if(!this.editableSegments)return;const o=this.editableSegments.find(l=>l.id===s);o&&(o.text=t),this.editedSubtitlesSaved=!1},updateSegmentTime({id:s,newStartTimeMs:t,newEndTimeMs:o}){if(!this.editableSegments)return;const l=this.editableSegments.find(r=>r.id===s);l&&(typeof t=="number"&&t>=0&&(l.startTimeMs=t),typeof o=="number"&&o>=0&&o>=l.startTimeMs&&(l.endTimeMs=o)),this.editedSubtitlesSaved=!1},addSegment({afterId:s=null,newSegmentData:t={startTimeMs:0,endTimeMs:0,text:"New Segment"}}){this.editableSegments||(this.editableSegments=[]);const o=`segment-${Date.now()}-${this.editableSegments.length}`,l={...t,id:o};if(s===null)this.editableSegments.push(l);else{const r=this.editableSegments.findIndex(u=>u.id===s);r!==-1?this.editableSegments.splice(r+1,0,l):(this.editableSegments.push(l),console.warn(`addSegment: afterId ${s} not found. Segment added to end.`))}this.selectedSegmentId=o,this.editedSubtitlesSaved=!1},deleteSegment(s){this.editableSegments&&(this.editableSegments=this.editableSegments.filter(t=>t.id!==s),this.selectedSegmentId===s&&(this.selectedSegmentId=null),this.editedSubtitlesSaved=!1)},setEditableSegments(s){this.editableSegments=s}},rt={videoToAudioResult:null,videoToAudioProgress:"",videoToAudioError:null,audioToTextResult:null,audioToTextProgress:"",audioToTextError:null,generatedSubtitles:null,generateSubtitlesProgress:"",optimizedSubtitles:null,optimizeSubtitlesProgress:""},nt={async processVideoToAudio(){if(!this.uploadedFile||!this.uploadedFile.path){this.videoToAudioError="No file uploaded or file path is missing.",console.error(this.videoToAudioError);return}this.isLoading=!0,this.videoToAudioError=null,this.videoToAudioProgress="Starting video to audio conversion...",this.progressUpdates=[this.videoToAudioProgress];try{return await this._handleWorkflowStream("videoToAudio",{video_path:this.uploadedFile.path},{stageName:"VideoToAudio",onStart:()=>{this.videoToAudioProgress="Starting video to audio conversion..."},onProgress:t=>{this.videoToAudioProgress=t.message||`${t.percentage}%`},onSuccess:t=>{this.videoToAudioProgress="Video to audio conversion completed."},onError:t=>{this.videoToAudioError=t.message||"Video to audio conversion failed.",this.videoToAudioProgress="Error: "+this.videoToAudioError}})}catch(s){throw this.videoToAudioError=s.message||"Video to audio conversion failed.",console.error("[processVideoToAudio] Error:",s),s}finally{this.isLoading=!1}},async processAudioToText(s={}){let t=s.audioPath;if(!t&&this.usePreviousAudioForTranscription&&this.videoToAudioResult&&(typeof this.videoToAudioResult=="string"?t=this.videoToAudioResult:this.videoToAudioResult&&this.videoToAudioResult.audio_path&&(t=this.videoToAudioResult.audio_path)),!t&&this.uploadedFile&&this.uploadedFile.path&&(this.uploadedFile.type.startsWith("audio/")||this.uploadedFile.name.match(/\.(mp3|wav|ogg|m4a|aac|flac)$/i))&&(t=this.uploadedFile.path),!t){this.audioToTextError="No audio path available for transcription.",console.error(this.audioToTextError);return}this.isLoading=!0,this.audioToTextError=null,this.audioToTextProgress="Starting audio transcription...";try{const o={audio_path:t,request_word_timestamps:s.requestWordTimestamps!==!1,skip_cache:s.skipCache===!0};return await this._handleWorkflowStream("audioToText",o,{stageName:"AudioToText",onStart:()=>{this.audioToTextProgress="Starting audio transcription..."},onProgress:r=>{this.audioToTextProgress=r.message||`${r.percentage}%`,r.dataPayload&&r.dataPayload.segments&&r.dataPayload.segments.length>0&&this._updateEditableSegmentsFromSegments(r.dataPayload.segments)},onSuccess:r=>{this.audioToTextProgress="Audio transcription completed.",r&&r.segments&&r.segments.length>0&&this._updateEditableSegmentsFromSegments(r.segments)},onError:r=>{this.audioToTextError=r.message||"Audio transcription failed.",this.audioToTextProgress="Error: "+this.audioToTextError}})}catch(o){throw this.audioToTextError=o.message||"Audio transcription failed.",console.error("[processAudioToText] Error:",o),o}finally{this.isLoading=!1}},_updateEditableSegmentsFromSegments(s){!s||s.length===0||(this.editableSegments||(this.editableSegments=[]),s.forEach((t,o)=>{const l=this.editableSegments.findIndex(r=>r.startTimeMs===t.start_time_ms&&r.endTimeMs===t.end_time_ms||r.id===t.segment_id);l>=0?(this.editableSegments[l].text=t.text,t.status&&(this.editableSegments[l].status=t.status),t.error_detail&&(this.editableSegments[l].errorDetail=t.error_detail)):this.editableSegments.push({id:t.segment_id||`s${o+1}`,startTimeMs:t.start_time_ms,endTimeMs:t.end_time_ms,text:t.text,status:t.status||"SUCCESS",errorDetail:t.error_detail||null})}))},initializeEditableSegments(){this.useTranscriptionForEditing?this.audioToTextResult&&Array.isArray(this.audioToTextResult.segments)?(this.editableSegments=this.audioToTextResult.segments.map((s,t)=>({id:`segment-${Date.now()}-${t}`,startTimeMs:s.startTimeMs,endTimeMs:s.endTimeMs,text:s.text})),this.selectedSegmentId=null,console.log("Editable segments initialized from transcription:",this.editableSegments)):typeof this.audioToTextResult=="string"?(this.editableSegments=[{id:`segment-${Date.now()}-0`,startTimeMs:0,endTimeMs:0,text:this.audioToTextResult}],this.selectedSegmentId=null,console.log("Editable segments initialized from plain text transcription:",this.editableSegments)):(console.warn("Cannot initialize editable segments: audioToTextResult is not in expected format or is null, despite useTranscriptionForEditing being true."),this.editableSegments=[]):(this.editableSegments=[],console.log("Not using transcription for editing. Editable segments cleared.")),this.editedSubtitlesSaved=!1},async generateSubtitles(s=!1){if(!this.audioToTextResult||!this.audioToTextResult.segments)throw new Error("No transcription result available for subtitle generation");this.isLoading=!0,this.generateSubtitlesProgress="开始生成字幕...",this.progressUpdates.push("📝 开始生成字幕片段...");try{this.generateSubtitlesProgress="调用后端生成字幕...";const t={req_text_content:this.audioToTextResult.transcript,workflow_type:"text_to_srt",request_word_timestamps:!1,cache_control:{skip_cache:s}};s&&this.progressUpdates.push("⚠️ 跳过缓存，强制重新生成字幕...");const o=u=>{console.log(`[${new Date().toISOString()}] Received GenerateSubtitles progress-update:`,JSON.stringify(u)),u&&u.stageName&&(u.stageName.includes("generateSubtitles")||u.stageName.includes("GenerateSubtitles")||u.stageName.includes("字幕生成")||u.stageName.includes("字幕断句")||u.stageName.includes("字幕优化"))&&(this.generateSubtitlesProgress=`${u.message} (${u.percentage}%)`,this.progressUpdates.push(`📝 生成进度：${u.percentage}% - ${u.message}`),console.log("[GenerateSubtitles] Progress update:",u))},l=window.electronAPI.on("progress-update",o);let r;try{r=await window.electronAPI.invoke("subtitler-full-workflow",t)}finally{l&&l()}r&&r.generate_subtitles_response&&r.generate_subtitles_response.segments?(this.generatedSubtitles=r.generate_subtitles_response.segments.map((u,p)=>({id:u.id||`generated-${p}`,text:u.text,startTimeMs:u.startTimeMs||u.start_time_ms,endTimeMs:u.endTimeMs||u.end_time_ms,start_time_ms:u.start_time_ms||u.startTimeMs,end_time_ms:u.end_time_ms||u.endTimeMs})),this.generateSubtitlesProgress="字幕生成完成！",this.progressUpdates.push(`✅ 成功从转录文本生成 ${this.generatedSubtitles.length} 个字幕片段`)):(this.generateSubtitlesProgress="使用备用句子生成方法...",this.generatedSubtitles=this._generateSentencesFromSegments(this.audioToTextResult.segments),this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`))}catch(t){console.error("Error generating subtitles:",t),this.generateSubtitlesProgress="后端失败，使用备用方法...";try{this.generatedSubtitles=this._generateSentencesFromSegments(this.audioToTextResult.segments),this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`)}catch(o){throw this.generateSubtitlesProgress="字幕生成失败",this.progressUpdates.push(`❌ 错误：${o.message}`),o}}finally{this.isLoading=!1}},_generateSentencesFromSegments(s){if(!s||s.length===0)return[];console.log(`[generateSentences] Processing ${s.length} segments`);const t=[];let o={words:[],startTime:null,endTime:null};const l=/[.!?。！？]/,r=12,u=5;for(let p=0;p<s.length;p++){const b=s[p],w=b.text.trim();if(!w)continue;o.words.length===0&&(o.startTime=b.startTimeMs||b.start_time_ms),o.words.push(w),o.endTime=b.endTimeMs||b.end_time_ms;const d=l.test(w),v=o.words.length>=u,y=o.words.length>=r,M=p<s.length-1&&(s[p+1].startTimeMs||s[p+1].start_time_ms)-o.endTime>1500,h=p===s.length-1;let P=!1,T="";if(d?(P=!0,T="punctuation"):y?(P=!0,T="too long"):v&&M?(P=!0,T="time gap"):h&&o.words.length>0&&(P=!0,T="last segment"),P){const m=o.words.join(" ").trim();m&&(console.log(`[generateSentences] Creating sentence ${t.length+1}: "${m.substring(0,50)}..." (${o.words.length} words, reason: ${T})`),t.push({id:`sentence-${t.length}`,text:m,startTimeMs:o.startTime,endTimeMs:o.endTime,start_time_ms:o.startTime,end_time_ms:o.endTime})),h||(o={words:[],startTime:null,endTime:null})}}return console.log(`[generateSentences] Generated ${t.length} sentences from ${s.length} segments`),t},async saveEditedSubtitles(){if(!this.editableSegments||this.editableSegments.length===0){console.warn("No edited segments to save."),this.editedSubtitlesSaved=!1;return}this.isLoading=!0,this.editedSubtitlesSaved=!1;try{const s=this.editableSegments.map(o=>({id:o.id,text:o.text,startTimeMs:o.startTimeMs,endTimeMs:o.endTimeMs,start_time_ms:o.start_time_ms,end_time_ms:o.end_time_ms}));console.log("Saving edited subtitles:",s.length,"segments"),this.progressUpdates.push(`💾 正在保存 ${s.length} 个编辑后的字幕片段...`);const t=await window.electronAPI.invoke("subtitler:save-edited-segments",s);console.log("Save edited subtitles result:",t),this.progressUpdates.push("✅ 编辑后的字幕保存成功"),this.editedSubtitlesSaved=!0}catch(s){console.error("Error saving edited subtitles:",s),this.progressUpdates.push(`❌ 保存编辑字幕失败：${s.message}`),this.editedSubtitlesSaved=!1}finally{this.isLoading=!1}}},lt={translationSettings:{targetLanguage:"en",quality:"balanced",style:"formal",customPrompt:""},translatedSubtitles:null,translationProgress:"",translationProgressPercent:0,translationError:null,lastError:null,currentOperationStatus:ie.IDLE,currentSegmentIndex:0,totalSegments:0},it={getLanguageDisplayName:()=>s=>({zh:"中文(简体)","zh-CN":"中文(简体)","zh-TW":"中文(繁体)",en:"英语",ja:"日语",ko:"韩语",fr:"法语",de:"德语",es:"西班牙语",it:"意大利语",pt:"葡萄牙语",ru:"俄语",ar:"阿拉伯语",th:"泰语",vi:"越南语"})[s]||s},at={setTranslationSettings(s){this.translationSettings={...this.translationSettings,...s}},async translateSubtitles(s={}){let t=s.subtitleContent;if(!t&&this.generatedSubtitles&&(typeof this.generatedSubtitles=="string"?t=this.generatedSubtitles:this.generatedSubtitles&&this.generatedSubtitles.srt_content&&(t=this.generatedSubtitles.srt_content)),!t&&this.editableSegments&&this.editableSegments.length>0)try{const{segmentsToSrt:l}=await Je(async()=>{const{segmentsToSrt:r}=await import("./index-5pfIJIcs.js").then(u=>u.I);return{segmentsToSrt:r}},__vite__mapDeps([0,1]),import.meta.url);t=l(this.editableSegments)}catch(l){console.error("Error converting segments to SRT:",l)}if(!t){const l=new Error("No subtitle content available for translation.");this.translationError=l.message,this.lastError={code:"NO_CONTENT",message:"没有可用于翻译的字幕内容",details:"请确保您已经完成了字幕编辑步骤，并且有可编辑的字幕段落。"},this.currentOperationStatus=ie.ERROR,console.error(this.translationError);return}const o=s.targetLanguage||this.translationSettings.targetLanguage||"en";this.isLoading=!0,this.translationError=null,this.lastError=null,this.translationProgress=`正在准备翻译为 ${o}...`,this.currentOperationStatus=ie.PENDING,this.currentSegmentIndex=0,this.totalSegments=this.editableSegments?this.editableSegments.length:0;try{const l={subtitle_content:t,target_language:o,skip_cache:s.skipCache===!0,force_retranslate:s.forceRetranslate===!0,trace_id:s.traceId||this._generateTraceId()};return await this._handleWorkflowStream("translateSubtitles",l,{stageName:"TranslateSubtitles",onStart:()=>{this.translationProgress=`正在开始翻译为 ${o}...`,this.currentOperationStatus=ie.RUNNING,this.progressUpdates.push(`✅ 开始翻译字幕为 ${o}`)},onProgress:u=>{this.translationProgress=u.message||`${u.percentage}%`,this.translationProgressPercent=u.percentage||0,u.dataPayload&&u.dataPayload.current_segment!==void 0&&(this.currentSegmentIndex=u.dataPayload.current_segment),u.status&&(this.currentOperationStatus=this._mapProgressStatusToOperationStatus(u.status)),u.dataPayload&&u.dataPayload.segment_results&&u.dataPayload.segment_results.length>0&&this._updateSegmentsWithTranslation(u.dataPayload.segment_results)},onSuccess:u=>{this.translationProgress="翻译已完成",this.currentOperationStatus=ie.COMPLETED,this.progressUpdates.push("✅ 字幕翻译已完成"),u&&u.segment_results&&u.segment_results.length>0&&this._updateSegmentsWithTranslation(u.segment_results)},onError:u=>{this.translationError=u.message||"翻译失败",this.translationProgress="错误: "+this.translationError,this.currentOperationStatus=ie.ERROR,this.lastError={code:u.code||"TRANSLATION_FAILED",message:u.message||"翻译过程中出现错误",details:u.details||"请检查网络连接和翻译设置，然后重试。"},this.progressUpdates.push(`❌ 翻译失败: ${u.message||"未知错误"}`)},onCancel:()=>{this.translationProgress="翻译已取消",this.currentOperationStatus=ie.CANCELED,this.progressUpdates.push("⚠️ 翻译已被用户取消")}})}catch(l){throw this.translationError=l.message||"翻译失败",this.currentOperationStatus=ie.ERROR,this.lastError={code:l.code||"TRANSLATION_FAILED",message:l.message||"翻译过程中出现错误",details:l.details||"请检查网络连接和翻译设置，然后重试。"},console.error("[translateSubtitles] Error:",l),this.progressUpdates.push(`❌ 翻译失败: ${l.message||"未知错误"}`),l}finally{this.isLoading=!1}},_updateSegmentsWithTranslation(s){if(!(!s||s.length===0)){if(!this.editableSegments){this.editableSegments=[],s.forEach((t,o)=>{this.editableSegments.push({id:t.segment_id||`s${o+1}`,text:t.original_text||"",translatedText:t.translated_text||"",status:t.status||"SUCCESS",errorDetail:t.error_detail||null})});return}s.forEach(t=>{let o=-1;t.segment_id&&(o=this.editableSegments.findIndex(l=>l.id===t.segment_id)),o<0&&t.original_text&&(o=this.editableSegments.findIndex(l=>l.text===t.original_text)),o>=0?(this.editableSegments[o].translatedText=t.translated_text||"",t.status&&(this.editableSegments[o].translationStatus=t.status),t.error_detail&&(this.editableSegments[o].translationErrorDetail=t.error_detail)):t.translated_text&&this.editableSegments.push({id:t.segment_id||`t${this.editableSegments.length+1}`,text:t.original_text||"",translatedText:t.translated_text,translationStatus:t.status||"SUCCESS",translationErrorDetail:t.error_detail||null})})}},_updateEditableSegmentsWithTranslation(s){if(!this.editableSegments||!s){console.warn("Cannot update editable segments: missing data");return}console.log("Updating editable segments with translation data..."),console.log("Original editableSegments count:",this.editableSegments.length),console.log("Translated segments count:",s.length),this.editableSegments=this.editableSegments.map((t,o)=>{let l=null;if(l=s.find(r=>r.startTimeMs===t.startTimeMs&&r.endTimeMs===t.endTimeMs),!l&&s[o]&&(l=s[o]),l){const r={...t,translatedText:l.translatedText||l.text,translation:l.translatedText||l.text,originalText:t.text};return console.log(`Updated segment ${o}:`,{original:t.text,translated:r.translatedText}),r}else return console.warn(`No translation found for segment ${o}:`,t.text),t}),console.log("Updated editableSegments with translation data"),this.progressUpdates.push("🔄 已将翻译数据合并到可编辑字幕中")}},ut={exportFilename:"",exportFormat:"srt",exportLayout:"原文在上",exportLayouts:["原文在上"],exportContentSource:"editable_segments",exportContentSources:["editable_segments"],exportAutoSaveToDefault:!1,lastExportPath:null,exportResults:[]},dt={_getLayoutSuffix:()=>s=>({原文在上:"原文在上",译文在上:"译文在上",仅原文:"仅原文",仅译文:"仅译文",original_top:"原文在上",translation_top:"译文在上",original_only:"仅原文",translation_only:"仅译文"})[s]||s},ct={setExportFilename(s){this.exportFilename=s},setExportFormat(s){this.exportFormat=s},setExportLayout(s){this.exportLayout=s},setExportLayouts(s){this.exportLayouts=s||[]},setExportContentSource(s){this.exportContentSource=s},setExportContentSources(s){this.exportContentSources=s||[]},setExportAutoSaveToDefault(s){this.exportAutoSaveToDefault=s},async exportSubtitles(s={}){let t,o=null;switch(this.exportContentSource){case"transcript_text":if(this.audioToTextResult&&typeof this.audioToTextResult.transcript=="string")o=this.audioToTextResult.transcript,t=[{id:"transcript",startTimeMs:0,endTimeMs:0,text:o}];else{console.warn("Transcript text selected for export, but no transcript available."),this.progressUpdates.push("❌ 错误：选择了转录文本导出，但未找到转录数据");return}break;case"transcript_segments":if(this.audioToTextResult&&Array.isArray(this.audioToTextResult.segments))t=JSON.parse(JSON.stringify(this.audioToTextResult.segments));else{console.warn("Transcript segments selected for export, but no segments available."),this.progressUpdates.push("❌ 错误：选择了转录片段导出，但未找到片段数据");return}break;case"translation_result":this.translatedSubtitles&&this.translatedSubtitles.length>0?(t=this.translatedSubtitles.map(r=>({id:r.id,text:r.originalText||r.text,translation:r.translatedText,startTimeMs:r.startTimeMs,endTimeMs:r.endTimeMs,start_time_ms:r.start_time_ms,end_time_ms:r.end_time_ms})),this.progressUpdates.push("📝 使用翻译字幕进行导出")):(console.warn("Translation result selected for export, but no translation available."),this.progressUpdates.push("⚠️ 警告：未找到翻译结果，使用当前编辑的字幕"),t=JSON.parse(JSON.stringify(this.editableSegments)));break;case"editable_segments":default:if(!this.editableSegments||this.editableSegments.length===0){console.warn("No segments to export."),this.progressUpdates.push("❌ 没有可导出的字幕");return}t=this.editableSegments.map(r=>({id:r.id,text:r.text,translation:r.translatedText||r.translation,startTimeMs:r.startTimeMs,endTimeMs:r.endTimeMs,start_time_ms:r.start_time_ms,end_time_ms:r.end_time_ms}));break}if(!t&&!o){console.warn("No content determined for export."),this.progressUpdates.push("Error: Could not determine content to export based on selection.");return}this.isLoading=!0,this.lastExportPath=null;const l={segments:t,rawContent:o,filename:s.filename||this.exportFilename||(this.uploadedFile?this.uploadedFile.name.split(".").slice(0,-1).join("."):"subtitles"),format:this.exportFormat,layout:this.exportLayout,contentSource:this.exportContentSource,autoSaveToDefault:this.exportAutoSaveToDefault};console.log("Exporting subtitles with payload:",l),this.progressUpdates.push(`Attempting to export as ${l.format.toUpperCase()}...`);try{try{this.progressUpdates.push("Using backend SaveSubtitle API...");const b={segments:t.map(d=>({start_time_ms:d.startTimeMs||d.start_time_ms||0,end_time_ms:d.endTimeMs||d.end_time_ms||0,original_text:d.text||"",translated_text:d.translation||d.translatedText||""})),format:this.exportFormat,layout:this.exportLayout,fileNamePrefix:l.filename,auto_save_to_default:this.exportAutoSaveToDefault},w=await window.electronAPI.invoke("subtitler-save-subtitle",b);if(w&&w.file_path){this.lastExportPath=w.file_path,this.progressUpdates.push(`✅ 字幕成功导出到：${w.file_path}`),console.log("Backend export successful:",w.file_path);return}else throw new Error("Backend SaveSubtitle API returned invalid response")}catch(p){console.warn("Backend SaveSubtitle failed, using fallback:",p),this.progressUpdates.push("Backend export failed, using local fallback")}const r=JSON.parse(JSON.stringify(l)),u=await window.electronAPI.invoke("subtitler:export-subtitles",r);u.filePath?(this.lastExportPath=u.filePath,this.progressUpdates.push(`✅ 字幕成功导出到：${u.filePath}`),console.log("Export successful:",u.filePath)):u.error?(console.error("Export failed:",u.error),this.progressUpdates.push(`❌ 导出失败：${u.error}`)):u.cancelled?(console.log("Export was cancelled by the user."),this.progressUpdates.push("⏹️ 用户取消了导出操作")):(console.warn("Export completed with an unknown status:",u),this.progressUpdates.push("❓ 导出状态未知"))}catch(r){console.error("Error during exportSubtitles action:",r),this.progressUpdates.push(`❌ 导出过程中发生错误：${r.message}`)}finally{this.isLoading=!1}},async exportMultipleSubtitles(s={}){const{filename:t,contentSources:o,layouts:l,format:r,autoSave:u}=s;this.isLoading=!0,this.exportResults=[],this.progressUpdates=[];try{let p=o.length*l.length,b=0;this.progressUpdates.push(`开始批量导出：${p} 个文件`);for(const w of o)for(const d of l)try{this.exportContentSource=w,this.exportLayout=d,this.exportFormat=r,this.exportAutoSaveToDefault=u;const v=this._generateExportSuffix(w,d),y=t?`${t}_${v}`:`subtitles_${v}`;this.exportFilename=y,this.progressUpdates.push(`导出中: ${y} (${b+1}/${p})`),await this.exportSubtitles({filename:y}),this.lastExportPath&&(this.exportResults.push({contentSource:w,layout:d,format:r,filename:y,path:this.lastExportPath}),b++,this.progressUpdates.push(`✅ 完成: ${y}`))}catch(v){console.error(`Export failed for ${w} + ${d}:`,v),this.progressUpdates.push(`❌ 失败: ${w} + ${d} - ${v.message}`)}this.progressUpdates.push(`🎉 批量导出完成！成功导出 ${b}/${p} 个文件`)}catch(p){console.error("Multi-export error:",p),this.progressUpdates.push(`❌ 批量导出失败: ${p.message}`)}finally{this.isLoading=!1}},_generateExportSuffix(s,t){const o={editable_segments:"编辑版",transcript_text:"原始文本",transcript_segments:"原始片段",translation_result:"翻译版"},l={original_top:"原文在上",translation_top:"译文在上",original_only:"仅原文",translation_only:"仅译文"},r=o[s]||s,u=l[t]||t;return`${r}_${u}`}},gt={oneClickOperationInProgress:!1,oneClickOperationError:null,oneClickWorkflowType:"vid_to_srt_trans",oneClickTargetLanguage:"zh-CN",oneClickExportFormat:"srt",oneClickExportLayout:"原文在上",oneClickExportLayouts:["原文在上"]},mt={async performOneClickOperation(){if(!this.oneClickOperationInProgress)try{if(this.oneClickOperationInProgress=!0,this.oneClickOperationError=null,!this.uploadedFile)throw new Error("请先上传文件");const s="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(l){const r=Math.random()*16|0;return(l==="x"?r:r&3|8).toString(16)}),t={file_path:this.uploadedFile.path,workflow_type:this.oneClickWorkflowType,target_language:this.oneClickTargetLanguage,request_word_timestamps:!0,export_format:this.oneClickExportFormat,export_layout:this.oneClickExportLayout,export_layouts:this.oneClickExportLayouts,auto_save_to_default:!1},o=await this._handleOneClickWorkflow(t);return o&&o.success&&(this.oneClickWorkflowType==="vid_to_audio"?this.videoToAudioResult=o.video_to_audio_response:this.oneClickWorkflowType==="vid_to_srt"||this.oneClickWorkflowType==="audio_to_srt"?(o.audio_to_text_response&&(this.audioToTextResult=o.audio_to_text_response,this.audioToTextResult.segments&&this.audioToTextResult.segments.length>0&&this.initializeEditableSegments()),o.export_results&&(this.exportResults=o.export_results,o.export_results.length>0&&(this.lastExportPath=o.export_results[0].file_path))):(this.oneClickWorkflowType==="vid_to_srt_trans"||this.oneClickWorkflowType==="audio_to_srt_trans")&&(o.audio_to_text_response&&(this.audioToTextResult=o.audio_to_text_response),o.translate_subtitles_response&&(this.translatedSubtitles=o.translate_subtitles_response),this.audioToTextResult&&this.audioToTextResult.segments&&this.audioToTextResult.segments.length>0&&(this.initializeEditableSegments(),this.translatedSubtitles&&this.translatedSubtitles.segment_results&&this._updateEditableSegmentsWithTranslation(this.translatedSubtitles.segment_results)),o.export_results&&(this.exportResults=o.export_results,o.export_results.length>0&&(this.lastExportPath=o.export_results[0].file_path)))),o}catch(s){throw console.error("One-click operation failed:",s),this.oneClickOperationError=s.message||"操作失败",s}finally{this.oneClickOperationInProgress=!1}},async _handleOneClickWorkflow(s){const t=s.trace_id||this._generateTraceId();return this.currentProcessState={traceId:t,workflowType:s.workflow_type,currentStageName:"准备一键操作",overallPercentage:0,currentMessage:"正在准备处理流程...",status:"IN_PROGRESS",errorDetail:null,isActive:!0,finalDataPayload:null},new Promise((o,l)=>{const r=p=>{if(!(p.traceId&&p.traceId!==t)&&(this.currentProcessState={...this.currentProcessState,currentStageName:p.stageName||this.currentProcessState.currentStageName,overallPercentage:p.percentage||0,currentMessage:p.message||"",status:p.status||"IN_PROGRESS",errorDetail:p.errorDetail||null,isActive:p.status!=="SUCCESS"&&p.status!=="ERROR",finalDataPayload:p.dataPayload||this.currentProcessState.finalDataPayload},this.progressUpdates.push({stageName:p.stageName,percentage:p.percentage,message:p.message,isError:p.isError,errorMessage:p.errorMessage,traceId:p.traceId,status:p.status,errorDetail:p.errorDetail,timestamp:Date.now(),dataPayload:p.dataPayload}),p.dataPayload&&this._updateEditableSegmentsFromProgressData(p),p.isError||p.status==="ERROR")){const b=new Error(p.errorMessage||"一键操作失败");b.detail=p.errorDetail,l(b)}},u=window.electronAPI.on("progress-update",r);window.electronAPI.invoke("subtitler-full-workflow",s).then(p=>{this.currentProcessState.isActive=!1,this.currentProcessState.status="SUCCESS",this.currentProcessState.finalDataPayload=p,o(p)}).catch(p=>{this.currentProcessState.isActive=!1,this.currentProcessState.status="ERROR",this.currentProcessState.errorDetail={errorCode:"WORKFLOW_ERROR",technicalMessage:p.message,userMessage:"一键操作失败",context:{workflowType:s.workflow_type}},l(p)}).finally(()=>{u&&u()})})},setOneClickWorkflowType(s){this.oneClickWorkflowType=s},setOneClickTargetLanguage(s){this.oneClickTargetLanguage=s},setOneClickExportFormat(s){this.oneClickExportFormat=s},setOneClickExportLayout(s){this.oneClickExportLayout=s},setOneClickExportLayouts(s){Array.isArray(s)&&s.length>0?(this.oneClickExportLayouts=s,this.oneClickExportLayout=s[0]):(this.oneClickExportLayouts=["原文在上"],this.oneClickExportLayout="原文在上")}};function pt(){return"trace-"+Date.now()+"-"+Math.random().toString(36).substring(2,15)}const ft={_segmentsToSrt(s){return!s||s.length===0?(console.warn("[_segmentsToSrt] No segments provided"),""):(console.log("[_segmentsToSrt] Converting",s.length,"segments to SRT"),s.map((t,o)=>{const l=t.startTimeMs||t.start_time_ms||0,r=t.endTimeMs||t.end_time_ms||1e3,u=this._msToSrtTime(l),p=this._msToSrtTime(r),b=t.text||"";return b.trim()||console.warn("[_segmentsToSrt] Empty text for segment",o),`${o+1}
${u} --> ${p}
${b}
`}).join(`
`))},_msToSrtTime(s){const t=Math.floor(s/1e3),o=Math.floor(t/3600),l=Math.floor(t%3600/60),r=t%60,u=s%1e3;return`${o.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")},${u.toString().padStart(3,"0")}`},_parseSrtToSegments(s,t){const o=s.trim().split(`
`),l=[];let r=null;for(let u=0;u<o.length;u++){const p=o[u].trim();if(!p){r&&(l.push(r),r=null);continue}if(/^\d+$/.test(p))r={index:parseInt(p)};else if(p.includes("-->")){const[b,w]=p.split("-->").map(y=>y.trim()),d=this._srtTimeToMs(b),v=this._srtTimeToMs(w);r&&(r.startTimeMs=d,r.endTimeMs=v,r.start_time_ms=d,r.end_time_ms=v)}else if(r&&!r.text){r.text=p,r.translatedText=p;const b=r.index-1;t&&t[b]?(r.originalText=t[b].text,r.id=t[b].id||`translated-${r.index}`):(r.originalText=p,r.id=`translated-${r.index}`)}}return r&&l.push(r),l},_srtTimeToMs(s){const[t,o]=s.split(","),[l,r,u]=t.split(":").map(Number);return(l*3600+r*60+u)*1e3+parseInt(o||0)},getStageNameInChinese(s){return{audio_extraction:"音频提取",audio_processing:"音频处理",transcription:"语音识别",text_processing:"文本处理",subtitle_generation:"字幕生成",subtitle_optimization:"字幕优化",translation:"翻译处理",export:"导出处理",file_processing:"文件处理",initialization:"初始化",completion:"完成处理",VideoToAudio:"视频转音频",AudioToText:"音频转文字",GenerateSubtitles:"生成字幕",OptimizeSubtitles:"优化字幕",Translation:"翻译处理",Export:"导出处理"}[s]||s},async cancelCurrentOperation(){if(!this.currentProcessState.isActive){console.log("没有活跃的操作需要取消");return}try{if(console.log(`正在取消操作，trace_id: ${this.currentProcessState.traceId}`),this.currentProcessState.status="CANCELING",this.currentProcessState.currentMessage="正在取消操作...",this.progressUpdates.push({stageName:this.currentProcessState.currentStageName||"取消操作",percentage:this.currentProcessState.overallPercentage,message:"用户取消了操作",isError:!1,errorMessage:"",traceId:this.currentProcessState.traceId,status:"CANCELED",errorDetail:null,timestamp:Date.now(),dataPayload:null}),this.currentProcessState.traceId)try{await window.electronAPI.invoke("cancel-operation",{trace_id:this.currentProcessState.traceId})}catch(s){console.warn("后端取消操作失败:",s.message)}this.currentProcessState.isActive=!1,this.currentProcessState.status="CANCELED",this.currentProcessState.currentMessage="操作已取消",this.isLoading=!1,this.currentOperationStatus&&(this.currentOperationStatus="CANCELED"),console.log("操作已成功取消")}catch(s){console.error("取消操作时发生错误:",s),this.currentProcessState.isActive=!1,this.currentProcessState.status="ERROR",this.currentProcessState.errorDetail={errorCode:"CANCEL_ERROR",technicalMessage:s.message,userMessage:"取消操作时发生错误",context:{}},this.isLoading=!1,this.currentOperationStatus&&(this.currentOperationStatus="ERROR")}},_mapProgressStatusToOperationStatus(s){return typeof s=="string"?{UNSPECIFIED:"IDLE",IN_PROGRESS:"RUNNING",ERROR:"ERROR",ERROR:"ERROR",SUCCESS:"COMPLETED",PARTIAL_SUCCESS:"COMPLETED",CANCELLED:"CANCELED",PENDING:"PENDING"}[s]||"IDLE":typeof s=="number"&&{0:"IDLE",1:"RUNNING",2:"ERROR",3:"COMPLETED",4:"COMPLETED",5:"CANCELED",6:"PENDING"}[s]||"IDLE"},_updateEditableSegmentsFromProgressData(s){if(!s||!s.dataPayload)return;const{dataPayload:t}=s;t.segments&&Array.isArray(t.segments)&&(!this.editableSegments||this.editableSegments.length===0?this._updateEditableSegmentsFromSegments(t.segments):t.segments.forEach((o,l)=>{this.editableSegments[l]&&(this.editableSegments[l].status=o.status||"SUCCESS",this.editableSegments[l].errorDetail=o.error_detail||null,o.text&&this.editableSegments[l].text!==o.text&&(this.editableSegments[l].text=o.text))})),t.segment_results&&Array.isArray(t.segment_results)&&this.editableSegments&&this.editableSegments.length>0&&t.segment_results.forEach(o=>{const l=o.segment_id;let r=null;if(l)if(/^\d+$/.test(l)){const u=parseInt(l)-1;u>=0&&u<this.editableSegments.length&&(r=this.editableSegments[u])}else r=this.editableSegments.find(u=>u.id===l);r&&(o.translated_text&&(r.translatedText=o.translated_text),r.status=o.status||"SUCCESS",r.errorDetail=o.error_detail||null)}),t.srt_content&&t.segments&&console.log("[Store] 收到字幕生成结果，段落数:",t.segments.length)},_generateTraceId(){return pt()}},vt=()=>typeof window<"u"&&window.subtitlerClient?(console.log("[SubtitlerStore] subtitlerClient found and available"),window.subtitlerClient):(console.warn("[SubtitlerStore] subtitlerClient not available. Debugging info:"),console.warn("- window object exists:",typeof window<"u"),console.warn("- window.subtitlerClient exists:",typeof window<"u"&&!!window.subtitlerClient),console.warn("- window.electronAPI exists:",typeof window<"u"&&!!window.electronAPI),typeof window<"u"&&console.warn("- Available window properties:",Object.keys(window).filter(s=>s.includes("subtitler")||s.includes("electron")||s.includes("grpc"))),null),ce=Xe("subtitler",{state:()=>({...tt,...rt,...lt,...ut,...gt}),getters:{...st,...it,...dt,srtPreviewFromEditableSegments:s=>s.editableSegments&&s.editableSegments.length>0?Re(s.editableSegments):"",srtPreviewFromOriginalSegments:s=>s.audioToTextResult&&s.audioToTextResult.segments&&s.audioToTextResult.segments.length>0?Re(s.audioToTextResult.segments):"",getSegmentById:s=>t=>s.editableSegments&&s.editableSegments.find(o=>o.id===t)||null,isReadyForNextStep:s=>{switch(s.currentStep){case 1:return s.uploadedFile!==null;case 2:return s.videoToAudioResult!==null;case 3:return s.audioToTextResult!==null;case 4:return s.generatedSubtitles!==null&&s.generatedSubtitles.length>0;case 5:return s.optimizedSubtitles!==null||s.generatedSubtitles!==null;case 6:return s.editableSegments!==null&&s.editableSegments.length>0;case 7:return!0;case 8:return s.translatedSubtitles!==null;case 9:return!0;default:return!1}},getUploadedFileName:s=>s.uploadedFile?s.uploadedFile.name:"",getUploadedFileSize:s=>s.uploadedFile?s.uploadedFile.size:"",getUploadedFileType:s=>s.uploadedFile?s.uploadedFile.type:"",hasEditableSegments:s=>s.editableSegments&&s.editableSegments.length>0,selectedSegment:s=>!s.selectedSegmentId||!s.editableSegments?null:s.editableSegments.find(t=>t.id===s.selectedSegmentId)||null},actions:{...ot,...nt,...at,...ct,...mt,...ft,setActiveResultTab(s){this.activeResultTab=s},setUploadedFile(s){s?(this.uploadedFile={name:s.name,path:s.path,type:s.type,size:s.size},this.currentStep=1,this.progressUpdates=[],console.log("[SubtitlerStore] File uploaded:",this.uploadedFile)):(this.uploadedFile=null,console.log("[SubtitlerStore] File cleared"))},setCurrentStep(s){s>=1&&s<=9&&(this.currentStep=s,console.log(`[SubtitlerStore] Current step set to: ${s}`))},setSelectedSegmentId(s){this.selectedSegmentId=s},updateSegmentText({id:s,newText:t}){const o=this.editableSegments.find(l=>l.id===s);o&&(o.text=t,console.log(`Updated segment ${s} text`))},updateSegmentTime({id:s,newStartTimeMs:t,newEndTimeMs:o}){const l=this.editableSegments.find(r=>r.id===s);l&&(t!==void 0&&(l.startTimeMs=t),o!==void 0&&(l.endTimeMs=o),console.log(`Updated segment ${s} timing`))},addSegment({afterId:s=null,newSegmentData:t={startTimeMs:0,endTimeMs:0,text:"New Segment"}}){this.editableSegments||(this.editableSegments=[]);const o={id:`segment-${Date.now()}`,...t};if(s){const l=this.editableSegments.findIndex(r=>r.id===s);l!==-1?this.editableSegments.splice(l+1,0,o):this.editableSegments.push(o)}else this.editableSegments.push(o);console.log(`Added new segment: ${o.id}`)},deleteSegment(s){if(this.editableSegments){const t=this.editableSegments.findIndex(o=>o.id===s);t!==-1&&(this.editableSegments.splice(t,1),console.log(`Deleted segment: ${s}`))}},async saveEditedSubtitles(){if(!this.editableSegments||this.editableSegments.length===0){console.warn("No segments to save");return}try{this.isLoading=!0,console.log("Saving edited subtitles..."),this.editedSubtitlesSaved=!0,console.log("Edited subtitles saved successfully"),this.progressUpdates.push("✅ 字幕编辑已保存")}catch(s){console.error("Failed to save edited subtitles:",s),this.progressUpdates.push(`❌ 保存失败: ${s.message}`)}finally{this.isLoading=!1}},async _handleWorkflowStream(s,t,o={}){const{stageName:l=s,onStart:r=()=>{},onProgress:u=()=>{},onSuccess:p=()=>{},onError:b=()=>{},onCancel:w=()=>{}}=o;return this.isLoading=!0,r(),new Promise((d,v)=>{const y=T=>{if(!(T.traceId&&this.currentProcessState.traceId&&T.traceId!==this.currentProcessState.traceId))try{if(u(T),T.status==="CANCELLED"||T.status==="CANCELED"){w(),d({cancelled:!0});return}if(T.isError||T.status==="ERROR"){const m=new Error(T.errorMessage||"操作失败");m.detail=T.errorDetail,b(m),v(m);return}if(T.status==="SUCCESS"&&T.dataPayload){p(T.dataPayload),d(T.dataPayload);return}}catch(m){console.error(`[_handleWorkflowStream] Error processing progress for ${s}:`,m),b(m),v(m)}},M=window.electronAPI.on("progress-update",y),h=vt();if(!h){const T=new Error("SubtitlerClient 不可用");b(T),v(T);return}let P;switch(s){case"videoToAudio":P=h.videoToAudio.bind(h);break;case"audioToText":P=h.audioToText.bind(h);break;case"generateSubtitles":P=h.generateSubtitles.bind(h);break;case"optimizeSubtitles":P=h.optimizeSubtitles.bind(h);break;case"translateSubtitles":P=h.translateSubtitles.bind(h);break;default:const T=new Error(`未知的方法: ${s}`);b(T),v(T);return}try{const T=P(t);T.on("data",m=>{if(m.getIsError&&m.getIsError()){const V=new Error(m.getErrorMessage?m.getErrorMessage():"操作失败");b(V),v(V);return}const z={stageName:m.getStageName?m.getStageName():l,percentage:m.getPercentage?m.getPercentage():0,message:m.getMessage?m.getMessage():"",isError:m.getIsError?m.getIsError():!1,errorMessage:m.getErrorMessage?m.getErrorMessage():"",status:m.getIsError&&m.getIsError()?"ERROR":"IN_PROGRESS",dataPayload:m};u(z)}),T.on("end",()=>{p({}),d({})}),T.on("error",m=>{console.error(`[_handleWorkflowStream] ${s} failed:`,m),b(m),v(m)})}catch(T){console.error(`[_handleWorkflowStream] ${s} setup failed:`,T),b(T),v(T)}finally{setTimeout(()=>{M&&M(),this.isLoading=!1},100)}})},_mapStatusEnumToString(s){if(s==null)return"UNKNOWN";if(typeof s=="string"){const t=s.toUpperCase();return["SUCCESS","ERROR","PENDING","CANCELLED","UNKNOWN"].includes(t)?t:"UNKNOWN"}return typeof s=="number"&&{0:"UNKNOWN",1:"SUCCESS",2:"ERROR",3:"PENDING",4:"CANCELLED"}[s]||"UNKNOWN"},_extractErrorDetail(s){if(!s)return null;try{return{errorCode:s.error_code||"UNKNOWN_ERROR",technicalMessage:s.technical_message||"",userMessage:s.user_message||"发生了未知错误",context:s.context||{}}}catch(t){return console.error("Error extracting error detail:",t),{errorCode:"EXTRACTION_ERROR",technicalMessage:"Failed to extract error details",userMessage:"无法获取错误详情",context:{}}}},_updateEditableSegmentsFromSegments(s){if(!s||s.length===0){console.warn("[_updateEditableSegmentsFromSegments] No segments provided");return}console.log("[_updateEditableSegmentsFromSegments] Updating with",s.length,"segments"),this.editableSegments=s.map((t,o)=>({id:t.id||`segment-${o+1}`,startTimeMs:t.startTimeMs||t.start_time_ms||0,endTimeMs:t.endTimeMs||t.end_time_ms||1e3,text:t.text||"",originalText:t.originalText||t.text||"",translatedText:t.translatedText||"",status:t.status||"SUCCESS",errorDetail:t.errorDetail||null})),console.log("[_updateEditableSegmentsFromSegments] Updated editableSegments with",this.editableSegments.length,"segments")}}}),bt={class:"stepper-navigation-container"},xt={class:"workflow-status mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"},ht={class:"flex items-center justify-between"},yt={class:"text-xs text-blue-600"},St={class:"stepper-nav bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl shadow-lg border border-blue-100 px-6 py-6"},wt={class:"flex items-center space-x-3 sm:space-x-4 min-w-max py-4 px-4"},_t=["onClick","disabled","title"],kt={class:"text-lg sm:text-xl md:text-2xl font-bold"},$t={class:"text-xs leading-tight text-center px-1 mt-0.5"},Tt={key:0,class:"flex items-center mx-2 sm:mx-3"},Ct={__name:"StepperNavigation",setup(s){const t=ce(),o=L(null),l=x(()=>t.currentStep),r=x(()=>{const h=u.filter(T=>p(T.id)).length,P=u.length;return`已完成 ${h}/${P} 个步骤`}),u=[{id:1,name:"上传文件"},{id:2,name:"提取音频"},{id:3,name:"语音转文字"},{id:4,name:"组成句子"},{id:5,name:"优化句子"},{id:6,name:"编辑字幕"},{id:7,name:"翻译选择"},{id:8,name:"翻译处理"},{id:9,name:"导出保存"}],p=h=>!!(h<l.value||h===2&&t.videoToAudioResult||h===3&&t.audioToTextResult||h===4&&t.editedSubtitlesSaved||h===5&&t.lastExportPath),b=h=>{if(h===l.value||p(h))return!0;const P=Math.max(l.value,w());switch(h){case 1:return!0;case 2:return t.uploadedFile!==null;case 3:return t.videoToAudioResult!==null;case 4:return t.audioToTextResult!==null;case 5:return t.audioToTextResult!==null;case 6:return t.audioToTextResult!==null;case 7:return t.editableSegments!==null;case 8:return t.editableSegments!==null;case 9:return t.editableSegments!==null;default:return h<=P}},w=()=>{var P;let h=1;return t.uploadedFile&&(h=Math.max(h,1)),t.videoToAudioResult&&(h=Math.max(h,2)),t.audioToTextResult&&(h=Math.max(h,3)),t.editableSegments&&(h=Math.max(h,6)),t.translatedSubtitles&&(h=Math.max(h,8)),(t.lastExportPath||((P=t.exportResults)==null?void 0:P.length)>0)&&(h=Math.max(h,9)),h},d=h=>{b(h)&&(t.setCurrentStep(h),fe(()=>{M()}))},v=()=>{o.value&&o.value.scrollTo({left:0,behavior:"smooth"})},y=()=>{o.value&&o.value.scrollTo({left:o.value.scrollWidth,behavior:"smooth"})},M=()=>{if(o.value){const h=o.value.querySelector(`button[title*="步骤 ${l.value}"]`);if(h){const P=o.value.getBoundingClientRect(),T=h.getBoundingClientRect(),m=T.left-P.left+o.value.scrollLeft-P.width/2+T.width/2;o.value.scrollTo({left:Math.max(0,m),behavior:"smooth"})}}};return ge(l,()=>{fe(()=>{M()})}),he(()=>{fe(()=>{M()})}),(h,P)=>(n(),i("div",bt,[e("div",xt,[e("div",ht,[P[0]||(P[0]=e("div",{class:"flex items-center"},[e("svg",{class:"h-4 w-4 text-blue-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("span",{class:"text-sm font-medium text-blue-800"},"当前工作流程")],-1)),e("span",yt,a(r.value),1)])]),e("nav",St,[e("div",{class:"nav-scroll-container overflow-x-auto overflow-y-hidden",ref_key:"navContainer",ref:o},[e("ol",wt,[(n(),i(Y,null,ee(u,T=>e("li",{key:T.id,class:"flex items-center"},[e("button",{onClick:m=>d(T.id),disabled:!b(T.id),class:H(["step-button flex flex-col items-center justify-center rounded-full text-xs font-medium transition-all duration-200 ease-in-out shadow-md hover:shadow-lg","w-16 h-16 sm:w-18 sm:h-18 md:w-20 md:h-20",l.value===T.id?"bg-gradient-to-r from-blue-500 to-indigo-600 text-white ring-4 ring-blue-200 scale-110 z-10":p(T.id)?"bg-gradient-to-r from-green-400 to-emerald-500 text-white hover:from-green-500 hover:to-emerald-600":b(T.id)?"bg-white text-gray-700 hover:bg-gray-50 border-2 border-gray-200 hover:border-blue-300":"bg-gray-100 text-gray-400 cursor-not-allowed border-2 border-gray-200"]),title:`步骤 ${T.id}: ${T.name}`},[e("span",kt,a(T.id),1),e("span",$t,a(T.name),1)],10,_t),T.id<u.length?(n(),i("div",Tt,P[1]||(P[1]=[e("div",{class:"w-5 h-0.5 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-full"},null,-1),e("svg",{class:"h-3 w-3 text-blue-400 mx-1",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1),e("div",{class:"w-5 h-0.5 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-full"},null,-1)]))):c("",!0)])),64))])],512),e("div",{class:"scroll-indicators flex justify-center mt-4 space-x-3"},[e("button",{onClick:v,class:"scroll-btn px-4 py-2 text-xs bg-white text-blue-600 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors shadow-sm",title:"滚动到开始"}," ← 开始 "),e("button",{onClick:M,class:"scroll-btn px-4 py-2 text-xs bg-blue-100 text-blue-700 rounded-full border border-blue-300 hover:bg-blue-200 transition-colors shadow-sm",title:"滚动到当前步骤"}," 当前步骤 "),e("button",{onClick:y,class:"scroll-btn px-4 py-2 text-xs bg-white text-blue-600 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors shadow-sm",title:"滚动到结束"}," 结束 → ")])])]))}},Et=re(Ct,[["__scopeId","data-v-24b9c782"]]),Mt={key:0,class:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm mb-4"},Pt={class:"flex items-center space-x-3"},At={class:"flex-shrink-0"},Lt={class:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center"},It={class:"text-xl"},Rt={class:"flex-1 min-w-0"},Ft={class:"flex items-center space-x-2"},Nt={class:"text-sm font-medium text-gray-900 truncate"},Ut={class:"flex items-center space-x-4 mt-1 text-xs text-gray-500"},Ot={key:0},zt={key:0,class:"flex-shrink-0"},Dt={class:"flex items-center space-x-2"},jt={key:0,class:"mt-3"},Bt={class:"flex items-center justify-between text-xs text-gray-600 mb-1"},Wt={class:"w-full bg-gray-200 rounded-full h-2"},Vt={key:1,class:"mt-3 p-3 bg-red-50 border border-red-200 rounded-md"},Gt={class:"flex items-center"},qt={class:"text-sm text-red-700"},Ht={__name:"FileInfoCard",props:{fileInfo:{type:Object,default:null},showCard:{type:Boolean,default:!0},processingStatus:{type:String,default:null},progress:{type:Number,default:null},progressText:{type:String,default:null},errorMessage:{type:String,default:null},showActions:{type:Boolean,default:!0},allowReselect:{type:Boolean,default:!0},allowRemove:{type:Boolean,default:!1},showProgress:{type:Boolean,default:!0}},emits:["reselect","remove"],setup(s,{emit:t}){const o=s,l=x(()=>{var y,M;if(!((y=o.fileInfo)!=null&&y.name))return"📄";const d=(M=o.fileInfo.name.split(".").pop())==null?void 0:M.toLowerCase();return{mp4:"🎬",avi:"🎬",mov:"🎬",mkv:"🎬",flv:"🎬",wmv:"🎬",mp3:"🎵",wav:"🎵",flac:"🎵",aac:"🎵",ogg:"🎵",m4a:"🎵",srt:"📝",vtt:"📝",ass:"📝",ssa:"📝",txt:"📄",doc:"📄",docx:"📄",pdf:"📄"}[d]||"📄"}),r=x(()=>({processing:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",error:"bg-red-100 text-red-800",ready:"bg-gray-100 text-gray-800",waiting:"bg-yellow-100 text-yellow-800"})[o.processingStatus]||"bg-gray-100 text-gray-800"),u=x(()=>({processing:"🔄",completed:"✅",error:"❌",ready:"📋",waiting:"⏳"})[o.processingStatus]||""),p=d=>{if(!d)return"0 B";const v=["B","KB","MB","GB"],y=Math.floor(Math.log(d)/Math.log(1024));return`${(d/Math.pow(1024,y)).toFixed(1)} ${v[y]}`},b=d=>{if(!d)return"";const v=Math.floor(d/3600),y=Math.floor(d%3600/60),M=Math.floor(d%60);return v>0?`${v}:${y.toString().padStart(2,"0")}:${M.toString().padStart(2,"0")}`:`${y}:${M.toString().padStart(2,"0")}`},w=d=>{var M;if(!d)return"";const v=(M=d.split(".").pop())==null?void 0:M.toLowerCase();return{mp4:"video/mp4",avi:"video/avi",mov:"video/quicktime",mkv:"video/x-matroska",flv:"video/x-flv",wmv:"video/x-ms-wmv",mp3:"audio/mpeg",wav:"audio/wav",flac:"audio/flac",aac:"audio/aac",ogg:"audio/ogg",m4a:"audio/mp4",srt:"text/srt",vtt:"text/vtt",ass:"text/ass"}[v]||`file/${v}`};return(d,v)=>s.fileInfo&&s.showCard?(n(),i("div",Mt,[e("div",Pt,[e("div",At,[e("div",Lt,[e("span",It,a(l.value),1)])]),e("div",Rt,[e("div",Ft,[e("h3",Nt,a(s.fileInfo.name),1),s.processingStatus?(n(),i("span",{key:0,class:H(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",r.value])},a(u.value)+" "+a(s.processingStatus),3)):c("",!0)]),e("div",Ut,[e("span",null,a(p(s.fileInfo.size)),1),e("span",null,a(s.fileInfo.type||w(s.fileInfo.name)),1),s.fileInfo.duration?(n(),i("span",Ot,a(b(s.fileInfo.duration)),1)):c("",!0)])]),s.showActions?(n(),i("div",zt,[e("div",Dt,[s.allowReselect?(n(),i("button",{key:0,onClick:v[0]||(v[0]=y=>d.$emit("reselect")),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 🔄 重新选择 ")):c("",!0),s.allowRemove?(n(),i("button",{key:1,onClick:v[1]||(v[1]=y=>d.$emit("remove")),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"}," ❌ 移除 ")):c("",!0)])])):c("",!0)]),s.showProgress&&s.progress!==null?(n(),i("div",jt,[e("div",Bt,[e("span",null,a(s.progressText||"处理进度"),1),e("span",null,a(Math.round(s.progress))+"%",1)]),e("div",Wt,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:be({width:`${Math.min(100,Math.max(0,s.progress))}%`})},null,4)])])):c("",!0),s.errorMessage?(n(),i("div",Vt,[e("div",Gt,[v[2]||(v[2]=e("span",{class:"text-red-400 mr-2"},"⚠️",-1)),e("span",qt,a(s.errorMessage),1)])])):c("",!0)])):c("",!0)}},Kt=re(Ht,[["__scopeId","data-v-6b9f5912"]]),Jt={class:"flex items-start"},Xt={class:"flex-shrink-0"},Qt={class:"text-lg"},Yt={class:"ml-3 flex-1"},Zt={class:"flex items-center justify-between"},es={key:0,class:"text-sm font-medium"},ts={class:"mt-1 text-sm"},ss={key:0,class:"mt-2 text-xs opacity-75"},os={key:1,class:"mt-3"},rs={class:"flex items-center justify-between text-xs mb-1"},ns={class:"w-full bg-white bg-opacity-30 rounded-full h-2"},ls={key:2,class:"mt-3 flex space-x-2"},is=["onClick","disabled"],as={key:0,class:"mr-1"},us={__name:"StatusMessage",props:{type:{type:String,default:"info",validator:s=>["success","warning","error","info","loading"].includes(s)},title:{type:String,default:null},message:{type:String,required:!0},details:{type:String,default:null},dismissible:{type:Boolean,default:!1},showProgress:{type:Boolean,default:!1},progress:{type:Number,default:null},progressText:{type:String,default:null},actions:{type:Array,default:()=>[]},autoHide:{type:Number,default:0}},emits:["dismiss","action"],setup(s,{emit:t}){const o=s,l=t,r=L(!0),u=x(()=>{const y={success:"bg-green-50 border-green-200 text-green-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",error:"bg-red-50 border-red-200 text-red-800",info:"bg-blue-50 border-blue-200 text-blue-800",loading:"bg-gray-50 border-gray-200 text-gray-800"};return y[o.type]||y.info}),p=x(()=>{const y={success:"✅",warning:"⚠️",error:"❌",info:"ℹ️",loading:"🔄"};return y[o.type]||y.info}),b=x(()=>o.type==="loading"),w=x(()=>{const y={success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600",info:"bg-blue-600",loading:"bg-gray-600"};return y[o.type]||y.info}),d=()=>{r.value=!1,l("dismiss")},v=y=>{y.disabled||(l("action",y),y.dismiss&&d())};return o.autoHide>0&&setTimeout(()=>{d()},o.autoHide*1e3),(y,M)=>s.message&&r.value?(n(),i("div",{key:0,class:H(["rounded-lg p-4 mb-4 border transition-all duration-300",u.value,{"animate-pulse":b.value}])},[e("div",Jt,[e("div",Xt,[e("span",Qt,a(p.value),1)]),e("div",Yt,[e("div",Zt,[s.title?(n(),i("h3",es,a(s.title),1)):c("",!0),s.dismissible?(n(),i("button",{key:1,onClick:d,class:"ml-auto flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"},M[0]||(M[0]=[e("span",{class:"sr-only"},"关闭",-1),e("svg",{class:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))):c("",!0)]),e("div",ts,a(s.message),1),s.details?(n(),i("div",ss,a(s.details),1)):c("",!0),s.showProgress&&s.progress!==null?(n(),i("div",os,[e("div",rs,[e("span",null,a(s.progressText||"进度"),1),e("span",null,a(Math.round(s.progress))+"%",1)]),e("div",ns,[e("div",{class:H([w.value,"h-2 rounded-full transition-all duration-300"]),style:be({width:`${Math.min(100,Math.max(0,s.progress))}%`})},null,6)])])):c("",!0),s.actions&&s.actions.length>0?(n(),i("div",ls,[(n(!0),i(Y,null,ee(s.actions,h=>(n(),i("button",{key:h.key,onClick:P=>v(h),class:H(["inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2",h.primary?"text-white bg-white bg-opacity-20 hover:bg-opacity-30 focus:ring-white":"text-current bg-white bg-opacity-10 hover:bg-opacity-20 focus:ring-current"]),disabled:h.disabled},[h.icon?(n(),i("span",as,a(h.icon),1)):c("",!0),ue(" "+a(h.label),1)],10,is))),128))])):c("",!0)])])],2)):c("",!0)}},pe=re(us,[["__scopeId","data-v-6716bcfe"]]),ds={class:"bg-white border border-gray-200 rounded-lg shadow-sm"},cs={class:"flex items-center space-x-2"},gs={class:"text-sm font-medium text-gray-900"},ms={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},ps={class:"flex items-center space-x-2"},fs={key:0,class:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"},vs={class:"border-t border-gray-200"},bs={class:"p-4"},xs={class:"flex items-center justify-between mb-3"},hs={class:"flex items-center space-x-2"},ys={class:"text-xs text-gray-500"},Ss={key:0,class:"text-gray-400 text-center py-4"},ws={class:"text-gray-400 text-xs flex-shrink-0 w-16"},_s={class:"flex-shrink-0"},ks={key:0,class:"mt-3 flex items-center justify-between text-xs text-gray-500"},$s={class:"flex items-center space-x-4"},Ts={key:0,class:"text-green-600"},Cs={key:1,class:"text-yellow-600"},Es={key:2,class:"text-red-600"},Ms={__name:"CollapsibleLog",props:{title:{type:String,default:"处理日志"},logs:{type:Array,default:()=>[]},initialExpanded:{type:Boolean,default:!1},showStats:{type:Boolean,default:!0},maxLogs:{type:Number,default:100}},emits:["clear","copy"],setup(s,{emit:t}){const o=s,l=t,r=L(o.initialExpanded),u=L(!0),p=L(!1),b=L(null),w=x(()=>o.logs.length),d=x(()=>o.logs.reduce((F,R)=>(F[R.level]=(F[R.level]||0)+1,F),{success:0,warning:0,error:0,info:0})),v=x(()=>{if(o.logs.length===0)return"";const F=o.logs[o.logs.length-1];return V(new Date(F.timestamp))}),y=()=>{r.value=!r.value,p.value=!1,r.value&&u.value&&fe(()=>{h()})},M=()=>{u.value=!u.value,u.value&&h()},h=()=>{b.value&&(b.value.scrollTop=b.value.scrollHeight)},P=()=>{l("clear"),p.value=!1},T=async()=>{const F=o.logs.map(R=>`[${X(R.timestamp)}] ${m(R.level)} ${R.message}`).join(`
`);try{await navigator.clipboard.writeText(F),l("copy",F)}catch(R){console.error("复制失败:",R)}},m=F=>({success:"✅",warning:"⚠️",error:"❌",info:"ℹ️",debug:"🔍"})[F]||"ℹ️",z=F=>({success:"text-green-400",warning:"text-yellow-400",error:"text-red-400",info:"text-blue-400",debug:"text-gray-400"})[F]||"text-gray-300",V=F=>F.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),X=F=>new Date(F).toLocaleTimeString("zh-CN",{hour12:!1,minute:"2-digit",second:"2-digit"});return ge(()=>o.logs.length,(F,R)=>{F>R&&(p.value=!r.value,r.value&&u.value&&fe(()=>{h()}))}),ge(r,F=>{F&&u.value&&fe(()=>{h()})}),(F,R)=>(n(),i("div",ds,[e("button",{onClick:y,class:"w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-t-lg"},[e("div",cs,[R[0]||(R[0]=e("span",{class:"text-lg"},"💡",-1)),e("span",gs,a(s.title),1),w.value>0?(n(),i("span",ms,a(w.value),1)):c("",!0)]),e("div",ps,[p.value?(n(),i("span",fs)):c("",!0),(n(),i("svg",{class:H(["w-4 h-4 text-gray-500 transition-transform duration-200",{"transform rotate-180":r.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},R[1]||(R[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))])]),q(e("div",vs,[e("div",bs,[e("div",xs,[e("div",hs,[e("button",{onClick:P,class:"inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 🗑️ 清除 "),e("button",{onClick:T,class:"inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 📋 复制 "),e("button",{onClick:M,class:H(["inline-flex items-center px-2 py-1 border shadow-sm text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",u.value?"border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100":"border-gray-300 text-gray-700 bg-white hover:bg-gray-50"])},a(u.value?"📌":"📍")+" 自动滚动 ",3)]),e("div",ys,a(V(new Date)),1)]),e("div",{ref_key:"logContainer",ref:b,class:"bg-gray-900 rounded-lg p-3 max-h-64 overflow-y-auto font-mono text-sm"},[s.logs.length===0?(n(),i("div",Ss," 暂无日志信息 ")):c("",!0),(n(!0),i(Y,null,ee(s.logs,(I,D)=>(n(),i("div",{key:D,class:H(["flex items-start space-x-2 py-1",{"border-t border-gray-700":D>0}])},[e("span",ws,a(X(I.timestamp)),1),e("span",_s,a(m(I.level)),1),e("span",{class:H(["flex-1 break-words",z(I.level)])},a(I.message),3)],2))),128))],512),s.showStats?(n(),i("div",ks,[e("div",$s,[e("span",null,"总计: "+a(w.value),1),d.value.success>0?(n(),i("span",Ts,"✅ "+a(d.value.success),1)):c("",!0),d.value.warning>0?(n(),i("span",Cs,"⚠️ "+a(d.value.warning),1)):c("",!0),d.value.error>0?(n(),i("span",Es,"❌ "+a(d.value.error),1)):c("",!0)]),e("div",null," 最后更新: "+a(v.value),1)])):c("",!0)])],512),[[Qe,r.value]])]))}},ye=re(Ms,[["__scopeId","data-v-4abe4d0b"]]),Ps={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},As={class:"current-actions mb-6"},Ls={key:0,class:"space-y-4"},Is={key:1,class:"space-y-4"},Rs={class:"text-sm text-gray-600"},Fs={class:"flex space-x-3 mt-4"},Ns=["disabled"],Us=["disabled"],Os={key:0,class:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"},zs={__name:"FileUpload",setup(s){const t=ce(),o=L(!1),l=L(null),r=Ee({type:"info",title:null,message:"",details:null,showProgress:!1,progress:0,progressText:"",dismissible:!1}),u=x(()=>t.uploadedFile?{name:t.getUploadedFileName,size:t.getUploadedFileSize,type:t.getUploadedFileType,duration:null}:null),p=x(()=>t.isLoading?"processing":t.uploadedFile&&!t.isLoading?"ready":null),b=x(()=>t.isLoading?50:null),w=x(()=>t.isLoading?"正在处理文件...":null),d=x(()=>null),v=x(()=>t.progressUpdates.map(I=>({timestamp:new Date().toISOString(),level:I.includes("✅")?"success":I.includes("❌")?"error":I.includes("⚠️")?"warning":"info",message:I}))),y=x(()=>t.uploadedFile&&!t.isLoading&&t.progressUpdates.some(I=>I.includes("✅"))),M=()=>{var I;(I=l.value)==null||I.click()},h=I=>{const D=I.target.files[0];D&&T(D)},P=I=>{I.preventDefault(),o.value=!1;const D=I.dataTransfer.files;D.length>0&&T(D[0])},T=I=>{if(!["video/mp4","video/avi","video/mov","video/quicktime","video/x-msvideo","audio/mp3","audio/wav","audio/mpeg","audio/x-wav"].some(J=>I.type.includes(J.split("/")[1]))){r.type="error",r.title="文件格式不支持",r.message="请选择支持的视频或音频文件格式",r.details="支持格式: MP4, AVI, MOV, MP3, WAV 等",r.dismissible=!0;return}const O=2*1024*1024*1024;if(I.size>O){r.type="error",r.title="文件过大",r.message="文件大小不能超过 2GB",r.dismissible=!0;return}t.setUploadedFile(I),r.type="success",r.title="文件选择成功",r.message=`已选择文件: ${I.name}`,r.dismissible=!0,console.log("File selected:",I)},m=()=>{t.uploadedFile&&(r.type="loading",r.title="开始处理",r.message="正在处理文件，请稍候...",r.showProgress=!0,r.progress=0,r.progressText="初始化处理",r.dismissible=!1,t.processVideoToAudio())},z=()=>{t.resetWorkflow(),l.value&&(l.value.value=""),X()},V=()=>{t.setCurrentStep(2)},X=()=>{r.message="",r.title=null,r.details=null,r.showProgress=!1,r.progress=0,r.progressText=""},F=()=>{t.progressUpdates=[]},R=I=>{r.type="success",r.title="复制成功",r.message="日志内容已复制到剪贴板",r.dismissible=!0};return(I,D)=>(n(),i(Y,null,[e("div",Ps,[D[8]||(D[8]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 1: 上传媒体文件"),e("p",{class:"text-sm text-gray-500 mt-1"},"选择视频或音频文件开始处理")],-1)),e("div",As,[e("div",{onDrop:P,onDragover:D[0]||(D[0]=_e(()=>{},["prevent"])),onDragenter:[D[1]||(D[1]=_e(()=>{},["prevent"])),D[2]||(D[2]=O=>o.value=!0)],class:H(["border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200",o.value?"border-blue-500 bg-blue-50":te(t).uploadedFile?"border-green-500 bg-green-50":"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"]),onDragleave:D[3]||(D[3]=O=>o.value=!1)},[te(t).uploadedFile?(n(),i("div",Is,[D[7]||(D[7]=e("div",{class:"text-4xl"},"✅",-1)),e("div",null,[D[6]||(D[6]=e("p",{class:"text-lg font-medium text-green-700"},"文件已选择",-1)),e("p",Rs,a(te(t).getUploadedFileName),1)])])):(n(),i("div",Ls,[D[4]||(D[4]=e("div",{class:"text-4xl"},"📁",-1)),D[5]||(D[5]=e("div",null,[e("p",{class:"text-lg font-medium text-gray-700"},"拖拽文件到此处或点击选择"),e("p",{class:"text-sm text-gray-500 mt-1"},"支持 MP4, AVI, MOV, MP3, WAV 等格式")],-1)),e("button",{onClick:M,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 📂 选择文件 ")]))],34),e("input",{ref_key:"fileInput",ref:l,type:"file",accept:"video/*,audio/*",onChange:h,class:"hidden"},null,544),te(t).uploadedFile?(n(),Z(Kt,{key:0,"file-info":u.value,"processing-status":p.value,progress:b.value,"progress-text":w.value,"error-message":d.value,"show-actions":!0,"allow-reselect":!0,"allow-remove":!1,onReselect:M},null,8,["file-info","processing-status","progress","progress-text","error-message"])):c("",!0),e("div",Fs,[e("button",{onClick:m,disabled:!te(t).uploadedFile||te(t).isLoading,class:"flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🎤 "+a(te(t).uploadedFile?"开始处理":"请先选择文件"),9,Ns),e("button",{onClick:z,disabled:!te(t).uploadedFile&&!te(t).isLoading,class:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🔄 重新选择 ",8,Us)])]),r.message?(n(),Z(pe,{key:0,type:r.type,title:r.title,message:r.message,details:r.details,"show-progress":r.showProgress,progress:r.progress,"progress-text":r.progressText,dismissible:r.dismissible,onDismiss:X},null,8,["type","title","message","details","show-progress","progress","progress-text","dismissible"])):c("",!0),te(t).progressUpdates.length>0?(n(),Z(ye,{key:1,title:"处理日志",logs:v.value,"initial-expanded":!1,"show-stats":!0,onClear:F,onCopy:R},null,8,["logs"])):c("",!0)]),y.value?(n(),i("div",Os,[e("div",{class:"flex items-center justify-between"},[D[9]||(D[9]=e("div",null,[e("h4",{class:"text-sm font-medium text-green-800"},"文件处理完成"),e("p",{class:"text-sm text-green-600 mt-1"},"可以进入下一步进行语音转文字处理")],-1)),e("button",{onClick:V,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," ➡️ 进入下一步 ")])])):c("",!0)],64))}},Ds=re(zs,[["__scopeId","data-v-e5d04b13"]]);function Te(s){if(s==null)return"未知";if(typeof s=="string")return{UNSPECIFIED:"未指定",IN_PROGRESS:"处理中",ERROR:"失败",SUCCESS:"已完成",PARTIAL_SUCCESS:"部分完成",CANCELLED:"已取消",PENDING:"等待中",UNKNOWN:"未知"}[s]||s;switch(s){case 0:return"未指定";case 1:return"处理中";case 2:return"失败";case 3:return"已完成";case 4:return"部分完成";case 5:return"已取消";case 6:return"等待中";default:return"未知"}}function js(s){switch(s.toLowerCase()){case"pending":case"等待中":return"bg-yellow-100 text-yellow-800";case"in_progress":case"processing":case"处理中":return"bg-blue-100 text-blue-800";case"completed":case"success":case"已完成":return"bg-green-100 text-green-800";case"failed":case"error":case"失败":return"bg-red-100 text-red-800";case"cancelled":case"已取消":return"bg-gray-100 text-gray-800";case"paused":case"已暂停":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}}function Bs(s){switch(s.toLowerCase()){case"pending":case"等待中":return{template:`
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `};case"in_progress":case"processing":case"处理中":return{template:`
          <svg class="animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        `};case"completed":case"success":case"已完成":return{template:`
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        `};case"failed":case"error":case"失败":return{template:`
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        `};case"cancelled":case"已取消":return{template:`
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 715.636 5.636m12.728 12.728L5.636 5.636" />
          </svg>
        `};case"paused":case"已暂停":return{template:`
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `};default:return{template:`
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `}}}const Ws={__name:"OperationStatusBadge",props:{status:{type:[Number,String],required:!0},showIcon:{type:Boolean,default:!0},customText:{type:String,default:""},size:{type:String,default:"default",validator:s=>["small","default","large"].includes(s)}},setup(s){const t=s,o=x(()=>t.customText?t.customText:typeof t.status=="number"?Te(t.status):t.status),l=x(()=>{const u={small:"text-xs",default:"text-sm",large:"text-base"}[t.size]||"text-sm",p=typeof t.status=="number"?Te(t.status):t.status,b=js(p);return`${u} ${b}`}),r=x(()=>{const u=typeof t.status=="number"?Te(t.status):t.status;return Bs(u)});return(u,p)=>(n(),i("span",{class:H(["operation-status-badge inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",l.value])},[s.showIcon?(n(),Z(Ne(r.value),{key:0,class:"h-3 w-3 mr-1"})):c("",!0),ue(" "+a(o.value),1)],2))}},Ce=re(Ws,[["__scopeId","data-v-d3187c17"]]),Vs={key:0,class:"error-detail-panel bg-white rounded-lg shadow-md border border-red-200 overflow-hidden"},Gs={class:"bg-red-50 px-4 py-3 border-b border-red-200"},qs={class:"flex items-center justify-between"},Hs={class:"flex items-center"},Ks={class:"text-sm font-medium text-red-800"},Js={class:"p-4"},Xs={class:"mb-4"},Qs={class:"text-sm text-gray-700"},Ys={key:0,class:"mb-4"},Zs={class:"text-xs bg-gray-100 px-2 py-1 rounded font-mono"},eo={key:1,class:"mb-4"},to={class:"bg-gray-50 p-2 rounded border border-gray-200"},so={class:"font-medium"},oo={key:2},ro={key:0,class:"bg-gray-50 p-2 rounded border border-gray-200"},no={class:"text-xs font-mono text-gray-600 whitespace-pre-wrap"},lo={key:0,class:"px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2"},io=["onClick"],ao={__name:"ErrorDetailPanel",props:{errorDetail:{type:Object,default:null},errorMessage:{type:String,default:"发生未知错误"},title:{type:String,default:""},showClose:{type:Boolean,default:!0},actions:{type:Array,default:()=>[]}},emits:["close","action"],setup(s,{emit:t}){const o=s,l=t,r=L(!1),u=x(()=>o.errorDetail&&o.errorDetail.context&&Object.keys(o.errorDetail.context).length>0),p=()=>{r.value=!r.value},b=w=>{l("action",w)};return(w,d)=>s.errorDetail?(n(),i("div",Vs,[e("div",Gs,[e("div",qs,[e("div",Hs,[d[1]||(d[1]=e("svg",{class:"h-5 w-5 text-red-600 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})],-1)),e("h3",Ks,a(s.title||"处理过程中发生错误"),1)]),s.showClose?(n(),i("button",{key:0,onClick:d[0]||(d[0]=v=>w.$emit("close")),class:"text-red-400 hover:text-red-600 focus:outline-none"},d[2]||(d[2]=[e("svg",{class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):c("",!0)])]),e("div",Js,[e("div",Xs,[d[3]||(d[3]=e("p",{class:"text-sm font-medium text-gray-800 mb-1"},"错误信息",-1)),e("p",Qs,a(s.errorDetail.userMessage||s.errorMessage),1)]),s.errorDetail.errorCode?(n(),i("div",Ys,[d[4]||(d[4]=e("p",{class:"text-xs font-medium text-gray-500 mb-1"},"错误代码",-1)),e("code",Zs,a(s.errorDetail.errorCode),1)])):c("",!0),u.value?(n(),i("div",eo,[d[5]||(d[5]=e("p",{class:"text-xs font-medium text-gray-500 mb-1"},"上下文信息",-1)),e("div",to,[(n(!0),i(Y,null,ee(s.errorDetail.context,(v,y)=>(n(),i("div",{key:y,class:"text-xs text-gray-600"},[e("span",so,a(y)+":",1),ue(" "+a(v),1)]))),128))])])):c("",!0),s.errorDetail.technicalMessage&&r.value?(n(),i("div",oo,[e("div",{onClick:p,class:"flex items-center text-xs text-gray-500 cursor-pointer hover:text-gray-700 mb-1"},[d[7]||(d[7]=e("span",{class:"font-medium"},"技术详情",-1)),(n(),i("svg",{class:H(["h-4 w-4 ml-1 transition-transform",{"transform rotate-180":r.value}]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},d[6]||(d[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))]),r.value?(n(),i("div",ro,[e("pre",no,a(s.errorDetail.technicalMessage),1)])):c("",!0)])):c("",!0)]),s.actions&&s.actions.length>0?(n(),i("div",lo,[(n(!0),i(Y,null,ee(s.actions,v=>(n(),i("button",{key:v.key,onClick:y=>b(v),class:H(["px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-offset-2",v.primary?"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500":"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500"])},a(v.label),11,io))),128))])):c("",!0)])):c("",!0)}},Me=re(ao,[["__scopeId","data-v-45fbeab2"]]),uo={class:"workflow-progress"},co={class:"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden"},go={class:"bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-200"},mo={class:"flex items-center justify-between"},po={class:"flex items-center"},fo={class:"text-sm font-medium text-blue-800"},vo={class:"flex items-center"},bo={class:"p-4"},xo={class:"mb-4"},ho={class:"flex items-center justify-between text-xs mb-1"},yo={class:"text-gray-600"},So={class:"w-full bg-gray-200 rounded-full h-2"},wo={key:0,class:"mb-4"},_o={class:"flex items-center justify-between mb-1"},ko={class:"flex items-center"},$o={class:"text-xs text-gray-500"},To={class:"text-sm text-gray-800 mb-1"},Co={key:0,class:"text-xs text-gray-600"},Eo={key:2,class:"mt-4"},Mo={class:"space-y-2"},Po={class:"flex items-center"},Ao={class:"flex-shrink-0 mr-2"},Lo={key:0,class:"h-4 w-4 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Io={key:1,class:"h-4 w-4 text-blue-500 animate-spin",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Ro={key:2,class:"h-4 w-4 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Fo={key:3,class:"h-4 w-4 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},No={key:0,class:"text-xs text-gray-500"},Uo={key:0,class:"px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2"},Oo=["onClick","disabled"],zo={__name:"WorkflowProgress",props:{title:{type:String,default:""},showClose:{type:Boolean,default:!1},currentWorkflow:{type:Object,default:null},overallProgress:{type:Number,default:0},currentStage:{type:Object,default:null},stages:{type:Array,default:()=>[]},showStages:{type:Boolean,default:!0},errorDetail:{type:Object,default:null},errorTitle:{type:String,default:"处理过程中发生错误"},errorActions:{type:Array,default:()=>[]},actions:{type:Array,default:()=>[]},theme:{type:String,default:"blue",validator:s=>["blue","green","indigo","purple","red","yellow","orange","pink","gray"].includes(s)}},emits:["close","action","error-action"],setup(s,{emit:t}){const o=s,l=t,r=x(()=>o.errorDetail||o.currentWorkflow&&o.currentWorkflow.status==="failed"||o.currentStage&&o.currentStage.status==="failed"),u=x(()=>{if(r.value)return"bg-red-500";const w={blue:"bg-blue-600",green:"bg-green-600",indigo:"bg-indigo-600",purple:"bg-purple-600",red:"bg-red-600",yellow:"bg-yellow-600",orange:"bg-orange-600",pink:"bg-pink-600",gray:"bg-gray-600"};return w[o.theme]||w.blue}),p=w=>{l("action",w)},b=w=>{l("error-action",w)};return(w,d)=>(n(),i("div",uo,[e("div",co,[e("div",go,[e("div",mo,[e("div",po,[d[1]||(d[1]=e("svg",{class:"h-5 w-5 text-blue-600 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1)),e("h3",fo,a(s.title||"工作流进度"),1)]),e("div",vo,[s.currentWorkflow?(n(),Z(Ce,{key:0,status:s.currentWorkflow.status,size:"small"},null,8,["status"])):c("",!0),s.showClose?(n(),i("button",{key:1,onClick:d[0]||(d[0]=v=>w.$emit("close")),class:"ml-2 text-gray-400 hover:text-gray-600 focus:outline-none"},d[2]||(d[2]=[e("svg",{class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):c("",!0)])])]),e("div",bo,[e("div",xo,[e("div",ho,[d[3]||(d[3]=e("span",{class:"font-medium text-gray-700"},"总体进度",-1)),e("span",yo,a(Math.round(s.overallProgress))+"%",1)]),e("div",So,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",u.value]),style:be({width:`${Math.min(100,Math.max(0,s.overallProgress))}%`})},null,6)])]),s.currentStage?(n(),i("div",wo,[e("div",_o,[e("div",ko,[d[4]||(d[4]=e("span",{class:"text-xs font-medium text-gray-700"},"当前阶段",-1)),s.currentStage.status!==void 0?(n(),Z(Ce,{key:0,status:s.currentStage.status,size:"small",class:"ml-2"},null,8,["status"])):c("",!0)]),e("span",$o,a(s.currentStage.progress)+"%",1)]),e("div",To,a(s.currentStage.name||"处理中"),1),s.currentStage.message?(n(),i("div",Co,a(s.currentStage.message),1)):c("",!0)])):c("",!0),r.value&&s.errorDetail?(n(),Z(Me,{key:1,errorDetail:s.errorDetail,title:s.errorTitle,showClose:!1,actions:s.errorActions,onAction:b,class:"mb-4"},null,8,["errorDetail","title","actions"])):c("",!0),s.showStages&&s.stages.length>0?(n(),i("div",Eo,[d[9]||(d[9]=e("h4",{class:"text-xs font-medium text-gray-700 mb-2"},"处理阶段",-1)),e("div",Mo,[(n(!0),i(Y,null,ee(s.stages,(v,y)=>(n(),i("div",{key:y,class:H(["flex items-center justify-between p-2 rounded-md",{"bg-blue-50 border border-blue-100":v.isActive,"bg-gray-50 border border-gray-100":!v.isActive}])},[e("div",Po,[e("div",Ao,[v.status==="completed"?(n(),i("svg",Lo,d[5]||(d[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))):v.isActive?(n(),i("svg",Io,d[6]||(d[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):v.status==="failed"?(n(),i("svg",Ro,d[7]||(d[7]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]))):(n(),i("svg",Fo,d[8]||(d[8]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),e("div",null,[e("div",{class:H(["text-xs font-medium",{"text-blue-700":v.isActive,"text-gray-700":!v.isActive}])},a(v.name),3),v.message?(n(),i("div",No,a(v.message),1)):c("",!0)])]),v.status?(n(),Z(Ce,{key:0,status:v.status,size:"small"},null,8,["status"])):c("",!0)],2))),128))])])):c("",!0)]),s.actions&&s.actions.length>0?(n(),i("div",Uo,[(n(!0),i(Y,null,ee(s.actions,v=>(n(),i("button",{key:v.key,onClick:y=>p(v),disabled:v.disabled,class:H(["px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-offset-2",v.primary?"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300":"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500 disabled:bg-gray-100 disabled:text-gray-400"])},a(v.label),11,Oo))),128))])):c("",!0)])]))}},$e=re(zo,[["__scopeId","data-v-61cffe3a"]]),Do={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},jo={key:2,class:"current-actions mb-6"},Bo={class:"flex space-x-3"},Wo=["disabled"],Vo={key:3,class:"result-section bg-green-50 border border-green-200 rounded-lg p-4 mb-6"},Go={class:"flex items-start"},qo={class:"ml-3"},Ho={class:"mt-2 text-sm text-green-700"},Ko={class:"mt-1"},Jo={class:"font-medium"},Xo={key:4,class:"log-section mt-6"},Qo={class:"flex items-center justify-between mb-2"},Yo={key:0,class:"bg-gray-50 border border-gray-200 rounded-lg p-3 max-h-60 overflow-y-auto"},Zo={class:"text-gray-400 mr-2"},er={__name:"VideoToAudioStep",setup(s){const t=ce(),o=x(()=>t.isLoading),l=x(()=>t.uploadedFile),r=x(()=>t.videoToAudioResult),u=x(()=>t.videoToAudioError);x(()=>t.videoToAudioProgress);const p=L(!1),b=L(null),w=L(0),d=L(null),v=L([]),y=L(null),M=L("音频提取过程中发生错误"),h=L([{key:"retry",label:"重试",primary:!0},{key:"skip",label:"跳过"}]),P=x(()=>{if(!b.value)return[];const O=[];return b.value.status==="in_progress"?O.push({key:"cancel",label:"取消",primary:!1}):b.value.status==="completed"?O.push({key:"next",label:"下一步",primary:!0}):b.value.status==="failed"&&(O.push({key:"retry",label:"重试",primary:!0}),O.push({key:"skip",label:"跳过",primary:!1})),O}),T=x(()=>r.value?"音频文件已成功从视频中提取，可以进入下一步进行语音转文字处理":""),m=x(()=>r.value?r.value.split("/").pop()||r.value:""),z=x(()=>t.progressUpdates.map(O=>({timestamp:new Date().toISOString(),level:O.includes("✅")?"success":O.includes("❌")?"error":O.includes("⚠️")?"warning":"info",message:O}))),V=async()=>{if(l.value)try{y.value=null,b.value={id:"videoToAudio",name:"视频转音频",status:"in_progress"},v.value=[{id:"init",name:"初始化",status:"completed",isActive:!1,progress:100,message:"准备处理视频文件"},{id:"extract",name:"提取音频",status:"in_progress",isActive:!0,progress:0,message:"正在从视频中提取音频轨道"},{id:"save",name:"保存文件",status:"pending",isActive:!1,progress:0,message:"等待保存音频文件"}],d.value=v.value[1];const O=K=>{if(K.percentage!==void 0){if(w.value=K.percentage,d.value&&(d.value.progress=K.percentage),K.stage){const j=v.value.find(U=>U.id===K.stage);j&&(v.value.forEach(U=>{U.id!==K.stage&&(U.isActive=!1,U.status==="in_progress"&&(U.status="completed"))}),j.isActive=!0,j.status="in_progress",j.message=K.message||j.message,d.value=j)}K.message&&d.value&&(d.value.message=K.message)}},J=await t.processVideoToAudio({onProgress:O});return b.value.status="completed",w.value=100,v.value.forEach(K=>{K.status="completed",K.progress=100,K.isActive=!1}),J}catch(O){b.value.status="failed",d.value&&(d.value.status="failed",d.value.message=O.message||"处理失败"),y.value={userMessage:O.message||"视频转音频处理失败",errorCode:O.code||"PROCESSING_ERROR",technicalMessage:O.stack||O.toString(),context:{fileName:l.value.name,fileSize:`${(l.value.size/1024/1024).toFixed(2)} MB`,fileType:l.value.type}},console.error("[VideoToAudioStep] Error:",O)}},X=O=>{O.key==="retry"?V():O.key==="next"||O.key==="skip"?D():O.key==="cancel"&&(t.cancelCurrentOperation(),b.value.status="cancelled",d.value&&(d.value.status="cancelled",d.value.message="操作已取消"))},F=O=>{X(O)},R=O=>{O.key==="goto-upload"&&t.setCurrentStep(1)},I=()=>{p.value=!p.value},D=()=>{t.setCurrentStep(3)};return he(()=>{var O,J;r.value&&(b.value={id:"videoToAudio",name:"视频转音频",status:"completed"},w.value=100,v.value=[{id:"init",name:"初始化",status:"completed",isActive:!1,progress:100,message:"准备处理视频文件"},{id:"extract",name:"提取音频",status:"completed",isActive:!1,progress:100,message:"音频轨道提取完成"},{id:"save",name:"保存文件",status:"completed",isActive:!1,progress:100,message:"音频文件已保存"}]),u.value&&(y.value={userMessage:u.value,errorCode:"PROCESSING_ERROR",technicalMessage:u.value,context:{fileName:(O=l.value)==null?void 0:O.name,fileSize:l.value?`${(l.value.size/1024/1024).toFixed(2)} MB`:"Unknown",fileType:(J=l.value)==null?void 0:J.type}},b.value&&(b.value.status="failed"))}),(O,J)=>(n(),i("div",Do,[J[4]||(J[4]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 2: 视频转音频"),e("p",{class:"text-sm text-gray-500 mt-1"},"从视频文件中提取音频用于后续处理")],-1)),l.value?c("",!0):(n(),Z(pe,{key:0,type:"warning",title:"需要先上传文件",message:"请先在步骤1中上传视频或音频文件",actions:[{key:"goto-upload",label:"前往上传文件",icon:"📁",primary:!0}],onAction:R})),l.value?(n(),Z($e,{key:1,title:"音频提取进度","current-workflow":b.value,"overall-progress":w.value,"current-stage":d.value,stages:v.value,"error-detail":y.value,"error-title":M.value,"error-actions":h.value,actions:P.value,onAction:X,onErrorAction:F,class:"mb-6"},null,8,["current-workflow","overall-progress","current-stage","stages","error-detail","error-title","error-actions","actions"])):c("",!0),l.value&&!o.value&&!b.value?(n(),i("div",jo,[e("div",Bo,[e("button",{onClick:V,disabled:o.value||r.value,class:"flex-1 px-6 py-3 text-white bg-purple-600 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🎵 "+a(r.value?"音频已提取":"开始提取音频"),9,Wo),r.value?(n(),i("button",{key:0,onClick:D,class:"px-6 py-3 text-purple-700 bg-purple-100 rounded-lg hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):c("",!0)])])):c("",!0),r.value&&!o.value?(n(),i("div",Vo,[e("div",Go,[J[2]||(J[2]=e("div",{class:"flex-shrink-0 mt-0.5"},[e("svg",{class:"h-5 w-5 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)),e("div",qo,[J[1]||(J[1]=e("h4",{class:"text-sm font-medium text-green-800"},"音频提取成功",-1)),e("div",Ho,[e("p",null,a(T.value),1),e("p",Ko,[J[0]||(J[0]=ue("输出文件: ")),e("span",Jo,a(m.value),1)])]),e("div",{class:"mt-3"},[e("button",{onClick:D,class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," 继续下一步 ")])])])])):c("",!0),z.value.length>0?(n(),i("div",Xo,[e("div",Qo,[J[3]||(J[3]=e("h4",{class:"text-sm font-medium text-gray-700"},"处理日志",-1)),e("button",{onClick:I,class:"text-xs text-gray-500 hover:text-gray-700 focus:outline-none"},a(p.value?"隐藏日志":"显示日志"),1)]),p.value?(n(),i("div",Yo,[(n(!0),i(Y,null,ee(z.value,(K,j)=>(n(),i("div",{key:j,class:H(["text-xs font-mono py-1 border-b border-gray-100 last:border-0",K.level==="error"?"text-red-600":K.level==="warning"?"text-yellow-600":K.level==="success"?"text-green-600":"text-gray-600"])},[e("span",Zo,a(K.timestamp.split("T")[1].split(".")[0]),1),e("span",null,a(K.message),1)],2))),128))])):c("",!0)])):c("",!0)]))}},tr=re(er,[["__scopeId","data-v-592b9169"]]),sr={key:0,class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4"},or={class:"flex items-center justify-between mb-2"},rr={class:"flex items-center space-x-2"},nr={class:"text-sm font-medium text-blue-800"},lr={key:0,class:"text-xs text-blue-600 font-mono bg-blue-100 px-2 py-1 rounded"},ir={class:"text-xs text-blue-600"},ar={class:"mb-2"},ur={class:"flex items-center justify-between"},dr={class:"text-sm text-blue-700"},cr={class:"text-sm text-blue-600"},gr={class:"w-full bg-blue-200 rounded-full h-2 mt-1"},mr={class:"text-sm text-blue-700"},pr={key:0,class:"mt-3 p-3 bg-red-100 border border-red-200 rounded"},fr={class:"text-red-800 font-medium text-sm"},vr={key:0,class:"text-red-600 text-xs mt-1"},br={key:1,class:"text-red-500 text-xs mt-1"},xr={key:1,class:"bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4"},hr={class:"flex items-center justify-between"},yr={class:"flex items-center space-x-2"},Sr={class:"text-sm font-medium text-gray-700"},wr={key:0,class:"text-xs text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded"},_r={class:"flex items-center space-x-1"},kr={key:0,class:"text-green-600"},$r={key:1,class:"text-red-600"},Tr={key:2,class:"text-yellow-600"},Cr={class:"text-xs text-gray-600"},Er={key:0,class:"text-sm text-gray-600 mt-1"},Mr={__name:"ProcessStateDisplay",props:{processState:{type:Object,default:null}},setup(s){const t=L(!1),o=l=>({SUCCESS:"成功",FAILURE:"失败",PARTIAL_SUCCESS:"部分成功",IN_PROGRESS:"进行中",UNSPECIFIED:"未知状态"})[l]||l;return(l,r)=>s.processState&&s.processState.isActive?(n(),i("div",sr,[e("div",or,[e("div",rr,[r[1]||(r[1]=e("div",{class:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"},null,-1)),e("span",nr,a(s.processState.workflowType||"处理中"),1),s.processState.traceId?(n(),i("span",lr," ID: "+a(s.processState.traceId.slice(-8)),1)):c("",!0)]),e("div",ir,a(o(s.processState.status)),1)]),e("div",ar,[e("div",ur,[e("span",dr,a(s.processState.currentStageName),1),e("span",cr,a(s.processState.overallPercentage)+"%",1)]),e("div",gr,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:be({width:`${s.processState.overallPercentage}%`})},null,4)])]),e("div",mr,a(s.processState.currentMessage),1),s.processState.errorDetail?(n(),i("div",pr,[e("div",fr,a(s.processState.errorDetail.userMessage),1),s.processState.errorDetail.errorCode?(n(),i("div",vr," 错误代码: "+a(s.processState.errorDetail.errorCode),1)):c("",!0),s.processState.errorDetail.technicalMessage&&t.value?(n(),i("div",br," 技术详情: "+a(s.processState.errorDetail.technicalMessage),1)):c("",!0),s.processState.errorDetail.technicalMessage?(n(),i("button",{key:2,onClick:r[0]||(r[0]=u=>t.value=!t.value),class:"text-red-600 text-xs mt-1 hover:underline"},a(t.value?"隐藏":"显示")+"技术详情 ",1)):c("",!0)])):c("",!0)])):s.processState&&!s.processState.isActive&&s.processState.status?(n(),i("div",xr,[e("div",hr,[e("div",yr,[e("span",Sr,a(s.processState.workflowType||"最近处理"),1),s.processState.traceId?(n(),i("span",wr," ID: "+a(s.processState.traceId.slice(-8)),1)):c("",!0)]),e("div",_r,[s.processState.status==="SUCCESS"?(n(),i("span",kr,"✅")):s.processState.status==="FAILURE"?(n(),i("span",$r,"❌")):s.processState.status==="PARTIAL_SUCCESS"?(n(),i("span",Tr,"⚠️")):c("",!0),e("span",Cr,a(o(s.processState.status)),1)])]),s.processState.currentMessage?(n(),i("div",Er,a(s.processState.currentMessage),1)):c("",!0)])):c("",!0)}},Ue=re(Mr,[["__scopeId","data-v-23371e3d"]]),Pr={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},Ar={key:2,class:"audio-source-selection mb-6"},Lr={class:"bg-blue-50 rounded-lg border border-blue-200 p-4"},Ir={class:"flex items-center mb-4"},Rr={for:"usePreviousAudio",class:"ml-2 block text-sm text-gray-700"},Fr={key:0,class:"mb-4"},Nr={key:3,class:"transcription-settings mb-6"},Ur={class:"bg-gray-50 rounded-lg border border-gray-200 p-4"},Or={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},zr={class:"md:col-span-2"},Dr={class:"flex items-center mb-2"},jr={key:0,class:"pl-4 border-l-2 border-blue-100 space-y-3 mt-2"},Br={key:4,class:"transcription-actions mb-6"},Wr={class:"flex space-x-3"},Vr=["disabled"],Gr={key:5,class:"result-section bg-green-50 border border-green-200 rounded-lg p-4 mb-6"},qr={class:"flex items-start"},Hr={class:"ml-3"},Kr={class:"mt-2 text-sm text-green-700"},Jr={key:6,class:"log-section mt-6"},Xr={class:"flex items-center justify-between mb-2"},Qr={key:0,class:"bg-gray-50 border border-gray-200 rounded-lg p-3 max-h-60 overflow-y-auto"},Yr={class:"text-gray-400 mr-2"},Zr={__name:"AudioToTextStep",setup(s){const t=ce(),o=x(()=>t.isLoading),l=x(()=>t.uploadedFile),r=x(()=>t.videoToAudioResult),u=x(()=>t.audioToTextResult),p=x(()=>t.audioToTextError);x(()=>t.audioToTextProgress);const b=L(!1),w=L(!1),d=L(null),v=L(0),y=L(null),M=L([]),h=L(null),P=L("语音识别过程中发生错误"),T=L([{key:"retry",label:"重试",primary:!0},{key:"skip",label:"跳过"}]),m=L({language:"zh",model:"whisper-1",segmentLength:5}),z=x(()=>{if(!d.value)return[];const $=[];return d.value.status==="in_progress"?$.push({key:"cancel",label:"取消",primary:!1}):d.value.status==="completed"?$.push({key:"next",label:"下一步",primary:!0}):d.value.status==="failed"&&($.push({key:"retry",label:"重试",primary:!0}),$.push({key:"skip",label:"跳过",primary:!1})),$}),V=x(()=>r.value||l.value&&l.value.type&&l.value.type.startsWith("audio/")),X=x(()=>I.value?!!r.value:!!l.value),F=x(()=>{var $;return I.value&&r.value?r.value:($=l.value)==null?void 0:$.path}),R=x(()=>r.value?r.value.split("/").pop()||r.value:""),I=x({get:()=>t.usePreviousAudioForTranscription,set:$=>{t.setUsePreviousAudioForTranscription($)}}),D=x(()=>t.editableSegments?t.editableSegments.length:0),O=x(()=>t.progressUpdates.map($=>{var E,N,_;return typeof $=="object"&&$.stageName?{timestamp:$.timestamp||Date.now(),level:$.isError?"error":$.status==="SUCCESS"?"success":$.status==="FAILURE"?"error":$.status==="PARTIAL_SUCCESS"?"warning":(E=$.message)!=null&&E.includes("✅")?"success":(N=$.message)!=null&&N.includes("❌")?"error":(_=$.message)!=null&&_.includes("⚠️")?"warning":"info",message:`[${$.stageName}] ${$.message}${$.traceId?` (${$.traceId.slice(-8)})`:""}`,traceId:$.traceId,status:$.status,errorDetail:$.errorDetail}:{timestamp:Date.now(),level:$.includes("✅")?"success":$.includes("❌")?"error":$.includes("⚠️")?"warning":"info",message:$}})),J=$=>{const E=$.target.files[0];E&&(t.setUploadedFile(E),t.setUsePreviousAudioForTranscription(!1))},K=async()=>{var $;if(X.value)try{h.value=null,d.value={id:"audioToText",name:"音频转文字",status:"in_progress"},M.value=[{id:"init",name:"初始化",status:"completed",isActive:!1,progress:100,message:"准备处理音频文件"},{id:"transcribe",name:"语音识别",status:"in_progress",isActive:!0,progress:0,message:"正在进行语音识别"},{id:"segment",name:"分段处理",status:"pending",isActive:!1,progress:0,message:"等待处理文本分段"}],y.value=M.value[1];const E=_=>{if(_.percentage!==void 0){if(v.value=_.percentage,y.value&&(y.value.progress=_.percentage),_.stage){const se=M.value.find(ne=>ne.id===_.stage);se&&(M.value.forEach(ne=>{ne.id!==_.stage&&(ne.isActive=!1,ne.status==="in_progress"&&(ne.status="completed"))}),se.isActive=!0,se.status="in_progress",se.message=_.message||se.message,y.value=se)}_.message&&y.value&&(y.value.message=_.message)}},N=await t.processAudioToText({audioPath:F.value,settings:m.value,onProgress:E});return d.value.status="completed",v.value=100,M.value.forEach(_=>{_.status="completed",_.progress=100,_.isActive=!1}),N}catch(E){d.value.status="failed",y.value&&(y.value.status="failed",y.value.message=E.message||"处理失败"),h.value={userMessage:E.message||"语音识别处理失败",errorCode:E.code||"PROCESSING_ERROR",technicalMessage:E.stack||E.toString(),context:{audioSource:I.value?"上一步提取的音频":"用户上传的音频",fileName:I.value?R.value:($=l.value)==null?void 0:$.name,settings:JSON.stringify(m.value)}},console.error("[AudioToTextStep] Error:",E)}},j=$=>{$.key==="retry"?K():$.key==="next"||$.key==="skip"?S():$.key==="cancel"&&(t.cancelCurrentOperation(),d.value.status="cancelled",y.value&&(y.value.status="cancelled",y.value.message="操作已取消"))},U=$=>{j($)},k=$=>{$.key==="goto-video-audio"&&t.setCurrentStep(2)},f=()=>{w.value=!w.value},S=()=>{t.setCurrentStep(4)};return he(()=>{var $;u.value&&(d.value={id:"audioToText",name:"音频转文字",status:"completed"},v.value=100,M.value=[{id:"init",name:"初始化",status:"completed",isActive:!1,progress:100,message:"准备处理音频文件"},{id:"transcribe",name:"语音识别",status:"completed",isActive:!1,progress:100,message:"语音识别完成"},{id:"segment",name:"分段处理",status:"completed",isActive:!1,progress:100,message:"文本分段处理完成"}]),p.value&&(h.value={userMessage:p.value,errorCode:"PROCESSING_ERROR",technicalMessage:p.value,context:{audioSource:I.value?"上一步提取的音频":"用户上传的音频",fileName:I.value?R.value:($=l.value)==null?void 0:$.name}},d.value&&(d.value.status="failed")),r.value&&t.setUsePreviousAudioForTranscription(!0)}),($,E)=>(n(),i("div",Pr,[E[18]||(E[18]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 3: 音频转文字"),e("p",{class:"text-sm text-gray-500 mt-1"},"使用语音识别将音频转换为文本")],-1)),ke(Ue,{"process-state":te(t).currentProcessState},null,8,["process-state"]),V.value?c("",!0):(n(),Z(pe,{key:0,type:"warning",title:"需要音频文件",message:"请先完成前面的步骤获取音频文件",actions:[{key:"goto-video-audio",label:"前往音频提取",icon:"🎵",primary:!0}],onAction:k})),V.value?(n(),Z($e,{key:1,title:"语音识别进度","current-workflow":d.value,"overall-progress":v.value,"current-stage":y.value,stages:M.value,"error-detail":h.value,"error-title":P.value,"error-actions":T.value,actions:z.value,onAction:j,onErrorAction:U,class:"mb-6"},null,8,["current-workflow","overall-progress","current-stage","stages","error-detail","error-title","error-actions","actions"])):c("",!0),V.value&&!o.value&&!d.value?(n(),i("div",Ar,[e("div",Lr,[E[6]||(E[6]=e("h4",{class:"text-sm font-medium text-blue-800 mb-2"},"音频源选择",-1)),e("div",Ir,[q(e("input",{id:"usePreviousAudio","onUpdate:modelValue":E[0]||(E[0]=N=>I.value=N),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[ae,I.value]]),e("label",Rr," 使用上一步提取的音频 ("+a(R.value)+") ",1)]),I.value?c("",!0):(n(),i("div",Fr,[E[5]||(E[5]=e("label",{for:"audioFile",class:"block text-sm font-medium text-gray-700 mb-1"}," 或选择其他音频文件: ",-1)),e("input",{type:"file",id:"audioFile",onChange:J,accept:"audio/*",class:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"},null,32)]))])])):c("",!0),V.value&&!o.value&&!d.value?(n(),i("div",Nr,[e("div",Ur,[E[14]||(E[14]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"转录设置",-1)),e("div",Or,[e("div",null,[E[8]||(E[8]=e("label",{for:"language",class:"block text-sm font-medium text-gray-700 mb-1"}," 音频语言 ",-1)),q(e("select",{id:"language","onUpdate:modelValue":E[1]||(E[1]=N=>m.value.language=N),class:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},E[7]||(E[7]=[e("option",{value:"zh"},"中文",-1),e("option",{value:"en"},"英语",-1),e("option",{value:"auto"},"自动检测",-1)]),512),[[le,m.value.language]])]),e("div",null,[E[10]||(E[10]=e("label",{for:"model",class:"block text-sm font-medium text-gray-700 mb-1"}," 识别模型 ",-1)),q(e("select",{id:"model","onUpdate:modelValue":E[2]||(E[2]=N=>m.value.model=N),class:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"},E[9]||(E[9]=[e("option",{value:"whisper-1"},"Whisper (标准)",-1),e("option",{value:"whisper-large"},"Whisper Large (高精度)",-1)]),512),[[le,m.value.model]])]),e("div",zr,[e("div",Dr,[e("button",{onClick:E[3]||(E[3]=N=>b.value=!b.value),class:"text-sm text-blue-600 hover:text-blue-800 focus:outline-none flex items-center"},[(n(),i("svg",{class:H(["h-4 w-4 mr-1 transition-transform",{"transform rotate-90":b.value}]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},E[11]||(E[11]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2)),E[12]||(E[12]=ue(" 高级设置 "))])]),b.value?(n(),i("div",jr,[e("div",null,[E[13]||(E[13]=e("label",{for:"segmentLength",class:"block text-sm font-medium text-gray-700 mb-1"}," 分段长度 (秒) ",-1)),q(e("input",{id:"segmentLength","onUpdate:modelValue":E[4]||(E[4]=N=>m.value.segmentLength=N),type:"number",min:"1",max:"30",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ve,m.value.segmentLength,void 0,{number:!0}]])])])):c("",!0)])])])])):c("",!0),V.value&&!o.value&&!d.value?(n(),i("div",Br,[e("div",Wr,[e("button",{onClick:K,disabled:!X.value||u.value,class:"flex-1 px-6 py-3 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🎙️ "+a(u.value?"已完成转录":"开始语音转文字"),9,Vr),u.value?(n(),i("button",{key:0,onClick:S,class:"px-6 py-3 text-indigo-700 bg-indigo-100 rounded-lg hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):c("",!0)])])):c("",!0),u.value&&!o.value?(n(),i("div",Gr,[e("div",qr,[E[16]||(E[16]=e("div",{class:"flex-shrink-0 mt-0.5"},[e("svg",{class:"h-5 w-5 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)),e("div",Hr,[E[15]||(E[15]=e("h4",{class:"text-sm font-medium text-green-800"},"转录完成",-1)),e("div",Kr,[e("p",null,"音频已成功转录为文本，共识别 "+a(D.value)+" 个片段",1)]),e("div",{class:"mt-3"},[e("button",{onClick:S,class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," 继续下一步 ")])])])])):c("",!0),O.value.length>0?(n(),i("div",Jr,[e("div",Xr,[E[17]||(E[17]=e("h4",{class:"text-sm font-medium text-gray-700"},"处理日志",-1)),e("button",{onClick:f,class:"text-xs text-gray-500 hover:text-gray-700 focus:outline-none"},a(w.value?"隐藏日志":"显示日志"),1)]),w.value?(n(),i("div",Qr,[(n(!0),i(Y,null,ee(O.value,(N,_)=>(n(),i("div",{key:_,class:H(["text-xs font-mono py-1 border-b border-gray-100 last:border-0",N.level==="error"?"text-red-600":N.level==="warning"?"text-yellow-600":N.level==="success"?"text-green-600":"text-gray-600"])},[e("span",Yr,a(N.timestamp.split("T")[1].split(".")[0]),1),e("span",null,a(N.message),1)],2))),128))])):c("",!0)])):c("",!0)]))}},en=re(Zr,[["__scopeId","data-v-1bbc0aa4"]]),tn={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},sn={key:1,class:"mb-6"},on={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},rn={class:"flex items-center justify-between mb-3"},nn={class:"text-xs text-blue-600"},ln={class:"bg-white border border-blue-200 rounded-md p-3 max-h-32 overflow-y-auto"},an={class:"text-gray-700 text-sm leading-relaxed"},un={key:2,class:"current-actions mb-6"},dn={class:"mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200"},cn={class:"flex items-center justify-between"},gn={class:"flex items-center space-x-2"},mn={class:"flex items-center cursor-pointer"},pn={class:"text-xs text-gray-500 ml-2"},fn={class:"text-xs text-gray-400"},vn={class:"flex space-x-3"},bn=["disabled"],xn={key:3,class:"mb-6"},hn={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},yn={class:"flex items-center justify-between mb-3"},Sn={class:"text-xs text-yellow-600"},wn={class:"mb-3"},_n={class:"flex items-center justify-between text-sm mb-1"},kn={class:"text-yellow-700"},$n={class:"w-full bg-yellow-200 rounded-full h-2"},Tn={class:"text-sm text-yellow-600"},Cn={key:4,class:"mb-6"},En={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Mn={class:"text-sm text-green-700 mb-3"},Pn={class:"bg-white rounded-md p-3 border border-green-200"},An={class:"grid grid-cols-2 gap-4 text-sm"},Ln={class:"text-gray-800 font-medium ml-1"},In={class:"text-gray-800 font-medium ml-1"},Rn={__name:"GenerateSubtitlesStep",setup(s){const t=ce(),o=L(0),l=L(""),r=L(!1),u=x(()=>t.isLoading),p=x(()=>t.audioToTextResult&&t.audioToTextResult.transcript),b=x(()=>t.generatedSubtitles&&t.generatedSubtitles.length>0),w=x(()=>t.generatedSubtitles?t.generatedSubtitles.length:0),d=x(()=>{if(!p.value)return"";const F=t.audioToTextResult.transcript;return F.length>200?F.substring(0,200)+"...":F}),v=x(()=>p.value?t.audioToTextResult.transcript.length:0),y=x(()=>b.value?`成功将转录文本组成 ${w.value} 个字幕句子，可以进入下一步进行优化`:""),M=x(()=>{if(!b.value)return 0;const F=t.generatedSubtitles.reduce((R,I)=>R+(I.text?I.text.length:0),0);return Math.round(F/w.value)}),h=x(()=>t.progressUpdates.map(F=>({timestamp:new Date().toISOString(),level:F.includes("✅")?"success":F.includes("❌")?"error":F.includes("⚠️")?"warning":"info",message:F}))),P=F=>F.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),T=async()=>{if(!p.value)return;o.value=0,l.value="准备组成句子...";const F=I=>{console.log("[GenerateSubtitles] Progress update:",I),I.percentage!==void 0&&(o.value=I.percentage),I.message&&(l.value=I.message),I.isError&&(l.value=`错误: ${I.errorMessage||I.message}`)},R=window.electronAPI.onProgressUpdate(F);try{await t.generateSubtitles(r.value),o.value=100,l.value="句子组成完成！"}catch(I){console.error("生成字幕时出错:",I),l.value=`生成失败: ${I.message}`}finally{R()}},m=F=>{F.key==="goto-transcription"&&t.setCurrentStep(2)},z=()=>{t.setCurrentStep(5)},V=()=>{t.progressUpdates=[]},X=F=>{console.log("日志已复制:",F)};return(F,R)=>(n(),i("div",tn,[R[9]||(R[9]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 4: 组成句子"),e("p",{class:"text-sm text-gray-500 mt-1"},"将转录文本组成带时间戳的字幕句子")],-1)),p.value?c("",!0):(n(),Z(pe,{key:0,type:"warning",title:"需要先完成音频转录",message:"请先完成语音转文字处理，获取转录文本后再进行句子组成",actions:[{key:"goto-transcription",label:"前往语音转文字",icon:"🎤",primary:!0}],onAction:m})),p.value?(n(),i("div",sn,[e("div",on,[e("div",rn,[R[1]||(R[1]=e("h4",{class:"text-sm font-medium text-blue-800"},"📝 转录文本预览",-1)),e("span",nn,a(v.value)+" 字符",1)]),e("div",ln,[e("p",an,a(d.value),1)])])])):c("",!0),p.value&&!u.value?(n(),i("div",un,[e("div",dn,[e("div",cn,[e("div",gn,[e("label",mn,[q(e("input",{type:"checkbox","onUpdate:modelValue":R[0]||(R[0]=I=>r.value=I),class:"w-4 h-4 text-yellow-600 border-gray-300 rounded focus:ring-yellow-500"},null,512),[[ae,r.value]]),R[2]||(R[2]=e("span",{class:"ml-2 text-sm text-gray-700"},"本次不使用缓存",-1))]),e("div",pn,a(r.value?"⚠️ 将强制重新处理":"💾 将使用缓存加速"),1)]),e("div",fn,a(r.value?"可能会稍慢但获取最新结果":"使用缓存提升响应速度"),1)])]),e("div",vn,[e("button",{onClick:T,disabled:u.value||b.value,class:"flex-1 px-6 py-3 text-white bg-yellow-600 rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 📝 "+a(b.value?"句子已组成":"开始组成句子"),9,bn),b.value?(n(),i("button",{key:0,onClick:z,class:"px-6 py-3 text-yellow-700 bg-yellow-100 rounded-lg hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):c("",!0)])])):c("",!0),u.value?(n(),i("div",xn,[e("div",hn,[e("div",yn,[R[3]||(R[3]=e("h4",{class:"text-sm font-medium text-yellow-800"},"📝 正在组成句子",-1)),e("span",Sn,a(P(new Date)),1)]),e("div",wn,[e("div",_n,[R[4]||(R[4]=e("span",{class:"text-yellow-700"},"📊 组成进度",-1)),e("span",kn,a(Math.round(o.value||0))+"%",1)]),e("div",$n,[e("div",{class:"bg-yellow-600 h-2 rounded-full transition-all duration-300",style:be({width:`${Math.min(100,Math.max(0,o.value||0))}%`})},null,4)])]),e("div",Tn," ⏱️ "+a(l.value||"正在将转录文本组成带时间戳的字幕句子..."),1)])])):c("",!0),b.value&&!u.value?(n(),i("div",Cn,[e("div",En,[R[8]||(R[8]=e("div",{class:"flex items-center justify-between mb-3"},[e("h4",{class:"text-sm font-medium text-green-800"},"✅ 句子组成完成")],-1)),e("p",Mn,a(y.value),1),e("div",Pn,[R[7]||(R[7]=e("div",{class:"text-xs text-green-600 mb-1"},"📊 组成统计",-1)),e("div",An,[e("div",null,[R[5]||(R[5]=e("span",{class:"text-gray-600"},"句子数量:",-1)),e("span",Ln,a(w.value),1)]),e("div",null,[R[6]||(R[6]=e("span",{class:"text-gray-600"},"平均长度:",-1)),e("span",In,a(M.value)+"字",1)])])])])])):c("",!0),te(t).progressUpdates.length>0?(n(),Z(ye,{key:5,title:"组成日志",logs:h.value,"initial-expanded":!1,"show-stats":!0,onClear:V,onCopy:X},null,8,["logs"])):c("",!0)]))}},Fn=re(Rn,[["__scopeId","data-v-21b5072c"]]),Nn={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},Un={key:1,class:"mb-6"},On={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},zn={class:"flex items-center justify-between mb-3"},Dn={class:"text-xs text-blue-600"},jn={class:"bg-white border border-blue-200 rounded-md p-3 max-h-32 overflow-y-auto"},Bn={class:"space-y-1"},Wn={class:"text-gray-500 font-mono text-xs"},Vn={class:"text-gray-700 ml-2"},Gn={key:2,class:"current-actions mb-6"},qn={class:"bg-gray-50 rounded-lg p-4 mb-4"},Hn={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},Kn={key:0},Jn=["value"],Xn={key:0,class:"mt-2 text-xs text-gray-600"},Qn={key:0},Yn={key:1,class:"text-sm text-gray-500 p-3 bg-gray-50 rounded-md"},Zn={class:"space-y-3 mb-4"},el={class:"grid grid-cols-2 md:grid-cols-3 gap-3"},tl={class:"flex items-center space-x-2 text-sm text-gray-700"},sl={class:"flex items-center space-x-2 text-sm text-gray-700"},ol={class:"flex items-center space-x-2 text-sm text-gray-700"},rl={class:"mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200"},nl={class:"flex items-center justify-between"},ll={class:"flex items-center space-x-2"},il={class:"flex items-center cursor-pointer"},al={class:"text-xs text-gray-500 ml-2"},ul={class:"text-xs text-gray-400"},dl={class:"flex space-x-3"},cl=["disabled"],gl=["disabled"],ml={key:3,class:"mb-6"},pl={class:"bg-orange-50 border border-orange-200 rounded-lg p-4"},fl={class:"flex items-center justify-between mb-3"},vl={class:"text-xs text-orange-600"},bl={class:"mb-3"},xl={class:"flex items-center justify-between text-sm mb-1"},hl={class:"text-orange-700"},yl={class:"w-full bg-orange-200 rounded-full h-2"},Sl={class:"text-sm text-orange-600"},wl={key:0,class:"mt-3 p-3 bg-white rounded-md border border-orange-200"},_l={class:"space-y-1"},kl={class:"text-sm text-gray-600"},$l={class:"text-sm text-gray-800"},Tl={key:4,class:"mb-6"},Cl={class:"bg-green-50 border border-green-200 rounded-lg p-4"},El={class:"flex items-center justify-between mb-3"},Ml={class:"text-sm text-green-700 mb-3"},Pl={key:0,class:"space-y-3"},Al={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},Ll={class:"text-center p-3 bg-white rounded-md border border-green-200"},Il={class:"text-lg font-bold text-green-700"},Rl={class:"text-center p-3 bg-white rounded-md border border-green-200"},Fl={class:"text-lg font-bold text-green-700"},Nl={class:"text-center p-3 bg-white rounded-md border border-green-200"},Ul={class:"text-lg font-bold text-green-700"},Ol={class:"text-center p-3 bg-white rounded-md border border-green-200"},zl={class:"text-lg font-bold text-green-700"},Dl={__name:"OptimizeSubtitlesStep",setup(s){const t=ce(),o=L([]),l=L(null),r=L(!1),u=L("medium"),p=L(""),b=L(!1),w=L(0),d=L(""),v=L(null),y=L(!1),M=Ee({grammar:!0,punctuation:!0,expression:!0}),h=x(()=>t.isLoading),P=x(()=>t.generatedSubtitles&&t.generatedSubtitles.length>0),T=x(()=>t.generatedSubtitles?t.generatedSubtitles.length:0),m=x(()=>t.optimizedSubtitles&&t.optimizedSubtitles.length>0),z=x(()=>t.optimizedSubtitles?t.optimizedSubtitles.length:0),V=x(()=>P.value?t.generatedSubtitles.slice(0,5):[]),X=x(()=>m.value?`成功优化 ${z.value} 个字幕片段，提升了语法准确性和表达流畅度`:""),F=x(()=>({grammarFixes:Math.floor(z.value*.3),punctuationFixes:Math.floor(z.value*.2),expressionImprovements:Math.floor(z.value*.4)})),R=x(()=>t.progressUpdates.map(N=>({timestamp:new Date().toISOString(),level:N.includes("✅")?"success":N.includes("❌")?"error":N.includes("⚠️")?"warning":"info",message:N}))),I=x(()=>l.value?o.value.find(N=>N.provider_id===l.value):null),D=N=>{if(N==null||N===void 0)return"00:00";if(typeof N=="number"){const _=Math.floor(N/1e3),se=Math.floor(_/60),ne=_%60;return`${se.toString().padStart(2,"0")}:${ne.toString().padStart(2,"0")}`}else{if(N instanceof Date)return N.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"});{const _=Number(N);if(!isNaN(_)){const se=Math.floor(_/1e3),ne=Math.floor(se/60),Q=se%60;return`${ne.toString().padStart(2,"0")}:${Q.toString().padStart(2,"0")}`}return"00:00"}}},O=async()=>{try{r.value=!0;const N=await window.electronAPI.invoke("load-ai-configs");Array.isArray(N)?(o.value=N.filter(_=>_.is_enabled),o.value.length>0&&!l.value&&(l.value=o.value[0].provider_id),console.log(`[OptimizeStep] 加载了 ${o.value.length} 个可用的AI服务`)):console.warn("[OptimizeStep] 获取AI配置失败: 返回数据格式不正确")}catch(N){console.error("[OptimizeStep] 加载AI服务时出错:",N)}finally{r.value=!1}},J=()=>{console.log("[OptimizeStep] AI服务已切换:",l.value)},K=()=>{window.electronAPI.invoke("open-ai-settings"),sessionStorage.setItem("went-to-ai-settings","true")},j=async()=>{if(!P.value)return;if(!l.value){console.error("请先选择AI服务");return}const N={aiServiceId:l.value,level:u.value,options:M,customPrompt:p.value,skipCache:y.value};w.value=0,d.value="准备优化...",v.value={original:"这个这个技术呢就是...",optimized:"这项技术的核心原理是..."};try{await t.optimizeSubtitles(N)}catch(_){console.error("优化字幕时出错:",_)}},U=()=>{t.setEditableSegments(t.generatedSubtitles),t.setCurrentStep(5)},k=()=>{b.value=!b.value},f=N=>{N.key==="goto-edit"&&t.setCurrentStep(3)},S=()=>{t.setCurrentStep(7)},$=()=>{t.progressUpdates=[]},E=N=>{console.log("日志已复制:",N)};return he(()=>{O();const N=()=>{console.log("[OptimizeStep] 接收到AI配置更新事件，重新加载服务列表"),O()},_=window.electronAPI.onMainProcessEvent("ai-configs-updated",N),se=()=>{console.log("[OptimizeStep] 窗口获得焦点，检查AI配置更新"),sessionStorage.getItem("went-to-ai-settings")&&(console.log("[OptimizeStep] 检测到从AI设置页面返回，强制刷新AI服务列表"),sessionStorage.removeItem("went-to-ai-settings"),O())},ne=()=>{document.hidden||(console.log("[OptimizeStep] 页面变为可见，检查AI配置更新"),setTimeout(()=>{O()},100))};window.addEventListener("focus",se),document.addEventListener("visibilitychange",ne),Ye(()=>{window.removeEventListener("focus",se),document.removeEventListener("visibilitychange",ne),_&&_()})}),(N,_)=>{var se,ne;return n(),i("div",Nn,[_[27]||(_[27]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 5: 优化字幕"),e("p",{class:"text-sm text-gray-500 mt-1"},"使用AI优化字幕的语法和表达")],-1)),P.value?c("",!0):(n(),Z(pe,{key:0,type:"warning",title:"需要先完成字幕生成",message:"请先完成语音转文字和字幕编辑，准备好字幕内容后再进行优化",actions:[{key:"goto-edit",label:"前往字幕编辑",icon:"📝",primary:!0}],onAction:f})),P.value?(n(),i("div",Un,[e("div",On,[e("div",zn,[_[7]||(_[7]=e("h4",{class:"text-sm font-medium text-blue-800"},"📝 字幕内容预览",-1)),e("span",Dn,"共 "+a(T.value)+" 个片段",1)]),e("div",jn,[e("div",Bn,[(n(!0),i(Y,null,ee(V.value,(Q,xe)=>(n(),i("div",{key:xe,class:"text-sm"},[e("span",Wn,a(D(Q.startTimeMs))+" → "+a(D(Q.endTimeMs)),1),e("span",Vn,a(Q.text),1)]))),128))])])])])):c("",!0),P.value&&!h.value?(n(),i("div",Gn,[e("div",qn,[_[17]||(_[17]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"🎯 优化设置",-1)),e("div",Hn,[e("div",null,[_[9]||(_[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"AI服务:",-1)),o.value.length>0?(n(),i("div",Kn,[q(e("select",{"onUpdate:modelValue":_[0]||(_[0]=Q=>l.value=Q),onChange:J,class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"},[(n(!0),i(Y,null,ee(o.value,Q=>(n(),i("option",{key:Q.provider_id,value:Q.provider_id},a(Q.display_name)+" ("+a(Q.provider_type)+") ",9,Jn))),128))],544),[[le,l.value]]),I.value?(n(),i("div",Xn,[e("div",null,"模型: "+a(((se=I.value.attributes)==null?void 0:se.default_model)||"默认"),1),(ne=I.value.attributes)!=null&&ne.api_base_url?(n(),i("div",Qn,"API: "+a(I.value.attributes.api_base_url),1)):c("",!0)])):c("",!0)])):(n(),i("div",Yn,[_[8]||(_[8]=e("div",{class:"flex items-center space-x-2"},[e("span",null,"⚠️"),e("span",null,"未找到可用的AI服务配置")],-1)),e("button",{onClick:K,class:"mt-2 text-orange-600 hover:text-orange-800 text-xs underline"}," 前往AI设置页面配置 ")]))]),e("div",null,[_[11]||(_[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"优化强度:",-1)),q(e("select",{"onUpdate:modelValue":_[1]||(_[1]=Q=>u.value=Q),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"},_[10]||(_[10]=[e("option",{value:"light"},"轻度 - 仅修正错误",-1),e("option",{value:"medium"},"标准 - 改善语法流畅度",-1),e("option",{value:"heavy"},"深度 - 全面优化润色",-1)]),512),[[le,u.value]])])]),e("div",Zn,[_[15]||(_[15]=e("h5",{class:"text-sm font-medium text-gray-700"},"优化目标:",-1)),e("div",el,[e("label",tl,[q(e("input",{type:"checkbox","onUpdate:modelValue":_[2]||(_[2]=Q=>M.grammar=Q),class:"rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"},null,512),[[ae,M.grammar]]),_[12]||(_[12]=e("span",null,"语法修正",-1))]),e("label",sl,[q(e("input",{type:"checkbox","onUpdate:modelValue":_[3]||(_[3]=Q=>M.punctuation=Q),class:"rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"},null,512),[[ae,M.punctuation]]),_[13]||(_[13]=e("span",null,"断句优化",-1))]),e("label",ol,[q(e("input",{type:"checkbox","onUpdate:modelValue":_[4]||(_[4]=Q=>M.expression=Q),class:"rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"},null,512),[[ae,M.expression]]),_[14]||(_[14]=e("span",null,"用词提升",-1))])])]),e("div",null,[_[16]||(_[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"自定义提示 (可选):",-1)),q(e("textarea",{"onUpdate:modelValue":_[5]||(_[5]=Q=>p.value=Q),placeholder:"例如：这是技术教程，请保持专业术语准确性...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 resize-none",rows:"2"},"          ",512),[[ve,p.value]])])]),e("div",rl,[e("div",nl,[e("div",ll,[e("label",il,[q(e("input",{type:"checkbox","onUpdate:modelValue":_[6]||(_[6]=Q=>y.value=Q),class:"w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"},null,512),[[ae,y.value]]),_[18]||(_[18]=e("span",{class:"ml-2 text-sm text-gray-700"},"本次不使用缓存",-1))]),e("div",al,a(y.value?"⚠️ 将强制重新优化":"💾 将使用缓存加速"),1)]),e("div",ul,a(y.value?"可能会稍慢但获取最新结果":"使用缓存提升响应速度"),1)])]),e("div",dl,[e("button",{onClick:j,disabled:h.value||m.value,class:"flex-1 px-6 py-3 text-white bg-orange-600 rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🤖 "+a(m.value?"已优化完成":"开始AI优化"),9,cl),e("button",{onClick:U,disabled:h.value,class:"px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," ⏭️ 跳过优化 ",8,gl),m.value&&!h.value?(n(),i("button",{key:0,onClick:S,class:"px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):c("",!0)])])):c("",!0),h.value?(n(),i("div",ml,[e("div",pl,[e("div",fl,[_[19]||(_[19]=e("h4",{class:"text-sm font-medium text-orange-800"},"🤖 正在AI优化",-1)),e("span",vl,a(D(new Date)),1)]),e("div",bl,[e("div",xl,[_[20]||(_[20]=e("span",{class:"text-orange-700"},"📊 优化进度",-1)),e("span",hl,a(Math.round(w.value||0))+"%",1)]),e("div",yl,[e("div",{class:"bg-orange-600 h-2 rounded-full transition-all duration-300",style:be({width:`${Math.min(100,Math.max(0,w.value||0))}%`})},null,4)])]),e("div",Sl," ⏱️ "+a(d.value||"正在使用AI优化字幕语法和表达..."),1),v.value?(n(),i("div",wl,[_[21]||(_[21]=e("div",{class:"text-xs text-orange-600 mb-1"},"💡 优化预览",-1)),e("div",_l,[e("div",kl,"原文: "+a(v.value.original),1),e("div",$l,"优化: "+a(v.value.optimized),1)])])):c("",!0)])])):c("",!0),m.value&&!h.value?(n(),i("div",Tl,[e("div",Cl,[e("div",El,[_[22]||(_[22]=e("h4",{class:"text-sm font-medium text-green-800"},"✅ AI优化完成",-1)),e("button",{onClick:k,class:"text-xs text-green-600 hover:text-green-800 focus:outline-none"},a(b.value?"隐藏详情":"查看详情")+" "+a(b.value?"▲":"▼"),1)]),e("p",Ml,a(X.value),1),b.value?(n(),i("div",Pl,[e("div",Al,[e("div",Ll,[e("div",Il,a(z.value),1),_[23]||(_[23]=e("div",{class:"text-xs text-green-600"},"优化片段",-1))]),e("div",Rl,[e("div",Fl,a(F.value.grammarFixes||0),1),_[24]||(_[24]=e("div",{class:"text-xs text-green-600"},"语法修正",-1))]),e("div",Nl,[e("div",Ul,a(F.value.punctuationFixes||0),1),_[25]||(_[25]=e("div",{class:"text-xs text-green-600"},"标点优化",-1))]),e("div",Ol,[e("div",zl,a(F.value.expressionImprovements||0),1),_[26]||(_[26]=e("div",{class:"text-xs text-green-600"},"表达提升",-1))])])])):c("",!0)])])):c("",!0),te(t).progressUpdates.length>0?(n(),Z(ye,{key:5,title:"优化日志",logs:R.value,"initial-expanded":!1,"show-stats":!0,onClear:$,onCopy:E},null,8,["logs"])):c("",!0)])}}},jl=re(Dl,[["__scopeId","data-v-35d6cd30"]]),Bl={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},Wl={key:1,class:"current-actions mb-6"},Vl={class:"bg-gray-50 rounded-lg p-4 mb-4"},Gl={for:"useTranscription",class:"flex items-center space-x-2 text-sm text-gray-700"},ql={key:0,class:"text-xs text-yellow-600 mt-1"},Hl={class:"flex flex-col lg:flex-row gap-4"},Kl={class:"lg:w-1/2"},Jl={class:"bg-white border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto"},Xl={class:"flex items-center justify-between mb-3"},Ql={class:"text-sm font-medium text-gray-700"},Yl={class:"space-y-2"},Zl=["onClick"],ei={class:"flex justify-between items-start mb-1"},ti={class:"flex items-center space-x-2"},si={class:"text-xs text-gray-500 font-mono"},oi={key:0,class:"text-xs"},ri={key:0,class:"text-green-600",title:"处理成功"},ni={key:1,class:"text-red-600",title:"处理失败"},li={key:2,class:"text-yellow-600",title:"部分成功"},ii={key:3,class:"text-gray-600",title:"处理中"},ai=["onClick"],ui={class:"text-sm text-gray-700 leading-relaxed"},di={key:0,class:"mt-2 p-2 bg-red-100 border border-red-200 rounded text-xs"},ci={class:"text-red-800 font-medium"},gi={key:0,class:"text-red-600 mt-1"},mi={key:1,class:"mt-2 p-2 bg-green-100 border border-green-200 rounded text-xs"},pi={class:"text-green-800 font-medium"},fi={class:"lg:w-1/2"},vi={class:"bg-white border border-gray-200 rounded-lg p-4 min-h-[300px]"},bi={key:0},xi={class:"text-sm font-medium text-gray-700 mb-3"},hi={class:"space-y-4"},yi=["for"],Si=["id"],wi={class:"grid grid-cols-2 gap-4"},_i=["for"],ki=["id"],$i=["for"],Ti=["id"],Ci={class:"flex justify-end space-x-3"},Ei=["disabled"],Mi={key:1,class:"flex items-center justify-center h-full text-gray-500"},Pi={key:2,class:"current-actions mb-6"},Ai={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center"},Li={class:"text-yellow-700 mb-4"},Ii={class:"space-y-3"},Ri={for:"useTranscription",class:"flex items-center justify-center space-x-2 text-sm text-yellow-700"},Fi={key:0,class:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"},Ni={class:"flex items-center justify-between"},Ui={class:"text-sm text-green-600 mt-1"},Oi={__name:"EditSubtitlesStep",setup(s){const t=ce(),{isLoading:o,editableSegments:l,selectedSegmentId:r,audioToTextResult:u,useTranscriptionForEditing:p}=Ze(t),b=x({get:()=>t.useTranscriptionForEditing,set:j=>{t.setUseTranscriptionForEditing(j),j&&u.value?t.initializeEditableSegments():j||(y.value="",M.value=0,h.value=0,P.value=!1,t.setSelectedSegmentId(null))}}),w=x(()=>!!u.value&&(!!u.value.segments||typeof u.value.transcript=="string")),d=x(()=>{const j=w.value,U=t.generatedSubtitles&&t.generatedSubtitles.length>0,k=t.optimizedSubtitles&&t.optimizedSubtitles.length>0;return!j&&!U&&!k?{title:"需要先完成语音转文字",message:"请先完成语音转文字处理，生成字幕内容后再进行编辑",actionText:"前往语音转文字"}:null}),v=x(()=>!r.value||!l.value?null:l.value.find(j=>j.id===r.value)||null),y=L(""),M=L(0),h=L(0),P=L(!1);ge(v,j=>{j?(y.value=j.text,M.value=j.startTimeMs,h.value=j.endTimeMs,P.value=!1):(y.value="",M.value=0,h.value=0,P.value=!1)},{immediate:!0});const T=()=>{P.value=!0},m=()=>{if(!(!v.value||!P.value)){if(M.value<0||h.value<0||h.value<M.value){alert("时间设置无效。结束时间必须大于等于开始时间，且两者都不能为负。"),M.value=v.value.startTimeMs,h.value=v.value.endTimeMs;return}t.updateSegmentText({id:v.value.id,newText:y.value}),t.updateSegmentTime({id:v.value.id,newStartTimeMs:M.value,newEndTimeMs:h.value}),P.value=!1}},z=()=>{v.value&&(y.value=v.value.text,M.value=v.value.startTimeMs,h.value=v.value.endTimeMs,P.value=!1)},V=j=>{P.value&&!confirm("当前片段有未应用的更改，切换将会丢失这些更改。确定要切换吗？")||t.setSelectedSegmentId(j)},X=x(()=>l.value&&l.value.length>0),F=()=>{console.log("Attempting to initialize segments from EditSubtitlesStep..."),t.initializeEditableSegments()};he(()=>{b.value&&w.value&&!X.value?(console.log("EditSubtitlesStep: useTranscription is true and audioToTextResult available on mount, initializing segments."),t.initializeEditableSegments()):!w.value&&b.value?console.log("EditSubtitlesStep: useTranscription is true but audioToTextResult not available on mount."):b.value||console.log("EditSubtitlesStep: useTranscription is false on mount. Segments will not be auto-initialized.")});const R=x(()=>b.value?w.value?"没有可编辑的字幕片段。点击下方按钮尝试从转录结果初始化。":"没有可编辑的字幕片段。请确保上一步“语音转文字”已成功完成，或检查“使用上一步的转录结果”选项。":"没有可编辑的字幕片段。请取消勾选“使用上一步的转录结果进行编辑”以手动添加，或确保上一步已完成并勾选该选项。"),I=j=>{if(typeof j!="number"||isNaN(j))return"00:00.000";const U=new Date(j),k=String(U.getUTCMinutes()).padStart(2,"0"),f=String(U.getUTCSeconds()).padStart(2,"0"),S=String(U.getUTCMilliseconds()).padStart(3,"0");return`${k}:${f}.${S}`},D=j=>{j.key==="goto-transcription"&&t.setCurrentStep(2)},O=()=>{if(P.value){alert("请先应用或撤销当前片段的更改，然后进入下一步。");return}t.setCurrentStep(4)},J=()=>{let j=null,U=0;if(v.value)j=v.value.id,U=v.value.endTimeMs+1;else if(l.value&&l.value.length>0){const k=l.value[l.value.length-1];j=k.id,U=k.endTimeMs+1}t.addSegment({afterId:j,newSegmentData:{startTimeMs:U,endTimeMs:U+2e3,text:"新字幕片段"}})},K=j=>{confirm(`确定要删除片段 #${j.substring(0,8)}... 吗？此操作无法撤销。`)&&t.deleteSegment(j)};return(j,U)=>(n(),i(Y,null,[e("div",Bl,[U[11]||(U[11]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 3: 编辑字幕"),e("p",{class:"text-sm text-gray-500 mt-1"},"编辑和调整字幕内容、时间戳")],-1)),d.value?(n(),Z(pe,{key:0,type:"warning",title:d.value.title,message:d.value.message,actions:[{key:"goto-transcription",label:d.value.actionText,icon:"🎤",primary:!0}],onAction:D},null,8,["title","message","actions"])):c("",!0),X.value?(n(),i("div",Wl,[e("div",Vl,[U[6]||(U[6]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"编辑设置",-1)),e("label",Gl,[q(e("input",{type:"checkbox",id:"useTranscription","onUpdate:modelValue":U[0]||(U[0]=k=>b.value=k),class:"rounded border-gray-300 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"},null,512),[[ae,b.value]]),U[5]||(U[5]=e("span",null,"使用上一步的转录结果进行编辑",-1))]),b.value&&!w.value?(n(),i("p",ql," 上一步未生成转录结果，或已被清除。 ")):c("",!0)]),e("div",Hl,[e("div",Kl,[e("div",Jl,[e("div",Xl,[e("h4",Ql,"字幕片段 ("+a(te(l).length)+")",1),e("button",{onClick:J,class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm font-medium transition-colors"}," 添加片段 ")]),e("div",Yl,[(n(!0),i(Y,null,ee(te(l),k=>(n(),i("div",{key:k.id,onClick:f=>V(k.id),class:H(["p-3 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors",{"bg-red-50 border-red-300":k.id===te(r),"border-red-200 bg-red-25":k.status==="FAILURE"||k.errorDetail,"border-yellow-200 bg-yellow-25":k.status==="PARTIAL_SUCCESS","border-gray-200":k.id!==te(r)&&!k.errorDetail&&k.status!=="FAILURE"&&k.status!=="PARTIAL_SUCCESS"}])},[e("div",ei,[e("div",ti,[e("span",si,a(I(k.startTimeMs))+" - "+a(I(k.endTimeMs)),1),k.status?(n(),i("span",oi,[k.status==="SUCCESS"?(n(),i("span",ri,"✅")):k.status==="FAILURE"?(n(),i("span",ni,"❌")):k.status==="PARTIAL_SUCCESS"?(n(),i("span",li,"⚠️")):(n(),i("span",ii,"⏳"))])):c("",!0)]),te(r)===k.id?(n(),i("button",{key:0,onClick:_e(f=>K(k.id),["stop"]),class:"text-red-500 hover:text-red-700 text-sm p-1 rounded hover:bg-red-100",title:"删除片段"}," × ",8,ai)):c("",!0)]),e("p",ui,a(k.text),1),k.errorDetail?(n(),i("div",di,[e("div",ci,"错误: "+a(k.errorDetail.userMessage||k.errorDetail.technicalMessage),1),k.errorDetail.errorCode?(n(),i("div",gi,"错误代码: "+a(k.errorDetail.errorCode),1)):c("",!0)])):c("",!0),k.translatedText?(n(),i("div",mi,[e("div",pi,"翻译: "+a(k.translatedText),1)])):c("",!0)],10,Zl))),128))])])]),e("div",fi,[e("div",vi,[v.value?(n(),i("div",bi,[e("h4",xi," 编辑片段 #"+a(v.value.id.substring(0,8))+"... ",1),e("div",hi,[e("div",null,[e("label",{for:"segment-text-"+v.value.id,class:"block text-sm font-medium text-gray-700 mb-2"}," 文本内容 ",8,yi),q(e("textarea",{id:"segment-text-"+v.value.id,"onUpdate:modelValue":U[1]||(U[1]=k=>y.value=k),onInput:T,rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500",placeholder:"输入字幕文本..."},null,40,Si),[[ve,y.value]])]),e("div",wi,[e("div",null,[e("label",{for:"start-time-"+v.value.id,class:"block text-sm font-medium text-gray-700 mb-2"}," 开始时间 (ms) ",8,_i),q(e("input",{type:"number",id:"start-time-"+v.value.id,"onUpdate:modelValue":U[2]||(U[2]=k=>M.value=k),onInput:T,min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"},null,40,ki),[[ve,M.value,void 0,{number:!0}]])]),e("div",null,[e("label",{for:"end-time-"+v.value.id,class:"block text-sm font-medium text-gray-700 mb-2"}," 结束时间 (ms) ",8,$i),q(e("input",{type:"number",id:"end-time-"+v.value.id,"onUpdate:modelValue":U[3]||(U[3]=k=>h.value=k),onInput:T,min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"},null,40,Ti),[[ve,h.value,void 0,{number:!0}]])])]),e("div",Ci,[P.value?(n(),i("button",{key:0,onClick:z,class:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"}," 撤销 ")):c("",!0),e("button",{onClick:m,disabled:!P.value,class:"px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"}," 应用更改 ",8,Ei)])])])):(n(),i("div",Mi,U[7]||(U[7]=[e("div",{class:"text-center"},[e("svg",{class:"h-12 w-12 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("p",null,"请从左侧列表中选择一个片段进行编辑")],-1)])))])])])])):X.value?c("",!0):(n(),i("div",Pi,[e("div",Ai,[U[9]||(U[9]=e("svg",{class:"h-12 w-12 text-yellow-500 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),U[10]||(U[10]=e("h3",{class:"text-lg font-medium text-yellow-800 mb-2"},"没有可编辑的字幕片段",-1)),e("p",Li,a(R.value),1),e("div",Ii,[e("label",Ri,[q(e("input",{type:"checkbox",id:"useTranscription","onUpdate:modelValue":U[4]||(U[4]=k=>b.value=k),class:"rounded border-yellow-300 text-yellow-600 shadow-sm focus:border-yellow-300 focus:ring focus:ring-yellow-200 focus:ring-opacity-50"},null,512),[[ae,b.value]]),U[8]||(U[8]=e("span",null,"使用上一步的转录结果进行编辑",-1))]),b.value&&w.value?(n(),i("button",{key:0,onClick:F,class:"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"}," 从转录结果初始化片段 ")):c("",!0)])])]))]),X.value&&!P.value?(n(),i("div",Fi,[e("div",Ni,[e("div",null,[U[12]||(U[12]=e("h4",{class:"text-sm font-medium text-green-800"},"字幕编辑完成",-1)),e("p",Ui,"已编辑 "+a(te(l).length)+" 个字幕片段，可以进入下一步",1)]),e("button",{onClick:O,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," ➡️ 进入下一步 ")])])):c("",!0)],64))}},zi=re(Oi,[["__scopeId","data-v-5e945c3c"]]),Di={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},ji={key:0,class:"previous-results mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"},Bi={class:"flex items-center justify-between"},Wi={class:"text-xs text-green-600"},Vi={key:1,class:"prerequisite-warning mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg"},Gi={class:"flex items-start"},qi={class:"flex-1"},Hi={key:2,class:"current-actions mb-6"},Ki={class:"bg-gray-50 rounded-lg p-4 mb-6"},Ji={class:"bg-white border border-gray-200 rounded-md p-3 max-h-32 overflow-y-auto"},Xi={class:"space-y-1"},Qi={class:"text-gray-500 font-mono text-xs"},Yi={class:"text-gray-700 ml-2"},Zi={class:"text-sm text-gray-500 mt-2"},ea={class:"space-y-6"},ta={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},sa={class:"text-center"},oa={class:"text-center"},ra={key:0,class:"bg-indigo-50 border border-indigo-200 rounded-lg p-4 space-y-4"},na={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},la={class:"flex justify-between items-center pt-4"},ia={class:"flex space-x-3"},aa={__name:"TranslationChoiceStep",setup(s){const t=ce(),o=L(!1),l=L("zh"),r=L("balanced"),u=L("formal"),p=L(""),b=x(()=>t.editableSegments&&t.editableSegments.length>0),w=x(()=>t.editableSegments?t.editableSegments.length:0),d=x(()=>b.value?t.editableSegments.slice(0,5):[]),v=T=>{const m=Math.floor(T/1e3),z=Math.floor(m/60),V=m%60;return`${z.toString().padStart(2,"0")}:${V.toString().padStart(2,"0")}`},y=T=>{o.value=T},M=()=>{t.setTranslationSettings({targetLanguage:l.value,quality:r.value,style:u.value,customPrompt:p.value}),t.setCurrentStep(8)},h=()=>{t.setCurrentStep(6)},P=()=>{t.setCurrentStep(5)};return(T,m)=>(n(),i("div",Di,[m[27]||(m[27]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 7: 翻译选择"),e("p",{class:"text-sm text-gray-500 mt-1"},"选择是否需要翻译字幕以及翻译设置")],-1)),b.value?(n(),i("div",ji,[e("div",Bi,[m[7]||(m[7]=e("div",{class:"flex items-center"},[e("svg",{class:"h-4 w-4 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})]),e("span",{class:"text-sm font-medium text-green-800"},"字幕编辑已完成")],-1)),e("span",Wi,"已编辑 "+a(w.value)+" 个字幕片段",1)])])):c("",!0),b.value?c("",!0):(n(),i("div",Vi,[e("div",Gi,[m[10]||(m[10]=e("svg",{class:"h-5 w-5 text-yellow-600 mr-3 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",qi,[m[8]||(m[8]=e("h4",{class:"text-sm font-medium text-yellow-800"},"需要先完成字幕编辑",-1)),m[9]||(m[9]=e("p",{class:"text-sm text-yellow-700 mt-1"},"请先在步骤6中完成字幕编辑，准备好字幕内容后再选择是否翻译",-1)),e("button",{onClick:m[0]||(m[0]=z=>T.handlePrerequisiteAction({step:6})),class:"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900 transition-colors"}," 前往字幕编辑 ")])])])),b.value?(n(),i("div",Hi,[e("div",Ki,[m[11]||(m[11]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"当前字幕预览",-1)),e("div",Ji,[e("div",Xi,[(n(!0),i(Y,null,ee(d.value,(z,V)=>(n(),i("div",{key:V,class:"text-sm"},[e("span",Qi,a(v(z.startTimeMs))+" → "+a(v(z.endTimeMs)),1),e("span",Yi,a(z.text),1)]))),128))])]),e("p",Zi,"共 "+a(w.value)+" 个字幕片段",1)]),e("div",ea,[m[26]||(m[26]=e("div",{class:"text-center mb-6"},[e("h3",{class:"text-lg font-semibold text-gray-800 mb-2"},"是否需要翻译字幕？"),e("p",{class:"text-gray-600"},"您可以选择将字幕翻译成其他语言，或直接进入导出步骤")],-1)),e("div",ta,[e("div",{class:H(["border-2 rounded-lg p-4 cursor-pointer transition-all",{"border-green-500 bg-green-50":!o.value,"border-gray-200 hover:border-gray-300":o.value}]),onClick:m[1]||(m[1]=z=>y(!1))},[e("div",sa,[e("div",{class:H(["w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3",{"bg-green-100":!o.value,"bg-gray-100":o.value}])},[(n(),i("svg",{class:H(["w-6 h-6",{"text-green-600":!o.value,"text-gray-500":o.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m[12]||(m[12]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]),2))],2),m[13]||(m[13]=e("h4",{class:"font-semibold text-gray-800 mb-1"},"不需要翻译",-1)),m[14]||(m[14]=e("p",{class:"text-gray-600 text-sm"},"直接使用当前语言的字幕",-1))])],2),e("div",{class:H(["border-2 rounded-lg p-4 cursor-pointer transition-all",{"border-indigo-500 bg-indigo-50":o.value,"border-gray-200 hover:border-gray-300":!o.value}]),onClick:m[2]||(m[2]=z=>y(!0))},[e("div",oa,[e("div",{class:H(["w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3",{"bg-indigo-100":o.value,"bg-gray-100":!o.value}])},[(n(),i("svg",{class:H(["w-6 h-6",{"text-indigo-600":o.value,"text-gray-500":!o.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m[15]||(m[15]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"},null,-1)]),2))],2),m[16]||(m[16]=e("h4",{class:"font-semibold text-gray-800 mb-1"},"需要翻译",-1)),m[17]||(m[17]=e("p",{class:"text-gray-600 text-sm"},"将字幕翻译成其他语言",-1))])],2)]),o.value?(n(),i("div",ra,[m[25]||(m[25]=e("h4",{class:"text-sm font-medium text-indigo-800 mb-3"},"翻译设置",-1)),e("div",na,[e("div",null,[m[19]||(m[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"目标语言",-1)),q(e("select",{"onUpdate:modelValue":m[3]||(m[3]=z=>l.value=z),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"},m[18]||(m[18]=[we('<option value="zh" data-v-b63b7ec7>中文 (简体)</option><option value="zh-TW" data-v-b63b7ec7>中文 (繁体)</option><option value="en" data-v-b63b7ec7>English</option><option value="ja" data-v-b63b7ec7>日本語</option><option value="ko" data-v-b63b7ec7>한국어</option><option value="es" data-v-b63b7ec7>Español</option><option value="fr" data-v-b63b7ec7>Français</option><option value="de" data-v-b63b7ec7>Deutsch</option><option value="ru" data-v-b63b7ec7>Русский</option><option value="ar" data-v-b63b7ec7>العربية</option>',10)]),512),[[le,l.value]])]),e("div",null,[m[21]||(m[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"翻译质量",-1)),q(e("select",{"onUpdate:modelValue":m[4]||(m[4]=z=>r.value=z),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"},m[20]||(m[20]=[e("option",{value:"fast"},"快速翻译 - 速度优先",-1),e("option",{value:"balanced"},"平衡模式 - 速度与质量兼顾",-1),e("option",{value:"quality"},"高质量 - 质量优先",-1)]),512),[[le,r.value]])])]),e("div",null,[m[23]||(m[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"翻译风格",-1)),q(e("select",{"onUpdate:modelValue":m[5]||(m[5]=z=>u.value=z),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"},m[22]||(m[22]=[e("option",{value:"formal"},"正式 - 适合商务、学术内容",-1),e("option",{value:"casual"},"随意 - 适合日常对话",-1),e("option",{value:"literary"},"文学 - 适合文艺作品",-1),e("option",{value:"technical"},"技术 - 适合技术文档",-1)]),512),[[le,u.value]])]),e("div",null,[m[24]||(m[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"自定义翻译提示 (可选)",-1)),q(e("textarea",{"onUpdate:modelValue":m[6]||(m[6]=z=>p.value=z),placeholder:"例如：这是一个烹饪教程，请保持食材和烹饪术语的准确性...",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 resize-none bg-white",rows:"3"},"            ",512),[[ve,p.value]])])])):c("",!0),e("div",la,[e("button",{onClick:P,class:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors text-base"}," ← 返回编辑 "),e("div",ia,[o.value===!1?(n(),i("button",{key:0,onClick:h,class:"px-6 py-3 text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 font-medium transition-colors text-base"}," 📥 进入导出 ")):c("",!0),o.value===!0&&l.value?(n(),i("button",{key:1,onClick:M,class:"px-6 py-3 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium transition-colors text-base"}," 🌐 开始翻译 ")):c("",!0)])])])])):c("",!0)]))}},ua=re(aa,[["__scopeId","data-v-b63b7ec7"]]),da={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},ca={key:0,class:"previous-results mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"},ga={class:"flex items-center justify-between"},ma={class:"text-xs text-green-600"},pa={key:1,class:"prerequisite-warning mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg"},fa={class:"flex items-start"},va={class:"flex-1"},ba={key:2,class:"current-actions mb-6"},xa={class:"bg-gray-50 rounded-lg p-4 mb-4"},ha={class:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm"},ya={class:"bg-white p-2 rounded border"},Sa={class:"text-pink-700 font-semibold"},wa={class:"bg-white p-2 rounded border"},_a={class:"text-pink-700 font-semibold"},ka={class:"bg-white p-2 rounded border"},$a={class:"text-pink-700 font-semibold"},Ta={class:"bg-white p-2 rounded border"},Ca={class:"text-pink-700 font-semibold"},Ea={class:"bg-gray-50 rounded-lg p-4 mb-4"},Ma={class:"bg-white border border-gray-200 rounded-md p-3 max-h-32 overflow-y-auto"},Pa={class:"space-y-1"},Aa={class:"text-gray-500 font-mono text-xs"},La={class:"text-gray-700 ml-2"},Ia={class:"bg-gray-50 rounded-lg p-4 mb-4"},Ra={class:"flex items-center space-x-2 text-sm text-gray-700"},Fa={class:"text-xs text-gray-500 ml-2"},Na={class:"flex space-x-3"},Ua=["disabled"],Oa={key:3,class:"translation-results mb-6"},za={class:"bg-gray-50 rounded-lg p-4"},Da={class:"bg-white border border-gray-200 rounded-md p-3 max-h-48 overflow-y-auto"},ja={class:"space-y-3"},Ba={class:"text-xs text-gray-500 font-mono mb-1"},Wa={class:"text-sm text-gray-600 mb-1"},Va={class:"text-sm text-pink-800"},Ga={class:"mt-3 mb-4"},qa={class:"text-sm text-gray-600"},Ha={class:"space-y-3"},Ka={class:"flex justify-between space-x-3"},Ja=["disabled"],Xa=["disabled"],Qa={__name:"TranslationStep",setup(s){const t=ce(),o=L(!1),l=x(()=>t.isLoading),r=x(()=>t.editableSegments&&t.editableSegments.length>0),u=x(()=>t.editableSegments?t.editableSegments.length:0),p=x(()=>t.translatedSubtitles&&t.translatedSubtitles.length>0),b=x(()=>t.translatedSubtitles?t.translatedSubtitles.length:0);x(()=>t.translationProgress);const w=x(()=>t.translationProgressPercent);x(()=>t.progressUpdates);const d=x(()=>t.translationSettings||{}),v=x(()=>t.currentOperationStatus||ie.IDLE),y=x(()=>t.lastError&&(v.value===ie.ERROR||v.value===ie.CANCELED)),M=x(()=>t.lastError||{}),h=x(()=>t.currentSegmentIndex||0),P=x(()=>t.totalSegments||u.value),T=x(()=>r.value?t.editableSegments.slice(0,3):[]),m=x(()=>p.value?t.translatedSubtitles.slice(0,3):[]),z=x(()=>t.progressUpdates.map(f=>{var S,$,E;return typeof f=="object"&&f.stageName?{timestamp:f.timestamp||Date.now(),level:f.isError?"error":f.status==="SUCCESS"?"success":f.status==="FAILURE"?"error":f.status==="PARTIAL_SUCCESS"?"warning":(S=f.message)!=null&&S.includes("✅")?"success":($=f.message)!=null&&$.includes("❌")?"error":(E=f.message)!=null&&E.includes("⚠️")?"warning":"info",message:`[${f.stageName}] ${f.message}${f.traceId?` (${f.traceId.slice(-8)})`:""}`,traceId:f.traceId,status:f.status,errorDetail:f.errorDetail}:{timestamp:Date.now(),level:f.includes("✅")?"success":f.includes("❌")?"error":f.includes("⚠️")?"warning":"info",message:f}})),V=f=>{const S=Math.floor(f/1e3),$=Math.floor(S/60),E=S%60;return`${$.toString().padStart(2,"0")}:${E.toString().padStart(2,"0")}`},X=f=>({zh:"中文 (简体)","zh-TW":"中文 (繁体)",en:"English",ja:"日本語",ko:"한国语",es:"Español",fr:"Français",de:"Deutsch",ru:"Русский",ar:"العربية"})[f]||f,F=f=>({fast:"快速翻译",balanced:"平衡模式",quality:"高质量"})[f]||f,R=f=>({formal:"正式",casual:"随意",literary:"文学",technical:"技术"})[f]||f,I=async()=>{try{await t.translateSubtitles({skipCache:o.value})}catch(f){console.error("翻译字幕时出错:",f)}},D=async()=>{try{await t.translateSubtitles({forceRetranslate:!0,skipCache:o.value})}catch(f){console.error("重新翻译字幕时出错:",f)}},O=()=>{t.cancelCurrentOperation()},J=()=>{t.setCurrentStep(7)},K=()=>{t.setCurrentStep(9)},j=()=>{t.progressUpdates=[]},U=f=>{console.log("日志已复制:",f)},k=f=>{f.step&&t.setCurrentStep(f.step)};return(f,S)=>(n(),i("div",da,[S[19]||(S[19]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 8: 翻译处理"),e("p",{class:"text-sm text-gray-500 mt-1"},"将字幕翻译成目标语言")],-1)),ke(Ue,{"process-state":te(t).currentProcessState},null,8,["process-state"]),r.value?(n(),i("div",ca,[e("div",ga,[S[2]||(S[2]=e("div",{class:"flex items-center"},[e("svg",{class:"h-4 w-4 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})]),e("span",{class:"text-sm font-medium text-green-800"},"翻译选择已完成")],-1)),e("span",ma,"目标语言: "+a(X(d.value.targetLanguage)),1)])])):c("",!0),r.value?c("",!0):(n(),i("div",pa,[e("div",fa,[S[5]||(S[5]=e("svg",{class:"h-5 w-5 text-yellow-600 mr-3 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",va,[S[3]||(S[3]=e("h4",{class:"text-sm font-medium text-yellow-800"},"需要先完成翻译选择",-1)),S[4]||(S[4]=e("p",{class:"text-sm text-yellow-700 mt-1"},"请先在步骤7中完成翻译选择，设置翻译参数后再开始翻译",-1)),e("button",{onClick:S[0]||(S[0]=$=>k({step:7})),class:"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900 transition-colors"}," 前往翻译选择 ")])])])),r.value&&!p.value?(n(),i("div",ba,[e("div",xa,[S[10]||(S[10]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"翻译设置",-1)),e("div",ha,[e("div",ya,[S[6]||(S[6]=e("span",{class:"text-gray-600 font-medium"},"目标语言:",-1)),e("div",Sa,a(X(d.value.targetLanguage)),1)]),e("div",wa,[S[7]||(S[7]=e("span",{class:"text-gray-600 font-medium"},"翻译质量:",-1)),e("div",_a,a(F(d.value.quality)),1)]),e("div",ka,[S[8]||(S[8]=e("span",{class:"text-gray-600 font-medium"},"翻译风格:",-1)),e("div",$a,a(R(d.value.style)),1)]),e("div",Ta,[S[9]||(S[9]=e("span",{class:"text-gray-600 font-medium"},"字幕数量:",-1)),e("div",Ca,a(u.value)+" 个",1)])])]),e("div",Ea,[S[11]||(S[11]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"原文字幕预览",-1)),e("div",Ma,[e("div",Pa,[(n(!0),i(Y,null,ee(T.value,($,E)=>(n(),i("div",{key:E,class:"text-sm"},[e("span",Aa,a(V($.startTimeMs))+" → "+a(V($.endTimeMs)),1),e("span",La,a($.text),1)]))),128))])])]),e("div",Ia,[S[13]||(S[13]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"翻译选项",-1)),e("label",Ra,[q(e("input",{type:"checkbox","onUpdate:modelValue":S[1]||(S[1]=$=>o.value=$),class:"rounded border-gray-300 text-pink-600 shadow-sm focus:border-pink-300 focus:ring focus:ring-pink-200 focus:ring-opacity-50"},null,512),[[ae,o.value]]),S[12]||(S[12]=e("span",null,"本次不使用缓存",-1)),e("span",Fa,a(o.value?"⚠️ 强制重新翻译":"💾 使用缓存加速"),1)])]),e("div",Na,[e("button",{onClick:I,disabled:l.value,class:"flex-1 px-4 py-3 text-white bg-pink-600 rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors"},[S[14]||(S[14]=e("svg",{class:"h-4 w-4 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})],-1)),ue(" "+a(l.value?"正在翻译...":`开始翻译成${X(d.value.targetLanguage)}`),1)],8,Ua),l.value?(n(),i("button",{key:0,onClick:O,class:"px-3 py-3 text-red-700 bg-red-100 rounded-lg hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 font-medium transition-colors"},S[15]||(S[15]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):c("",!0)])])):c("",!0),p.value?(n(),i("div",Oa,[e("div",za,[S[18]||(S[18]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"翻译结果预览",-1)),e("div",Da,[e("div",ja,[(n(!0),i(Y,null,ee(m.value,($,E)=>(n(),i("div",{key:E,class:"border-b border-gray-200 pb-2 last:border-b-0"},[e("div",Ba,a(V($.startTimeMs))+" → "+a(V($.endTimeMs)),1),e("div",Wa,[S[16]||(S[16]=e("span",{class:"font-medium text-gray-500"},"原文:",-1)),ue(" "+a($.originalText),1)]),e("div",Va,[S[17]||(S[17]=e("span",{class:"font-medium text-pink-600"},"译文:",-1)),ue(" "+a($.translatedText),1)])]))),128))])]),e("div",Ga,[e("span",qa,"共翻译 "+a(b.value)+" 个字幕片段",1)]),e("div",Ha,[e("div",{class:"flex justify-start"},[e("button",{onClick:J,class:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors text-base"}," ← 返回选择 ")]),e("div",Ka,[e("button",{onClick:D,disabled:l.value,class:"flex-1 px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🔄 重新翻译 ",8,Ja),e("button",{onClick:K,disabled:l.value,class:"flex-1 px-6 py-3 text-white bg-pink-600 rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," ➡️ 进入导出 ",8,Xa)])])])])):c("",!0),l.value?(n(),Z($e,{key:4,"operation-name":"翻译字幕","operation-status":v.value,"progress-percentage":w.value,"current-segment":h.value,"total-segments":P.value,theme:"pink"},null,8,["operation-status","progress-percentage","current-segment","total-segments"])):c("",!0),y.value?(n(),Z(Me,{key:5,error:M.value,title:"翻译过程中出现错误",onRetry:I},null,8,["error"])):c("",!0),te(t).progressUpdates.length>0?(n(),Z(ye,{key:6,title:"翻译日志",logs:z.value,"initial-expanded":!1,"show-stats":!0,onClear:j,onCopy:U},null,8,["logs"])):c("",!0)]))}},Ya=re(Qa,[["__scopeId","data-v-2572a627"]]),Za={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},eu={key:0,class:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"},tu={class:"flex items-center justify-between"},su={class:"text-sm text-blue-600"},ou={key:2,class:"current-actions mb-6"},ru={class:"bg-gray-50 rounded-lg p-4 mb-4"},nu={class:"grid grid-cols-1 md:grid-cols-2 gap-3"},lu=["value"],iu={class:"flex-1"},au={class:"text-sm font-medium text-gray-800"},uu={class:"text-xs text-gray-600"},du={class:"bg-gray-50 rounded-lg p-4 mb-4"},cu={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},gu=["value"],mu={class:"text-center"},pu={class:"text-sm font-medium text-gray-800"},fu={class:"text-xs text-gray-600"},vu={key:0,class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4"},bu={class:"text-sm font-medium text-blue-800 mb-3"},xu={class:"bg-white rounded-md p-3 border border-blue-200"},hu={class:"space-y-1 max-h-32 overflow-y-auto"},yu={class:"mt-3 pt-3 border-t border-blue-200 text-xs text-blue-600"},Su={key:1,class:"bg-gray-50 rounded-lg p-4 mb-4"},wu={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},_u={class:"mt-3"},ku={class:"flex items-center space-x-2 text-sm text-gray-700"},$u={class:"bg-gray-50 rounded-lg p-4 mb-4"},Tu={class:"space-y-3"},Cu={class:"flex items-center space-x-2 text-sm text-gray-700"},Eu={key:0,class:"ml-6 text-xs text-gray-600"},Mu={class:"flex space-x-3"},Pu=["disabled"],Au=["disabled"],Lu={key:5,class:"mb-6"},Iu={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Ru={class:"flex items-center justify-between mb-3"},Fu={class:"text-sm text-green-700 mb-3"},Nu={key:0,class:"space-y-3"},Uu={class:"bg-white rounded-md p-3 border border-green-200"},Ou={class:"space-y-1 max-h-32 overflow-y-auto"},zu={class:"text-gray-700 font-mono"},Du={class:"text-gray-500"},ju={class:"flex items-center justify-between text-xs text-green-600"},Bu={key:0,class:"mt-8 flex justify-center"},Wu={class:"p-6 border-b border-gray-200"},Vu={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Gu=["value"],qu=["value"],Hu={class:"p-6 overflow-y-auto max-h-96"},Ku={key:0,class:"bg-gray-50 rounded-lg p-4"},Ju={class:"flex items-center justify-between mb-3"},Xu={class:"text-sm font-medium text-gray-700"},Qu={class:"text-xs text-gray-500"},Yu={class:"text-sm text-gray-800 whitespace-pre-wrap font-mono bg-white border border-gray-200 rounded p-3 overflow-x-auto"},Zu={key:1,class:"text-center text-gray-500 py-8"},ed={__name:"ExportStep",setup(s){const t=ce(),o=L(["source_only"]),l=L(["srt"]),r=L(!1),u=L(0),p=L(""),b=L([]),w=L([]),d=L(!1),v=L(!1),y=L("source_only"),M=L("srt"),h=L(""),P=Ee({separator:"newline",originalFont:"16px",translationFont:"14px",addLanguageLabels:!1}),T=x(()=>{var G;const C=((G=t.translatedSubtitles)==null?void 0:G.length)>0,g=K(),B=[{value:"source_only",label:`仅原文 (${g.sourceName})`,description:"只包含原始语言内容"}];return C&&B.push({value:"target_only",label:`仅译文 (${g.targetName})`,description:"只包含翻译语言内容"},{value:"source_first",label:`原文在上 (${g.sourceName}在上，${g.targetName}在下)`,description:"原文在上，译文在下"},{value:"target_first",label:`译文在上 (${g.targetName}在上，${g.sourceName}在下)`,description:"译文在上，原文在下"}),B}),m=x(()=>[{value:"srt",label:"SRT",extension:".srt",description:"最常用的字幕格式"},{value:"vtt",label:"VTT",extension:".vtt",description:"Web视频字幕格式"},{value:"ass",label:"ASS",extension:".ass",description:"高级字幕格式"},{value:"txt",label:"TXT",extension:".txt",description:"纯文本格式"}]),z=x(()=>t.isLoading),V=x(()=>t.hasEditableSegments),X=x(()=>t.currentOperationStatus||ie.IDLE),F=x(()=>t.lastError&&(X.value===ie.ERROR||X.value===ie.CANCELED)),R=x(()=>t.lastError||{}),I=x(()=>t.currentSegmentIndex||0),D=x(()=>t.totalSegments||(t.editableSegments?t.editableSegments.length:0)),O=x(()=>t.exportProgress||0),J=C=>({zh:"中文 (简体)","zh-TW":"中文 (繁体)",en:"English",ja:"日本語",ko:"한국어",es:"Español",fr:"Français",de:"Deutsch",ru:"Русский",ar:"العربية"})[C]||C,K=()=>{const C=t.translationSettings;if(C&&C.targetLanguage){const g=C.sourceLanguage||"en",B=C.targetLanguage;return{source:g,target:B,sourceName:J(g),targetName:J(B)}}if(t.editableSegments&&t.editableSegments.length>0){const g=t.editableSegments[0];if(g.translatedText||g.translation){const B=g.text||"",G=g.translatedText||g.translation||"",W=de=>/[\u4e00-\u9fff]/.test(de)?"zh":/[a-zA-Z]/.test(de)?"en":/[\u3040-\u309f\u30a0-\u30ff]/.test(de)?"ja":/[\uac00-\ud7af]/.test(de)?"ko":"en",A=W(B),oe=W(G);return{source:A,target:oe,sourceName:J(A),targetName:J(oe)}}}return{source:"en",target:"zh",sourceName:"English",targetName:"中文 (简体)"}},j=x(()=>{var B;const C=((B=t.translatedSubtitles)==null?void 0:B.length)>0,g=K();return C?`原文: ${g.sourceName} | 译文: ${g.targetName}`:`仅${g.sourceName}`}),U=x(()=>o.value.length*l.value.length),k=x(()=>{var B,G;const C=[],g=((B=t.getUploadedFileName)==null?void 0:B.replace(/\.[^/.]+$/,""))||"video";for(const W of o.value)for(const A of l.value){const oe=_(W),de=((G=m.value.find(me=>me.value===A))==null?void 0:G.extension)||".srt";C.push(`${g}_${oe}${de}`)}return C}),f=x(()=>{var W;const G=(((W=t.editableSegments)==null?void 0:W.length)||0)*50*o.value.length*2;return G<1024?`${G}B`:G<1024*1024?`${(G/1024).toFixed(1)}KB`:`${(G/1024/1024).toFixed(1)}MB`}),S=x(()=>o.value.some(C=>C==="source_first"||C==="target_first")),$=x(()=>{if(w.value.length===0)return"0B";const C=w.value.reduce((g,B)=>{const G=B.size.match(/(\d+(?:\.\d+)?)\s*([KMGT]?B)/i);if(G){const W=parseFloat(G[1]),A=G[2].toUpperCase(),oe={B:1,KB:1024,MB:1024*1024,GB:1024*1024*1024};return g+W*(oe[A]||1)}return g},0);return C<1024?`${C}B`:C<1024*1024?`${(C/1024).toFixed(1)}KB`:`${(C/1024/1024).toFixed(1)}MB`}),E=x(()=>t.progressUpdates.map(C=>({timestamp:new Date().toISOString(),level:C.includes("✅")?"success":C.includes("❌")?"error":C.includes("⚠️")?"warning":"info",message:C}))),N=x(()=>w.value.length>0&&!z.value),_=C=>({source_only:"仅原文",target_only:"仅译文",source_first:"原文在上",target_first:"译文在上"})[C]||C,se=C=>{C.key==="goto-edit"&&t.setCurrentStep(3)},ne=async()=>{var B,G;if(o.value.length===0){alert("请至少选择一个内容类型");return}if(l.value.length===0){alert("请至少选择一个导出格式");return}u.value=0,p.value="准备导出...",b.value=[],w.value=[];const C=[],g=((B=t.getUploadedFileName)==null?void 0:B.replace(/\.[^/.]+$/,""))||"video";for(const W of o.value)for(const A of l.value){const oe=_(W),de=((G=m.value.find(Ke=>Ke.value===A))==null?void 0:G.extension)||".srt",me=`${g}_${oe}${de}`;C.push({content:W,format:A,filename:me,status:"waiting"})}b.value=C.map(W=>({file:W.filename,status:"waiting"}));try{for(let W=0;W<C.length;W++){const A=C[W];b.value[W].status="processing",p.value=`正在生成 ${A.filename}...`,u.value=W/C.length*100,await new Promise(me=>setTimeout(me,1e3)),t.setExportFormat(A.format),t.setExportFilename(A.filename.replace(/\.[^/.]+$/,""));const oe={source_only:"editable_segments",target_only:"translation_result",source_first:"editable_segments",target_first:"editable_segments"};t.setExportContentSource(oe[A.content]||"editable_segments");const de={source_only:"仅原文",target_only:"仅译文",source_first:"原文在上",target_first:"译文在上"};t.setExportLayout(de[A.content]||"仅原文");try{t.setExportAutoSaveToDefault(d.value),await t.exportSubtitles({filename:A.filename.replace(/\.[^/.]+$/,"")}),t.lastExportPath?(b.value[W].status="completed",w.value.push({filename:A.filename,path:t.lastExportPath,size:"未知",directory:t.lastExportPath.substring(0,t.lastExportPath.lastIndexOf("/"))})):b.value[W].status="error"}catch(me){console.error(`导出 ${A.filename} 失败:`,me),b.value[W].status="error"}}u.value=100,p.value="导出完成！"}catch(W){console.error("批量导出失败:",W),p.value="导出失败"}},Q=()=>{y.value=o.value[0]||"source_only",M.value=l.value[0]||"srt",h.value="",v.value=!0},xe=()=>{v.value=!1,h.value=""},Oe=()=>{if(!t.editableSegments||t.editableSegments.length===0){h.value="没有可预览的字幕内容";return}const C=t.editableSegments.slice(0,10);let g="";switch(M.value){case"srt":g=Pe(C);break;case"vtt":g=ze(C);break;case"ass":g=De(C);break;case"txt":g=je(C);break;default:g=Pe(C)}h.value=g},Pe=C=>C.map((g,B)=>{const G=Ae(g.startTimeMs),W=Ae(g.endTimeMs),A=Se(g);return`${B+1}
${G} --> ${W}
${A}
`}).join(`
`),ze=C=>{let g=`WEBVTT

`;return g+=C.map((B,G)=>{const W=Le(B.startTimeMs),A=Le(B.endTimeMs),oe=Se(B);return`${W} --> ${A}
${oe}
`}).join(`
`),g},De=C=>{let g=`[Script Info]
Title: 字幕预览

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`;return g+=C.map(B=>{const G=Ie(B.startTimeMs),W=Ie(B.endTimeMs),A=Se(B);return`Dialogue: 0,${G},${W},Default,,0,0,0,,${A}`}).join(`
`),g},je=C=>C.map(g=>Se(g)).join(`
`),Se=C=>{const g=C.text||"";let B="";if(C.translatedText)B=C.translatedText;else if(C.translation)B=C.translation;else if(t.translatedSubtitles&&t.translatedSubtitles.length>0){const G=t.translatedSubtitles.find(W=>W.startTimeMs===C.startTimeMs&&W.endTimeMs===C.endTimeMs);G&&(B=G.text||G.translatedText||"")}switch(y.value){case"source_only":return g;case"target_only":return B||g;case"source_first":return B?`${g}
${B}`:g;case"target_first":return B?`${B}
${g}`:g;default:return g}},Ae=C=>{const g=Math.floor(C/1e3),B=Math.floor(g/3600),G=Math.floor(g%3600/60),W=g%60,A=C%1e3;return`${B.toString().padStart(2,"0")}:${G.toString().padStart(2,"0")}:${W.toString().padStart(2,"0")},${A.toString().padStart(3,"0")}`},Le=C=>{const g=Math.floor(C/1e3),B=Math.floor(g/3600),G=Math.floor(g%3600/60),W=g%60,A=C%1e3;return`${B.toString().padStart(2,"0")}:${G.toString().padStart(2,"0")}:${W.toString().padStart(2,"0")}.${A.toString().padStart(3,"0")}`},Ie=C=>{const g=Math.floor(C/1e3),B=Math.floor(g/3600),G=Math.floor(g%3600/60),W=g%60,A=Math.floor(C%1e3/10);return`${B}:${G.toString().padStart(2,"0")}:${W.toString().padStart(2,"0")}.${A.toString().padStart(2,"0")}`},Be=()=>{r.value=!r.value},We=()=>{var C;if(w.value.length>0){const g=w.value[0].directory;(C=window.electronAPI)==null||C.openFolder(g)}},Ve=async()=>{const C=w.value.map(g=>g.path).join(`
`);try{await navigator.clipboard.writeText(C),console.log("路径已复制到剪贴板")}catch(g){console.error("复制失败:",g)}},Ge=()=>{t.resetWorkflow(),t.setCurrentStep(1)},qe=()=>{t.progressUpdates=[]},He=C=>{console.log("日志已复制:",C)};return(C,g)=>{var B,G,W;return n(),i(Y,null,[e("div",Za,[g[27]||(g[27]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 6: 导出字幕"),e("p",{class:"text-sm text-gray-500 mt-1"},"选择格式和样式导出最终字幕")],-1)),V.value?(n(),i("div",eu,[e("div",tu,[g[10]||(g[10]=e("div",{class:"flex items-center"},[e("span",{class:"text-lg mr-2"},"🌍"),e("span",{class:"text-sm font-medium text-blue-800"},"当前语言设置")],-1)),e("div",su,a(j.value),1)])])):c("",!0),V.value?c("",!0):(n(),Z(pe,{key:1,type:"warning",title:"需要先完成前面的步骤",message:"请先完成字幕编辑或翻译处理，准备好字幕内容后再进行导出",actions:[{key:"goto-edit",label:"前往字幕编辑",icon:"📝",primary:!0}],onAction:se})),V.value&&!z.value?(n(),i("div",ou,[e("div",ru,[g[11]||(g[11]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"📋 内容选择 (可多选)",-1)),e("div",nu,[(n(!0),i(Y,null,ee(T.value,A=>(n(),i("label",{key:A.value,class:H(["flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200",o.value.includes(A.value)?"border-blue-500 bg-blue-50":"border-gray-200 bg-white hover:bg-gray-50"])},[q(e("input",{type:"checkbox",value:A.value,"onUpdate:modelValue":g[0]||(g[0]=oe=>o.value=oe),class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-3"},null,8,lu),[[ae,o.value]]),e("div",iu,[e("div",au,a(A.label),1),e("div",uu,a(A.description),1)])],2))),128))])]),e("div",du,[g[12]||(g[12]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"📄 格式选择 (可多选)",-1)),e("div",cu,[(n(!0),i(Y,null,ee(m.value,A=>(n(),i("label",{key:A.value,class:H(["flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200",l.value.includes(A.value)?"border-green-500 bg-green-50":"border-gray-200 bg-white hover:bg-gray-50"])},[q(e("input",{type:"checkbox",value:A.value,"onUpdate:modelValue":g[1]||(g[1]=oe=>l.value=oe),class:"h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500 mr-2"},null,8,gu),[[ae,l.value]]),e("div",mu,[e("div",pu,a(A.label),1),e("div",fu,a(A.extension),1)])],2))),128))])]),o.value.length>0&&l.value.length>0?(n(),i("div",vu,[e("h4",bu,"📊 导出预览 ("+a(U.value)+"个文件)",1),e("div",xu,[g[13]||(g[13]=e("div",{class:"text-xs text-blue-600 mb-2"},"📁 将生成文件:",-1)),e("div",hu,[(n(!0),i(Y,null,ee(k.value,A=>(n(),i("div",{key:A,class:"text-sm text-gray-700 font-mono"},a(A),1))),128))]),e("div",yu," 📊 预计总大小: "+a(f.value),1)])])):c("",!0),S.value?(n(),i("div",Su,[g[21]||(g[21]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"🎨 双语样式设置",-1)),e("div",wu,[e("div",null,[g[15]||(g[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"分隔方式:",-1)),q(e("select",{"onUpdate:modelValue":g[2]||(g[2]=A=>P.separator=A),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},g[14]||(g[14]=[e("option",{value:"newline"},"换行",-1),e("option",{value:"space"},"空格",-1),e("option",{value:"pipe"},"|",-1)]),512),[[le,P.separator]])]),e("div",null,[g[17]||(g[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"原文字体:",-1)),q(e("select",{"onUpdate:modelValue":g[3]||(g[3]=A=>P.originalFont=A),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},g[16]||(g[16]=[e("option",{value:"16px"},"16px",-1),e("option",{value:"18px"},"18px",-1),e("option",{value:"20px"},"20px",-1)]),512),[[le,P.originalFont]])]),e("div",null,[g[19]||(g[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"译文字体:",-1)),q(e("select",{"onUpdate:modelValue":g[4]||(g[4]=A=>P.translationFont=A),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},g[18]||(g[18]=[e("option",{value:"14px"},"14px",-1),e("option",{value:"16px"},"16px",-1),e("option",{value:"18px"},"18px",-1)]),512),[[le,P.translationFont]])])]),e("div",_u,[e("label",ku,[q(e("input",{type:"checkbox","onUpdate:modelValue":g[5]||(g[5]=A=>P.addLanguageLabels=A),class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[ae,P.addLanguageLabels]]),g[20]||(g[20]=e("span",null,"添加语言标识 (🇨🇳/🇺🇸)",-1))])])])):c("",!0),e("div",$u,[g[24]||(g[24]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"⚙️ 导出设置",-1)),e("div",Tu,[e("label",Cu,[q(e("input",{type:"checkbox","onUpdate:modelValue":g[6]||(g[6]=A=>d.value=A),class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[ae,d.value]]),g[22]||(g[22]=e("span",null,"使用默认地址（不弹出确认对话框）",-1))]),d.value?(n(),i("div",Eu,g[23]||(g[23]=[e("span",null,"默认路径: ~/Downloads/subtitles/",-1)]))):c("",!0)])]),e("div",Mu,[e("button",{onClick:ne,disabled:z.value||!V.value||o.value.length===0||l.value.length===0,class:"flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 📥 批量导出 ",8,Pu),e("button",{onClick:Q,disabled:z.value||!V.value||o.value.length===0||l.value.length===0,class:"px-6 py-3 text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 👁️ 预览 ",8,Au)])])):c("",!0),z.value?(n(),Z($e,{key:3,"operation-name":"导出字幕","operation-status":X.value,"progress-percentage":O.value,"current-segment":I.value,"total-segments":D.value,theme:"blue"},null,8,["operation-status","progress-percentage","current-segment","total-segments"])):c("",!0),F.value?(n(),Z(Me,{key:4,error:R.value,title:"导出过程中出现错误",onRetry:C.handleExport},null,8,["error","onRetry"])):c("",!0),w.value.length>0&&!z.value?(n(),i("div",Lu,[e("div",Iu,[e("div",Ru,[g[25]||(g[25]=e("h4",{class:"text-sm font-medium text-green-800"},"✅ 批量导出成功！",-1)),e("button",{onClick:Be,class:"text-xs text-green-600 hover:text-green-800 focus:outline-none"},a(r.value?"隐藏详情":"查看详情")+" "+a(r.value?"▲":"▼"),1)]),e("p",Fu,"已生成 "+a(w.value.length)+" 个文件",1),r.value?(n(),i("div",Nu,[e("div",Uu,[g[26]||(g[26]=e("div",{class:"text-xs text-green-600 mb-2"},"📁 已生成文件:",-1)),e("div",Ou,[(n(!0),i(Y,null,ee(w.value,A=>(n(),i("div",{key:A.path,class:"flex items-center justify-between text-sm"},[e("span",zu,a(A.filename),1),e("span",Du,"("+a(A.size)+")",1)]))),128))])]),e("div",ju,[e("span",null,"📂 保存位置: "+a((B=w.value[0])==null?void 0:B.directory),1),e("span",null,"📊 总大小: "+a($.value),1)]),e("div",{class:"flex space-x-2"},[e("button",{onClick:We,class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," 📂 打开文件夹 "),e("button",{onClick:Ve,class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," 📋 复制路径 ")])])):c("",!0)])])):c("",!0),te(t).progressUpdates.length>0?(n(),Z(ye,{key:6,title:"导出日志",logs:E.value,"initial-expanded":!1,"show-stats":!0,onClear:qe,onCopy:He},null,8,["logs"])):c("",!0)]),N.value?(n(),i("div",Bu,[e("button",{onClick:Ge,class:"px-8 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors text-base shadow-lg"}," 🔄 新任务 ")])):c("",!0),v.value?(n(),i("div",{key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:xe},[e("div",{class:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",onClick:g[9]||(g[9]=_e(()=>{},["stop"]))},[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-200"},[g[29]||(g[29]=e("h3",{class:"text-lg font-semibold text-gray-800"},"👁️ 字幕预览",-1)),e("button",{onClick:xe,class:"text-gray-400 hover:text-gray-600 focus:outline-none"},g[28]||(g[28]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Wu,[e("div",Vu,[e("div",null,[g[30]||(g[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"预览内容类型:",-1)),q(e("select",{"onUpdate:modelValue":g[7]||(g[7]=A=>y.value=A),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[(n(!0),i(Y,null,ee(o.value,A=>(n(),i("option",{key:A,value:A},a(_(A)),9,Gu))),128))],512),[[le,y.value]])]),e("div",null,[g[31]||(g[31]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"预览格式:",-1)),q(e("select",{"onUpdate:modelValue":g[8]||(g[8]=A=>M.value=A),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[(n(!0),i(Y,null,ee(l.value,A=>{var oe;return n(),i("option",{key:A,value:A},a(((oe=m.value.find(de=>de.value===A))==null?void 0:oe.label)||A),9,qu)}),128))],512),[[le,M.value]])])]),e("div",{class:"mt-4 flex justify-center"},[e("button",{onClick:Oe,class:"px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors"}," 🔄 生成预览 ")])]),e("div",Hu,[h.value?(n(),i("div",Ku,[e("div",Ju,[e("h4",Xu," 预览: "+a(_(y.value))+" - "+a((G=m.value.find(A=>A.value===M.value))==null?void 0:G.label),1),e("span",Qu,"显示前10条，总共"+a(((W=te(t).editableSegments)==null?void 0:W.length)||0)+"条",1)]),e("pre",Yu,a(h.value),1)])):(n(),i("div",Zu,g[32]||(g[32]=[e("p",null,'请选择内容类型和格式，然后点击"生成预览"',-1)])))]),e("div",{class:"flex justify-end space-x-3 p-6 border-t border-gray-200"},[e("button",{onClick:xe,class:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-medium transition-colors"}," 关闭 ")])])])):c("",!0)],64)}}},td=re(ed,[["__scopeId","data-v-f5586d70"]]),sd={class:"p-6 bg-white shadow-xl rounded-2xl border border-gray-100"},od={key:0,class:"flex items-center justify-center mb-4"},rd={key:1},nd={class:"mb-6"},ld={class:"flex space-x-4"},id={class:"flex items-center cursor-pointer"},ad={class:"flex items-center"},ud={key:0,class:"w-2 h-2 bg-white rounded-full m-0.5"},dd={class:"flex items-center cursor-pointer"},cd={class:"flex items-center"},gd={key:0,class:"w-2 h-2 bg-white rounded-full m-0.5"},md={key:0,class:"mb-6"},pd=["disabled"],fd={key:0,class:"text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded"},vd={key:1,class:"mb-6"},bd={class:"flex items-center space-x-3"},xd=["disabled"],hd={class:"flex items-center"},yd={key:0,class:"mt-3 p-3 bg-purple-50 rounded-lg border border-purple-200"},Sd={class:"text-sm text-purple-700 font-medium mb-2"},wd={class:"max-h-32 overflow-y-auto"},_d={class:"text-xs text-purple-600 space-y-1"},kd={key:0,class:"text-purple-500 italic"},$d={key:1,class:"mt-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200"},Td={class:"mb-6"},Cd={key:2,class:"mb-6"},Ed={class:"mb-6"},Md={key:3,class:"mb-6"},Pd={class:"space-y-2"},Ad=["value"],Ld={class:"ml-2 text-sm text-gray-700"},Id=["disabled"],Rd={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},Fd={key:1,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 mr-2"},Nd={key:2,class:"mt-4 p-3 bg-red-700 text-red-100 rounded"},Ud={key:3,class:"mt-4 p-3 bg-green-700 text-green-100 rounded"},Od={key:4,class:"mt-4"},zd={class:"list-disc list-inside bg-gray-750 p-3 rounded max-h-60 overflow-y-auto text-sm"},Dd={key:5,class:"mt-4"},jd={__name:"OneClickOperation",setup(s){const t=ce(),o=L(null),l=L("single"),r=L(""),u=L([]),p=L(t.oneClickWorkflowType),b=L(t.oneClickTargetLanguage),w=L(t.oneClickExportFormat),d=L(t.oneClickExportLayout),v=L([{value:"original_top",label:"原文在上，译文在下"},{value:"translation_top",label:"译文在上，原文在下"},{value:"original_only",label:"仅显示原文"},{value:"translation_only",label:"仅显示译文"}]),y=L([t.oneClickExportLayout||"original_top"]),M=x(()=>t.isLoading),h=x(()=>t.oneClickOperationInProgress),P=x(()=>t.oneClickOperationError),T=x(()=>t.lastExportPath),m=x(()=>t.progressUpdates),z=x(()=>t.uploadedFile),V=x(()=>l.value==="single"?z.value!==null:r.value&&u.value.length>0);ge(()=>t.oneClickWorkflowType,k=>{p.value=k}),ge(()=>t.oneClickTargetLanguage,k=>{b.value=k}),ge(()=>t.oneClickExportFormat,k=>{w.value=k}),ge(()=>t.oneClickExportLayout,k=>{d.value=k});const X=()=>{t.setOneClickWorkflowType(p.value)},F=()=>{t.setOneClickTargetLanguage(b.value)},R=()=>{t.setOneClickExportFormat(w.value)},I=()=>{y.value.length===0&&(y.value=["original_top"]),t.setOneClickExportLayouts(y.value),t.setOneClickExportLayout(y.value[0])},D=k=>{const f=k.target.files[0];f?(t.setUploadedFile({name:f.name,path:f.path,type:f.type,size:f.size}),o.value=f):(t.setUploadedFile(null),o.value=null)},O=async()=>{try{const k=await window.electronAPI.selectFolder();k&&k.folderPath&&(r.value=k.folderPath,u.value=k.mediaFiles||[])}catch(k){console.error("选择文件夹时出错:",k),t.$patch({oneClickOperationError:"选择文件夹时出错: "+k.message})}},J=async()=>{if(l.value==="single"){if(!t.uploadedFile){t.$patch({oneClickOperationError:"请先选择一个文件。"});return}}else if(!r.value||u.value.length===0){t.$patch({oneClickOperationError:"请先选择包含音视频文件的文件夹。"});return}t.setOneClickWorkflowType(p.value),p.value.includes("trans")&&(t.setOneClickTargetLanguage(b.value),t.setOneClickExportLayout(d.value),t.setOneClickExportLayouts(y.value)),t.setOneClickExportFormat(w.value),l.value==="single"?await t.performOneClickOperation():await K()},K=async()=>{try{t.$patch({oneClickOperationInProgress:!0,oneClickOperationError:null,progressUpdates:[]});const k=u.value.length;let f=0,S=0,$=0;const E={workflowType:p.value,targetLanguage:b.value,exportFormat:w.value,exportLayout:d.value,exportLayouts:y.value};t.progressUpdates.push(`🚀 开始批量处理 ${k} 个文件...`),t.progressUpdates.push(`📋 设置: ${E.workflowType}, ${E.targetLanguage}, ${E.exportFormat}, ${E.exportLayout}`);for(const N of u.value){const _=`${r.value}/${N}`;f++;try{t.progressUpdates.push(`
[${f}/${k}] 🎬 开始处理: ${N}`),t.resetWorkflow(),t.setUploadedFile({name:N,path:_,type:j(N),size:0}),t.setOneClickWorkflowType(E.workflowType),t.setOneClickTargetLanguage(E.targetLanguage),t.setOneClickExportFormat(E.exportFormat),t.setOneClickExportLayout(E.exportLayout),t.setOneClickExportLayouts(E.exportLayouts),console.log(`[Batch] Processing file ${f}/${k}: ${N}`),console.log("[Batch] Settings:",E),await t.performOneClickOperation(),S++,t.progressUpdates.push(`✅ [${f}/${k}] 完成: ${N}`),t.lastExportPath&&t.progressUpdates.push(`📁 导出到: ${t.lastExportPath}`)}catch(se){$++,console.error(`处理文件 ${N} 时出错:`,se),t.progressUpdates.push(`❌ [${f}/${k}] 失败: ${N}`),t.progressUpdates.push(`   错误: ${se.message}`)}}t.progressUpdates.push(`
🎉 批量处理完成!`),t.progressUpdates.push(`✅ 成功处理: ${S} 个文件`),$>0&&t.progressUpdates.push(`❌ 处理失败: ${$} 个文件`),t.$patch({oneClickOperationInProgress:!1,lastExportPath:`批量处理完成 - 成功: ${S}, 失败: ${$}`})}catch(k){console.error("批量处理时出错:",k),t.$patch({oneClickOperationInProgress:!1,oneClickOperationError:`批量处理失败: ${k.message}`})}},j=k=>{const f=k.split(".").pop().toLowerCase(),S=["mp4","avi","mov","mkv","wmv","flv","webm","m4v"],$=["mp3","wav","flac","aac","ogg","wma","m4a"];return S.includes(f)?"video/"+f:$.includes(f)?"audio/"+f:"application/octet-stream"},U=()=>{t.resetWorkflow(),o.value=null,r.value="",u.value=[],l.value="single";const k=document.getElementById("oneClickFile");k&&(k.value="")};return(k,f)=>(n(),i("div",sd,[f[27]||(f[27]=e("h2",{class:"text-2xl font-bold mb-6 text-gray-800 flex items-center"},[e("div",{class:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 text-white"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"})])]),ue(" 一键字幕生成 ")],-1)),h.value||M.value?(n(),i("div",od,f[6]||(f[6]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-blue-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),e("span",{class:"text-gray-300"},"Processing... Please wait.",-1)]))):c("",!0),!h.value&&!M.value?(n(),i("div",rd,[e("div",nd,[f[9]||(f[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"处理模式:",-1)),e("div",ld,[e("label",id,[q(e("input",{type:"radio","onUpdate:modelValue":f[0]||(f[0]=S=>l.value=S),value:"single",class:"sr-only"},null,512),[[Fe,l.value]]),e("div",ad,[e("div",{class:H(["w-4 h-4 rounded-full border-2 mr-2 transition-all duration-200",l.value==="single"?"border-blue-500 bg-blue-500":"border-gray-300"])},[l.value==="single"?(n(),i("div",ud)):c("",!0)],2),f[7]||(f[7]=e("span",{class:"text-sm text-gray-700"},"单文件处理",-1))])]),e("label",dd,[q(e("input",{type:"radio","onUpdate:modelValue":f[1]||(f[1]=S=>l.value=S),value:"batch",class:"sr-only"},null,512),[[Fe,l.value]]),e("div",cd,[e("div",{class:H(["w-4 h-4 rounded-full border-2 mr-2 transition-all duration-200",l.value==="batch"?"border-purple-500 bg-purple-500":"border-gray-300"])},[l.value==="batch"?(n(),i("div",gd)):c("",!0)],2),f[8]||(f[8]=e("span",{class:"text-sm text-gray-700"},"文件夹批量处理",-1))])])])]),l.value==="single"?(n(),i("div",md,[f[10]||(f[10]=e("label",{for:"oneClickFile",class:"block text-sm font-medium text-gray-700 mb-2"}," 选择音视频文件: ",-1)),e("input",{type:"file",id:"oneClickFile",onChange:D,accept:"video/*,audio/*",class:"block w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-500 file:text-white hover:file:bg-blue-600 disabled:opacity-50 border border-gray-200 rounded-lg",disabled:h.value||M.value},null,40,pd),z.value?(n(),i("p",fd," 已选择: "+a(z.value.name)+" ("+a((z.value.size/1024/1024).toFixed(2))+" MB) ",1)):c("",!0)])):c("",!0),l.value==="batch"?(n(),i("div",vd,[f[13]||(f[13]=e("label",{for:"folderSelect",class:"block text-sm font-medium text-gray-700 mb-2"}," 选择包含音视频文件的文件夹: ",-1)),e("div",bd,[e("button",{onClick:O,class:"flex-1 px-4 py-3 border border-gray-300 rounded-lg text-left text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors duration-200",disabled:h.value||M.value},[e("div",hd,[f[11]||(f[11]=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-5 h-5 mr-2 text-purple-500"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25H11.69z"})],-1)),ue(" "+a(r.value||"点击选择文件夹..."),1)])],8,xd)]),r.value&&u.value.length>0?(n(),i("div",yd,[e("p",Sd," 检测到 "+a(u.value.length)+" 个音视频文件: ",1),e("div",wd,[e("ul",_d,[(n(!0),i(Y,null,ee(u.value.slice(0,10),S=>(n(),i("li",{key:S,class:"truncate"}," 📁 "+a(S),1))),128)),u.value.length>10?(n(),i("li",kd," ... 还有 "+a(u.value.length-10)+" 个文件 ",1)):c("",!0)])])])):r.value&&u.value.length===0?(n(),i("div",$d,f[12]||(f[12]=[e("p",{class:"text-sm text-yellow-700"}," ⚠️ 在选择的文件夹中未检测到音视频文件 ",-1)]))):c("",!0)])):c("",!0),e("div",Td,[f[15]||(f[15]=e("label",{for:"workflowType",class:"block text-sm font-medium text-gray-700 mb-2"},"工作流类型:",-1)),q(e("select",{id:"workflowType","onUpdate:modelValue":f[2]||(f[2]=S=>p.value=S),onChange:X,class:"mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg"},f[14]||(f[14]=[we('<option value="vid_to_srt" data-v-a6c5b310>视频 → SRT字幕</option><option value="vid_to_srt_trans" data-v-a6c5b310>视频 → 翻译SRT字幕</option><option value="audio_to_srt" data-v-a6c5b310>音频 → SRT字幕</option><option value="audio_to_srt_trans" data-v-a6c5b310>音频 → 翻译SRT字幕</option><option value="vid_to_text" data-v-a6c5b310>视频 → 纯文本</option><option value="audio_to_text" data-v-a6c5b310>音频 → 纯文本</option>',6)]),544),[[le,p.value]])]),p.value.includes("trans")?(n(),i("div",Cd,[f[17]||(f[17]=e("label",{for:"targetLanguage",class:"block text-sm font-medium text-gray-700 mb-2"},"目标语言:",-1)),q(e("select",{id:"targetLanguage","onUpdate:modelValue":f[3]||(f[3]=S=>b.value=S),onChange:F,class:"mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg"},f[16]||(f[16]=[we('<option value="zh-CN" data-v-a6c5b310>中文（简体）</option><option value="zh-TW" data-v-a6c5b310>中文（繁体）</option><option value="en" data-v-a6c5b310>英语</option><option value="ja" data-v-a6c5b310>日语</option><option value="ko" data-v-a6c5b310>韩语</option><option value="es" data-v-a6c5b310>西班牙语</option><option value="fr" data-v-a6c5b310>法语</option><option value="de" data-v-a6c5b310>德语</option><option value="it" data-v-a6c5b310>意大利语</option><option value="pt" data-v-a6c5b310>葡萄牙语</option><option value="ru" data-v-a6c5b310>俄语</option>',11)]),544),[[le,b.value]])])):c("",!0),e("div",Ed,[f[19]||(f[19]=e("label",{for:"oneClickExportFormat",class:"block text-sm font-medium text-gray-700 mb-2"},"导出格式:",-1)),q(e("select",{id:"oneClickExportFormat","onUpdate:modelValue":f[4]||(f[4]=S=>w.value=S),onChange:R,class:"mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg"},f[18]||(f[18]=[we('<option value="srt" data-v-a6c5b310>SRT (.srt)</option><option value="vtt" data-v-a6c5b310>VTT (.vtt)</option><option value="ass" data-v-a6c5b310>ASS (.ass)</option><option value="txt" data-v-a6c5b310>TXT (.txt)</option><option value="json" data-v-a6c5b310>JSON (.json)</option>',5)]),544),[[le,w.value]])]),p.value.includes("trans")?(n(),i("div",Md,[f[20]||(f[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"字幕布局 (可多选):",-1)),e("div",Pd,[(n(!0),i(Y,null,ee(v.value,S=>(n(),i("label",{key:S.value,class:"flex items-center"},[q(e("input",{type:"checkbox",value:S.value,"onUpdate:modelValue":f[5]||(f[5]=$=>y.value=$),onChange:I,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,Ad),[[ae,y.value]]),e("span",Ld,a(S.label),1)]))),128))]),f[21]||(f[21]=e("p",{class:"mt-1 text-xs text-gray-500"}," 选择多个布局将生成多个文件，例如：filename_原文在上.srt, filename_仅译文.srt ",-1))])):c("",!0),e("button",{onClick:J,disabled:!V.value||h.value||M.value,class:"w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-6 rounded-xl focus:outline-none focus:ring-4 focus:ring-purple-200 transition-all duration-200 text-lg shadow-lg hover:shadow-xl disabled:shadow-none flex items-center justify-center"},[h.value||M.value?(n(),i("svg",Rd,f[22]||(f[22]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(n(),i("svg",Fd,f[23]||(f[23]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"},null,-1)]))),ue(" "+a(h.value||M.value?"处理中...":l.value==="batch"?"开始批量处理":"开始一键处理"),1)],8,Id)])):c("",!0),P.value&&!h.value?(n(),i("div",Nd,[f[24]||(f[24]=ue(" Error: ")),e("strong",null,a(P.value),1)])):c("",!0),T.value&&!h.value&&!P.value?(n(),i("div",Ud,[f[25]||(f[25]=ue(" ✅ 一键操作成功完成！导出到: ")),e("strong",null,a(T.value),1),e("button",{onClick:U,class:"ml-2 text-sm underline"},"清除")])):c("",!0),m.value.length>0&&(h.value||M.value||P.value||T.value)?(n(),i("div",Od,[f[26]||(f[26]=e("h3",{class:"text-md font-semibold mb-2 text-gray-300"},"操作日志:",-1)),e("ul",zd,[(n(!0),i(Y,null,ee(m.value,(S,$)=>(n(),i("li",{key:$,class:H({"text-green-300":S.includes("✅")||S.includes("成功")||S.includes("完成"),"text-red-400":S.includes("❌")||S.includes("错误")||S.includes("失败"),"text-blue-300":S.includes("🚀")||S.includes("🔄")||S.includes("📝")||S.includes("🌐"),"text-yellow-300":S.includes("⚠️")||S.includes("警告"),"text-gray-300":!(S.includes("✅")||S.includes("❌")||S.includes("🚀")||S.includes("🔄")||S.includes("📝")||S.includes("🌐")||S.includes("⚠️"))})},a(S),3))),128))])])):c("",!0),!h.value&&(T.value||P.value)?(n(),i("div",Dd,[e("button",{onClick:U,class:"w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out"}," 🔄 开始新的一键操作 ")])):c("",!0)]))}},Bd=re(jd,[["__scopeId","data-v-a6c5b310"]]),Wd={class:"subtitler-view-container"},Vd={class:"flex justify-center mb-8"},Gd={class:"bg-white rounded-xl p-1 shadow-lg border border-gray-200"},qd={key:0},Hd={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},Kd={key:1,class:"mt-6"},Jd={class:"bg-white rounded-2xl shadow-xl border border-gray-100 p-6"},Xd={__name:"SubtitlerView",setup(s){const t=ce(),o=x(()=>t.currentStep),l=L("stepper"),r={1:Ds,2:tr,3:en,4:Fn,5:jl,6:zi,7:ua,8:Ya,9:td},u=x(()=>r[o.value]||null);return(p,b)=>(n(),i("div",Wd,[b[2]||(b[2]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-bold text-center bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2"},"字幕工具"),e("p",{class:"text-center text-gray-600 text-sm"},"智能视频字幕生成与编辑工具")],-1)),e("div",Vd,[e("div",Gd,[e("button",{onClick:b[0]||(b[0]=w=>l.value="stepper"),class:H(["px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none",l.value==="stepper"?"bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md":"text-gray-600 hover:text-gray-800 hover:bg-gray-50"])}," 分步操作 ",2),e("button",{onClick:b[1]||(b[1]=w=>l.value="oneclick"),class:H(["px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none",l.value==="oneclick"?"bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-md":"text-gray-600 hover:text-gray-800 hover:bg-gray-50"])}," 一键操作 ",2)])]),l.value==="stepper"?(n(),i("div",qd,[ke(Et),e("div",Hd,[(n(),Z(et,null,[(n(),Z(Ne(u.value)))],1024))])])):c("",!0),l.value==="oneclick"?(n(),i("div",Kd,[e("div",Jd,[ke(Bd)])])):c("",!0)]))}},Yd=re(Xd,[["__scopeId","data-v-fe916d6b"]]);export{Yd as default};
