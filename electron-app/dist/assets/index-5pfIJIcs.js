const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./SubtitlerView-1izcaWE_.js","./SubtitlerView-l6meq5iv.css","./GrpcTestView-DfnSBKaq.js","./GrpcTestView-BgkmbJlC.css","./AISettingsView-CsPB72Se.js","./AISettingsView-BzJ8sEmS.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ls(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const B={},gt=[],Fe=()=>{},Fi=()=>!1,vn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),cs=e=>e.startsWith("onUpdate:"),ae=Object.assign,fs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},ki=Object.prototype.hasOwnProperty,W=(e,t)=>ki.call(e,t),O=Array.isArray,mt=e=>Tt(e)==="[object Map]",St=e=>Tt(e)==="[object Set]",Ds=e=>Tt(e)==="[object Date]",Di=e=>Tt(e)==="[object RegExp]",k=e=>typeof e=="function",Q=e=>typeof e=="string",ke=e=>typeof e=="symbol",X=e=>e!==null&&typeof e=="object",_r=e=>(X(e)||k(e))&&k(e.then)&&k(e.catch),br=Object.prototype.toString,Tt=e=>br.call(e),Vi=e=>Tt(e).slice(8,-1),vr=e=>Tt(e)==="[object Object]",as=e=>Q(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,kt=ls(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),wn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ji=/-(\w)/g,Se=wn(e=>e.replace(ji,(t,n)=>n?n.toUpperCase():"")),Li=/\B([A-Z])/g,at=wn(e=>e.replace(Li,"-$1").toLowerCase()),yn=wn(e=>e.charAt(0).toUpperCase()+e.slice(1)),On=wn(e=>e?`on${yn(e)}`:""),Ye=(e,t)=>!Object.is(e,t),_t=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},wr=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ln=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Vs;const xn=()=>Vs||(Vs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function us(e){if(O(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=Q(s)?Ui(s):us(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(Q(e)||X(e))return e}const Ni=/;(?![^(]*\))/g,Hi=/:([^]+)/,$i=/\/\*[^]*?\*\//g;function Ui(e){const t={};return e.replace($i,"").split(Ni).forEach(n=>{if(n){const s=n.split(Hi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function it(e){let t="";if(Q(e))t=e;else if(O(e))for(let n=0;n<e.length;n++){const s=it(e[n]);s&&(t+=s+" ")}else if(X(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Wi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ki=ls(Wi);function yr(e){return!!e||e===""}function Bi(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=ct(e[s],t[s]);return n}function ct(e,t){if(e===t)return!0;let n=Ds(e),s=Ds(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=ke(e),s=ke(t),n||s)return e===t;if(n=O(e),s=O(t),n||s)return n&&s?Bi(e,t):!1;if(n=X(e),s=X(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,i=Object.keys(t).length;if(r!==i)return!1;for(const o in e){const l=e.hasOwnProperty(o),c=t.hasOwnProperty(o);if(l&&!c||!l&&c||!ct(e[o],t[o]))return!1}}return String(e)===String(t)}function ds(e,t){return e.findIndex(n=>ct(n,t))}const xr=e=>!!(e&&e.__v_isRef===!0),Sr=e=>Q(e)?e:e==null?"":O(e)||X(e)&&(e.toString===br||!k(e.toString))?xr(e)?Sr(e.value):JSON.stringify(e,Tr,2):String(e),Tr=(e,t)=>xr(t)?Tr(e,t.value):mt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Fn(s,i)+" =>"]=r,n),{})}:St(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Fn(n))}:ke(t)?Fn(t):X(t)&&!O(t)&&!vr(t)?String(t):t,Fn=(e,t="")=>{var n;return ke(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ce;class Cr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ce,!t&&ce&&(this.index=(ce.scopes||(ce.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ce;try{return ce=this,t()}finally{ce=n}}}on(){++this._on===1&&(this.prevScope=ce,ce=this)}off(){this._on>0&&--this._on===0&&(ce=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Er(e){return new Cr(e)}function Ar(){return ce}function qi(e,t=!1){ce&&ce.cleanups.push(e)}let Y;const kn=new WeakSet;class Mr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ce&&ce.active&&ce.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,kn.has(this)&&(kn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ir(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,js(this),Pr(this);const t=Y,n=Te;Y=this,Te=!0;try{return this.fn()}finally{Or(this),Y=t,Te=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)gs(t);this.deps=this.depsTail=void 0,js(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?kn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){qn(this)&&this.run()}get dirty(){return qn(this)}}let Rr=0,Dt,Vt;function Ir(e,t=!1){if(e.flags|=8,t){e.next=Vt,Vt=e;return}e.next=Dt,Dt=e}function hs(){Rr++}function ps(){if(--Rr>0)return;if(Vt){let t=Vt;for(Vt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Dt;){let t=Dt;for(Dt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Pr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Or(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),gs(s),Gi(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function qn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Fr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Fr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ut)||(e.globalVersion=Ut,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!qn(e))))return;e.flags|=2;const t=e.dep,n=Y,s=Te;Y=e,Te=!0;try{Pr(e);const r=e.fn(e._value);(t.version===0||Ye(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{Y=n,Te=s,Or(e),e.flags&=-3}}function gs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)gs(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Gi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Te=!0;const kr=[];function Ue(){kr.push(Te),Te=!1}function We(){const e=kr.pop();Te=e===void 0?!0:e}function js(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Y;Y=void 0;try{t()}finally{Y=n}}}let Ut=0;class zi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ms{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Y||!Te||Y===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Y)n=this.activeLink=new zi(Y,this),Y.deps?(n.prevDep=Y.depsTail,Y.depsTail.nextDep=n,Y.depsTail=n):Y.deps=Y.depsTail=n,Dr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=Y.depsTail,n.nextDep=void 0,Y.depsTail.nextDep=n,Y.depsTail=n,Y.deps===n&&(Y.deps=s)}return n}trigger(t){this.version++,Ut++,this.notify(t)}notify(t){hs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ps()}}}function Dr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Dr(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const cn=new WeakMap,ot=Symbol(""),Gn=Symbol(""),Wt=Symbol("");function fe(e,t,n){if(Te&&Y){let s=cn.get(e);s||cn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new ms),r.map=s,r.key=n),r.track()}}function Le(e,t,n,s,r,i){const o=cn.get(e);if(!o){Ut++;return}const l=c=>{c&&c.trigger()};if(hs(),t==="clear")o.forEach(l);else{const c=O(e),d=c&&as(n);if(c&&n==="length"){const u=Number(s);o.forEach((p,S)=>{(S==="length"||S===Wt||!ke(S)&&S>=u)&&l(p)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),d&&l(o.get(Wt)),t){case"add":c?d&&l(o.get("length")):(l(o.get(ot)),mt(e)&&l(o.get(Gn)));break;case"delete":c||(l(o.get(ot)),mt(e)&&l(o.get(Gn)));break;case"set":mt(e)&&l(o.get(ot));break}}ps()}function Ji(e,t){const n=cn.get(e);return n&&n.get(t)}function ut(e){const t=U(e);return t===e?t:(fe(t,"iterate",Wt),ye(e)?t:t.map(oe))}function Sn(e){return fe(e=U(e),"iterate",Wt),e}const Yi={__proto__:null,[Symbol.iterator](){return Dn(this,Symbol.iterator,oe)},concat(...e){return ut(this).concat(...e.map(t=>O(t)?ut(t):t))},entries(){return Dn(this,"entries",e=>(e[1]=oe(e[1]),e))},every(e,t){return Ve(this,"every",e,t,void 0,arguments)},filter(e,t){return Ve(this,"filter",e,t,n=>n.map(oe),arguments)},find(e,t){return Ve(this,"find",e,t,oe,arguments)},findIndex(e,t){return Ve(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ve(this,"findLast",e,t,oe,arguments)},findLastIndex(e,t){return Ve(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ve(this,"forEach",e,t,void 0,arguments)},includes(...e){return Vn(this,"includes",e)},indexOf(...e){return Vn(this,"indexOf",e)},join(e){return ut(this).join(e)},lastIndexOf(...e){return Vn(this,"lastIndexOf",e)},map(e,t){return Ve(this,"map",e,t,void 0,arguments)},pop(){return Rt(this,"pop")},push(...e){return Rt(this,"push",e)},reduce(e,...t){return Ls(this,"reduce",e,t)},reduceRight(e,...t){return Ls(this,"reduceRight",e,t)},shift(){return Rt(this,"shift")},some(e,t){return Ve(this,"some",e,t,void 0,arguments)},splice(...e){return Rt(this,"splice",e)},toReversed(){return ut(this).toReversed()},toSorted(e){return ut(this).toSorted(e)},toSpliced(...e){return ut(this).toSpliced(...e)},unshift(...e){return Rt(this,"unshift",e)},values(){return Dn(this,"values",oe)}};function Dn(e,t,n){const s=Sn(e),r=s[t]();return s!==e&&!ye(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const Xi=Array.prototype;function Ve(e,t,n,s,r,i){const o=Sn(e),l=o!==e&&!ye(e),c=o[t];if(c!==Xi[t]){const p=c.apply(e,i);return l?oe(p):p}let d=n;o!==e&&(l?d=function(p,S){return n.call(this,oe(p),S,e)}:n.length>2&&(d=function(p,S){return n.call(this,p,S,e)}));const u=c.call(o,d,s);return l&&r?r(u):u}function Ls(e,t,n,s){const r=Sn(e);let i=n;return r!==e&&(ye(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,oe(l),c,e)}),r[t](i,...s)}function Vn(e,t,n){const s=U(e);fe(s,"iterate",Wt);const r=s[t](...n);return(r===-1||r===!1)&&vs(n[0])?(n[0]=U(n[0]),s[t](...n)):r}function Rt(e,t,n=[]){Ue(),hs();const s=U(e)[t].apply(e,n);return ps(),We(),s}const Zi=ls("__proto__,__v_isRef,__isVue"),Vr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ke));function Qi(e){ke(e)||(e=String(e));const t=U(this);return fe(t,"has",e),t.hasOwnProperty(e)}class jr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?fo:$r:i?Hr:Nr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=O(t);if(!r){let c;if(o&&(c=Yi[n]))return c;if(n==="hasOwnProperty")return Qi}const l=Reflect.get(t,n,Z(t)?t:s);return(ke(n)?Vr.has(n):Zi(n))||(r||fe(t,"get",n),i)?l:Z(l)?o&&as(n)?l:l.value:X(l)?r?Ur(l):Tn(l):l}}class Lr extends jr{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=Ze(i);if(!ye(s)&&!Ze(s)&&(i=U(i),s=U(s)),!O(t)&&Z(i)&&!Z(s))return c?!1:(i.value=s,!0)}const o=O(t)&&as(n)?Number(n)<t.length:W(t,n),l=Reflect.set(t,n,s,Z(t)?t:r);return t===U(r)&&(o?Ye(s,i)&&Le(t,"set",n,s):Le(t,"add",n,s)),l}deleteProperty(t,n){const s=W(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Le(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!ke(n)||!Vr.has(n))&&fe(t,"has",n),s}ownKeys(t){return fe(t,"iterate",O(t)?"length":ot),Reflect.ownKeys(t)}}class eo extends jr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const to=new Lr,no=new eo,so=new Lr(!0);const zn=e=>e,Zt=e=>Reflect.getPrototypeOf(e);function ro(e,t,n){return function(...s){const r=this.__v_raw,i=U(r),o=mt(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,d=r[e](...s),u=n?zn:t?fn:oe;return!t&&fe(i,"iterate",c?Gn:ot),{next(){const{value:p,done:S}=d.next();return S?{value:p,done:S}:{value:l?[u(p[0]),u(p[1])]:u(p),done:S}},[Symbol.iterator](){return this}}}}function Qt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function io(e,t){const n={get(r){const i=this.__v_raw,o=U(i),l=U(r);e||(Ye(r,l)&&fe(o,"get",r),fe(o,"get",l));const{has:c}=Zt(o),d=t?zn:e?fn:oe;if(c.call(o,r))return d(i.get(r));if(c.call(o,l))return d(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&fe(U(r),"iterate",ot),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=U(i),l=U(r);return e||(Ye(r,l)&&fe(o,"has",r),fe(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,c=U(l),d=t?zn:e?fn:oe;return!e&&fe(c,"iterate",ot),l.forEach((u,p)=>r.call(i,d(u),d(p),o))}};return ae(n,e?{add:Qt("add"),set:Qt("set"),delete:Qt("delete"),clear:Qt("clear")}:{add(r){!t&&!ye(r)&&!Ze(r)&&(r=U(r));const i=U(this);return Zt(i).has.call(i,r)||(i.add(r),Le(i,"add",r,r)),this},set(r,i){!t&&!ye(i)&&!Ze(i)&&(i=U(i));const o=U(this),{has:l,get:c}=Zt(o);let d=l.call(o,r);d||(r=U(r),d=l.call(o,r));const u=c.call(o,r);return o.set(r,i),d?Ye(i,u)&&Le(o,"set",r,i):Le(o,"add",r,i),this},delete(r){const i=U(this),{has:o,get:l}=Zt(i);let c=o.call(i,r);c||(r=U(r),c=o.call(i,r)),l&&l.call(i,r);const d=i.delete(r);return c&&Le(i,"delete",r,void 0),d},clear(){const r=U(this),i=r.size!==0,o=r.clear();return i&&Le(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=ro(r,e,t)}),n}function _s(e,t){const n=io(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(W(n,r)&&r in s?n:s,r,i)}const oo={get:_s(!1,!1)},lo={get:_s(!1,!0)},co={get:_s(!0,!1)};const Nr=new WeakMap,Hr=new WeakMap,$r=new WeakMap,fo=new WeakMap;function ao(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function uo(e){return e.__v_skip||!Object.isExtensible(e)?0:ao(Vi(e))}function Tn(e){return Ze(e)?e:bs(e,!1,to,oo,Nr)}function ho(e){return bs(e,!1,so,lo,Hr)}function Ur(e){return bs(e,!0,no,co,$r)}function bs(e,t,n,s,r){if(!X(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=uo(e);if(i===0)return e;const o=r.get(e);if(o)return o;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function $e(e){return Ze(e)?$e(e.__v_raw):!!(e&&e.__v_isReactive)}function Ze(e){return!!(e&&e.__v_isReadonly)}function ye(e){return!!(e&&e.__v_isShallow)}function vs(e){return e?!!e.__v_raw:!1}function U(e){const t=e&&e.__v_raw;return t?U(t):e}function ws(e){return!W(e,"__v_skip")&&Object.isExtensible(e)&&wr(e,"__v_skip",!0),e}const oe=e=>X(e)?Tn(e):e,fn=e=>X(e)?Ur(e):e;function Z(e){return e?e.__v_isRef===!0:!1}function bt(e){return Wr(e,!1)}function po(e){return Wr(e,!0)}function Wr(e,t){return Z(e)?e:new go(e,t)}class go{constructor(t,n){this.dep=new ms,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:U(t),this._value=n?t:oe(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||ye(t)||Ze(t);t=s?t:U(t),Ye(t,n)&&(this._rawValue=t,this._value=s?t:oe(t),this.dep.trigger())}}function mo(e){return Z(e)?e.value:e}const _o={get:(e,t,n)=>t==="__v_raw"?e:mo(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Z(r)&&!Z(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Kr(e){return $e(e)?e:new Proxy(e,_o)}function bo(e){const t=O(e)?new Array(e.length):{};for(const n in e)t[n]=Br(e,n);return t}class vo{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ji(U(this._object),this._key)}}class wo{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function yo(e,t,n){return Z(e)?e:k(e)?new wo(e):X(e)&&arguments.length>1?Br(e,t,n):bt(e)}function Br(e,t,n){const s=e[t];return Z(s)?s:new vo(e,t,n)}class xo{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ms(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ut-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&Y!==this)return Ir(this,!0),!0}get value(){const t=this.dep.track();return Fr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function So(e,t,n=!1){let s,r;return k(e)?s=e:(s=e.get,r=e.set),new xo(s,r,n)}const en={},an=new WeakMap;let rt;function To(e,t=!1,n=rt){if(n){let s=an.get(n);s||an.set(n,s=[]),s.push(e)}}function Co(e,t,n=B){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:c}=n,d=v=>r?v:ye(v)||r===!1||r===0?Ne(v,1):Ne(v);let u,p,S,w,R=!1,A=!1;if(Z(e)?(p=()=>e.value,R=ye(e)):$e(e)?(p=()=>d(e),R=!0):O(e)?(A=!0,R=e.some(v=>$e(v)||ye(v)),p=()=>e.map(v=>{if(Z(v))return v.value;if($e(v))return d(v);if(k(v))return c?c(v,2):v()})):k(e)?t?p=c?()=>c(e,2):e:p=()=>{if(S){Ue();try{S()}finally{We()}}const v=rt;rt=u;try{return c?c(e,3,[w]):e(w)}finally{rt=v}}:p=Fe,t&&r){const v=p,V=r===!0?1/0:r;p=()=>Ne(v(),V)}const H=Ar(),D=()=>{u.stop(),H&&H.active&&fs(H.effects,u)};if(i&&t){const v=t;t=(...V)=>{v(...V),D()}}let M=A?new Array(e.length).fill(en):en;const T=v=>{if(!(!(u.flags&1)||!u.dirty&&!v))if(t){const V=u.run();if(r||R||(A?V.some((ne,q)=>Ye(ne,M[q])):Ye(V,M))){S&&S();const ne=rt;rt=u;try{const q=[V,M===en?void 0:A&&M[0]===en?[]:M,w];M=V,c?c(t,3,q):t(...q)}finally{rt=ne}}}else u.run()};return l&&l(T),u=new Mr(p),u.scheduler=o?()=>o(T,!1):T,w=v=>To(v,!1,u),S=u.onStop=()=>{const v=an.get(u);if(v){if(c)c(v,4);else for(const V of v)V();an.delete(u)}},t?s?T(!0):M=u.run():o?o(T.bind(null,!0),!0):u.run(),D.pause=u.pause.bind(u),D.resume=u.resume.bind(u),D.stop=D,D}function Ne(e,t=1/0,n){if(t<=0||!X(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Z(e))Ne(e.value,t,n);else if(O(e))for(let s=0;s<e.length;s++)Ne(e[s],t,n);else if(St(e)||mt(e))e.forEach(s=>{Ne(s,t,n)});else if(vr(e)){for(const s in e)Ne(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Ne(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function qt(e,t,n,s){try{return s?e(...s):e()}catch(r){Gt(r,t,n)}}function De(e,t,n,s){if(k(e)){const r=qt(e,t,n,s);return r&&_r(r)&&r.catch(i=>{Gt(i,t,n)}),r}if(O(e)){const r=[];for(let i=0;i<e.length;i++)r.push(De(e[i],t,n,s));return r}}function Gt(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||B;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let p=0;p<u.length;p++)if(u[p](e,c,d)===!1)return}l=l.parent}if(i){Ue(),qt(i,null,10,[e,c,d]),We();return}}Eo(e,n,r,s,o)}function Eo(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const de=[];let Ie=-1;const vt=[];let ze=null,ht=0;const qr=Promise.resolve();let un=null;function ys(e){const t=un||qr;return e?t.then(this?e.bind(this):e):t}function Ao(e){let t=Ie+1,n=de.length;for(;t<n;){const s=t+n>>>1,r=de[s],i=Kt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function xs(e){if(!(e.flags&1)){const t=Kt(e),n=de[de.length-1];!n||!(e.flags&2)&&t>=Kt(n)?de.push(e):de.splice(Ao(t),0,e),e.flags|=1,Gr()}}function Gr(){un||(un=qr.then(Jr))}function Mo(e){O(e)?vt.push(...e):ze&&e.id===-1?ze.splice(ht+1,0,e):e.flags&1||(vt.push(e),e.flags|=1),Gr()}function Ns(e,t,n=Ie+1){for(;n<de.length;n++){const s=de[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;de.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function zr(e){if(vt.length){const t=[...new Set(vt)].sort((n,s)=>Kt(n)-Kt(s));if(vt.length=0,ze){ze.push(...t);return}for(ze=t,ht=0;ht<ze.length;ht++){const n=ze[ht];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ze=null,ht=0}}const Kt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Jr(e){try{for(Ie=0;Ie<de.length;Ie++){const t=de[Ie];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),qt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ie<de.length;Ie++){const t=de[Ie];t&&(t.flags&=-2)}Ie=-1,de.length=0,zr(),un=null,(de.length||vt.length)&&Jr()}}let he=null,Yr=null;function dn(e){const t=he;return he=e,Yr=e&&e.type.__scopeId||null,t}function Ro(e,t=he,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Js(-1);const i=dn(t);let o;try{o=e(...r)}finally{dn(i),s._d&&Js(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function Xc(e,t){if(he===null)return e;const n=Rn(he),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,o,l,c=B]=t[r];i&&(k(i)&&(i={mounted:i,updated:i}),i.deep&&Ne(o),s.push({dir:i,instance:n,value:o,oldValue:void 0,arg:l,modifiers:c}))}return e}function nt(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let c=l.dir[s];c&&(Ue(),De(c,n,8,[e.el,l,e,t]),We())}}const Io=Symbol("_vte"),Po=e=>e.__isTeleport;function Cn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Cn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Oo(e,t){return k(e)?ae({name:e.name},t,{setup:e}):e}function Ss(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function hn(e,t,n,s,r=!1){if(O(e)){e.forEach((R,A)=>hn(R,t&&(O(t)?t[A]:t),n,s,r));return}if(wt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&hn(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?Rn(s.component):s.el,o=r?null:i,{i:l,r:c}=e,d=t&&t.r,u=l.refs===B?l.refs={}:l.refs,p=l.setupState,S=U(p),w=p===B?()=>!1:R=>W(S,R);if(d!=null&&d!==c&&(Q(d)?(u[d]=null,w(d)&&(p[d]=null)):Z(d)&&(d.value=null)),k(c))qt(c,l,12,[o,u]);else{const R=Q(c),A=Z(c);if(R||A){const H=()=>{if(e.f){const D=R?w(c)?p[c]:u[c]:c.value;r?O(D)&&fs(D,i):O(D)?D.includes(i)||D.push(i):R?(u[c]=[i],w(c)&&(p[c]=u[c])):(c.value=[i],e.k&&(u[e.k]=c.value))}else R?(u[c]=o,w(c)&&(p[c]=o)):A&&(c.value=o,e.k&&(u[e.k]=o))};o?(H.id=-1,ie(H,n)):H()}}}const Hs=e=>e.nodeType===8;xn().requestIdleCallback;xn().cancelIdleCallback;function Fo(e,t){if(Hs(e)&&e.data==="["){let n=1,s=e.nextSibling;for(;s;){if(s.nodeType===1){if(t(s)===!1)break}else if(Hs(s))if(s.data==="]"){if(--n===0)break}else s.data==="["&&n++;s=s.nextSibling}}else t(e)}const wt=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function jn(e){k(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:s,delay:r=200,hydrate:i,timeout:o,suspensible:l=!0,onError:c}=e;let d=null,u,p=0;const S=()=>(p++,d=null,w()),w=()=>{let R;return d||(R=d=t().catch(A=>{if(A=A instanceof Error?A:new Error(String(A)),c)return new Promise((H,D)=>{c(A,()=>H(S()),()=>D(A),p+1)});throw A}).then(A=>R!==d&&d?d:(A&&(A.__esModule||A[Symbol.toStringTag]==="Module")&&(A=A.default),u=A,A)))};return Oo({name:"AsyncComponentWrapper",__asyncLoader:w,__asyncHydrate(R,A,H){const D=i?()=>{const T=i(()=>{H()},v=>Fo(R,v));T&&(A.bum||(A.bum=[])).push(T),(A.u||(A.u=[])).push(()=>!0)}:H;u?D():w().then(()=>!A.isUnmounted&&D())},get __asyncResolved(){return u},setup(){const R=se;if(Ss(R),u)return()=>Ln(u,R);const A=T=>{d=null,Gt(T,R,13,!s)};if(l&&R.suspense||yt)return w().then(T=>()=>Ln(T,R)).catch(T=>(A(T),()=>s?pe(s,{error:T}):null));const H=bt(!1),D=bt(),M=bt(!!r);return r&&setTimeout(()=>{M.value=!1},r),o!=null&&setTimeout(()=>{if(!H.value&&!D.value){const T=new Error(`Async component timed out after ${o}ms.`);A(T),D.value=T}},o),w().then(()=>{H.value=!0,R.parent&&Ts(R.parent.vnode)&&R.parent.update()}).catch(T=>{A(T),D.value=T}),()=>{if(H.value&&u)return Ln(u,R);if(D.value&&s)return pe(s,{error:D.value});if(n&&!M.value)return pe(n)}}})}function Ln(e,t){const{ref:n,props:s,children:r,ce:i}=t.vnode,o=pe(e,s,r);return o.ref=n,o.ce=i,delete t.vnode.ce,o}const Ts=e=>e.type.__isKeepAlive,ko={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=Fl(),s=n.ctx;if(!s.renderer)return()=>{const M=t.default&&t.default();return M&&M.length===1?M[0]:M};const r=new Map,i=new Set;let o=null;const l=n.suspense,{renderer:{p:c,m:d,um:u,o:{createElement:p}}}=s,S=p("div");s.activate=(M,T,v,V,ne)=>{const q=M.component;d(M,T,v,0,l),c(q.vnode,M,T,v,q,l,V,M.slotScopeIds,ne),ie(()=>{q.isDeactivated=!1,q.a&&_t(q.a);const j=M.props&&M.props.onVnodeMounted;j&&we(j,q.parent,M)},l)},s.deactivate=M=>{const T=M.component;gn(T.m),gn(T.a),d(M,S,null,1,l),ie(()=>{T.da&&_t(T.da);const v=M.props&&M.props.onVnodeUnmounted;v&&we(v,T.parent,M),T.isDeactivated=!0},l)};function w(M){Nn(M),u(M,n,l,!0)}function R(M){r.forEach((T,v)=>{const V=ts(T.type);V&&!M(V)&&A(v)})}function A(M){const T=r.get(M);T&&(!o||!pt(T,o))?w(T):o&&Nn(o),r.delete(M),i.delete(M)}Nt(()=>[e.include,e.exclude],([M,T])=>{M&&R(v=>Ot(M,v)),T&&R(v=>!Ot(T,v))},{flush:"post",deep:!0});let H=null;const D=()=>{H!=null&&(mn(n.subTree.type)?ie(()=>{r.set(H,tn(n.subTree))},n.subTree.suspense):r.set(H,tn(n.subTree)))};return Cs(D),Zr(D),Qr(()=>{r.forEach(M=>{const{subTree:T,suspense:v}=n,V=tn(T);if(M.type===V.type&&M.key===V.key){Nn(V);const ne=V.component.da;ne&&ie(ne,v);return}w(M)})}),()=>{if(H=null,!t.default)return o=null;const M=t.default(),T=M[0];if(M.length>1)return o=null,M;if(!Rs(T)||!(T.shapeFlag&4)&&!(T.shapeFlag&128))return o=null,T;let v=tn(T);if(v.type===Ke)return o=null,v;const V=v.type,ne=ts(wt(v)?v.type.__asyncResolved||{}:V),{include:q,exclude:j,max:N}=e;if(q&&(!ne||!Ot(q,ne))||j&&ne&&Ot(j,ne))return v.shapeFlag&=-257,o=v,T;const G=v.key==null?V:v.key,le=r.get(G);return v.el&&(v=ft(v),T.shapeFlag&128&&(T.ssContent=v)),H=G,le?(v.el=le.el,v.component=le.component,v.transition&&Cn(v,v.transition),v.shapeFlag|=512,i.delete(G),i.add(G)):(i.add(G),N&&i.size>parseInt(N,10)&&A(i.values().next().value)),v.shapeFlag|=256,o=v,mn(T.type)?T:v}}},Do=ko;function Ot(e,t){return O(e)?e.some(n=>Ot(n,t)):Q(e)?e.split(",").includes(t):Di(e)?(e.lastIndex=0,e.test(t)):!1}function Vo(e,t){Xr(e,"a",t)}function jo(e,t){Xr(e,"da",t)}function Xr(e,t,n=se){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(En(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Ts(r.parent.vnode)&&Lo(s,t,n,r),r=r.parent}}function Lo(e,t,n,s){const r=En(t,e,s,!0);Es(()=>{fs(s[t],r)},n)}function Nn(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function tn(e){return e.shapeFlag&128?e.ssContent:e}function En(e,t,n=se,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Ue();const l=zt(n),c=De(t,n,e,o);return l(),We(),c});return s?r.unshift(i):r.push(i),i}}const Be=e=>(t,n=se)=>{(!yt||e==="sp")&&En(e,(...s)=>t(...s),n)},No=Be("bm"),Cs=Be("m"),Ho=Be("bu"),Zr=Be("u"),Qr=Be("bum"),Es=Be("um"),$o=Be("sp"),Uo=Be("rtg"),Wo=Be("rtc");function Ko(e,t=se){En("ec",e,t)}const Bo="components",ei=Symbol.for("v-ndc");function qo(e){return Q(e)?Go(Bo,e,!1)||e:e||ei}function Go(e,t,n=!0,s=!1){const r=he||se;if(r){const i=r.type;{const l=ts(i,!1);if(l&&(l===t||l===Se(t)||l===yn(Se(t))))return i}const o=$s(r[e]||i[e],t)||$s(r.appContext[e],t);return!o&&s?i:o}}function $s(e,t){return e&&(e[t]||e[Se(t)]||e[yn(Se(t))])}function zo(e,t,n,s){let r;const i=n,o=O(e);if(o||Q(e)){const l=o&&$e(e);let c=!1,d=!1;l&&(c=!ye(e),d=Ze(e),e=Sn(e)),r=new Array(e.length);for(let u=0,p=e.length;u<p;u++)r[u]=t(c?d?fn(oe(e[u])):oe(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(X(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,d=l.length;c<d;c++){const u=l[c];r[c]=t(e[u],u,c,i)}}else r=[];return r}const Jn=e=>e?vi(e)?Rn(e):Jn(e.parent):null,jt=ae(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Jn(e.parent),$root:e=>Jn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ni(e),$forceUpdate:e=>e.f||(e.f=()=>{xs(e.update)}),$nextTick:e=>e.n||(e.n=ys.bind(e.proxy)),$watch:e=>_l.bind(e)}),Hn=(e,t)=>e!==B&&!e.__isScriptSetup&&W(e,t),Jo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const w=o[t];if(w!==void 0)switch(w){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Hn(s,t))return o[t]=1,s[t];if(r!==B&&W(r,t))return o[t]=2,r[t];if((d=e.propsOptions[0])&&W(d,t))return o[t]=3,i[t];if(n!==B&&W(n,t))return o[t]=4,n[t];Yn&&(o[t]=0)}}const u=jt[t];let p,S;if(u)return t==="$attrs"&&fe(e.attrs,"get",""),u(e);if((p=l.__cssModules)&&(p=p[t]))return p;if(n!==B&&W(n,t))return o[t]=4,n[t];if(S=c.config.globalProperties,W(S,t))return S[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Hn(r,t)?(r[t]=n,!0):s!==B&&W(s,t)?(s[t]=n,!0):W(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==B&&W(e,o)||Hn(t,o)||(l=i[0])&&W(l,o)||W(s,o)||W(jt,o)||W(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:W(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Us(e){return O(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Yn=!0;function Yo(e){const t=ni(e),n=e.proxy,s=e.ctx;Yn=!1,t.beforeCreate&&Ws(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:c,inject:d,created:u,beforeMount:p,mounted:S,beforeUpdate:w,updated:R,activated:A,deactivated:H,beforeDestroy:D,beforeUnmount:M,destroyed:T,unmounted:v,render:V,renderTracked:ne,renderTriggered:q,errorCaptured:j,serverPrefetch:N,expose:G,inheritAttrs:le,components:Ce,directives:qe,filters:Ct}=t;if(d&&Xo(d,s,null),o)for(const L in o){const z=o[L];k(z)&&(s[L]=z.bind(n))}if(r){const L=r.call(n,n);X(L)&&(e.data=Tn(L))}if(Yn=!0,i)for(const L in i){const z=i[L],et=k(z)?z.bind(n,n):k(z.get)?z.get.bind(n,n):Fe,Yt=!k(z)&&k(z.set)?z.set.bind(n):Fe,tt=Xe({get:et,set:Yt});Object.defineProperty(s,L,{enumerable:!0,configurable:!0,get:()=>tt.value,set:Ee=>tt.value=Ee})}if(l)for(const L in l)ti(l[L],s,n,L);if(c){const L=k(c)?c.call(n):c;Reflect.ownKeys(L).forEach(z=>{sl(z,L[z])})}u&&Ws(u,e,"c");function ee(L,z){O(z)?z.forEach(et=>L(et.bind(n))):z&&L(z.bind(n))}if(ee(No,p),ee(Cs,S),ee(Ho,w),ee(Zr,R),ee(Vo,A),ee(jo,H),ee(Ko,j),ee(Wo,ne),ee(Uo,q),ee(Qr,M),ee(Es,v),ee($o,N),O(G))if(G.length){const L=e.exposed||(e.exposed={});G.forEach(z=>{Object.defineProperty(L,z,{get:()=>n[z],set:et=>n[z]=et})})}else e.exposed||(e.exposed={});V&&e.render===Fe&&(e.render=V),le!=null&&(e.inheritAttrs=le),Ce&&(e.components=Ce),qe&&(e.directives=qe),N&&Ss(e)}function Xo(e,t,n=Fe){O(e)&&(e=Xn(e));for(const s in e){const r=e[s];let i;X(r)?"default"in r?i=Lt(r.from||s,r.default,!0):i=Lt(r.from||s):i=Lt(r),Z(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function Ws(e,t,n){De(O(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function ti(e,t,n,s){let r=s.includes(".")?gi(n,s):()=>n[s];if(Q(e)){const i=t[e];k(i)&&Nt(r,i)}else if(k(e))Nt(r,e.bind(n));else if(X(e))if(O(e))e.forEach(i=>ti(i,t,n,s));else{const i=k(e.handler)?e.handler.bind(n):t[e.handler];k(i)&&Nt(r,i,e)}}function ni(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(d=>pn(c,d,o,!0)),pn(c,t,o)),X(t)&&i.set(t,c),c}function pn(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&pn(e,i,n,!0),r&&r.forEach(o=>pn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=Zo[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const Zo={data:Ks,props:Bs,emits:Bs,methods:Ft,computed:Ft,beforeCreate:ue,created:ue,beforeMount:ue,mounted:ue,beforeUpdate:ue,updated:ue,beforeDestroy:ue,beforeUnmount:ue,destroyed:ue,unmounted:ue,activated:ue,deactivated:ue,errorCaptured:ue,serverPrefetch:ue,components:Ft,directives:Ft,watch:el,provide:Ks,inject:Qo};function Ks(e,t){return t?e?function(){return ae(k(e)?e.call(this,this):e,k(t)?t.call(this,this):t)}:t:e}function Qo(e,t){return Ft(Xn(e),Xn(t))}function Xn(e){if(O(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ue(e,t){return e?[...new Set([].concat(e,t))]:t}function Ft(e,t){return e?ae(Object.create(null),e,t):t}function Bs(e,t){return e?O(e)&&O(t)?[...new Set([...e,...t])]:ae(Object.create(null),Us(e),Us(t??{})):t}function el(e,t){if(!e)return t;if(!t)return e;const n=ae(Object.create(null),e);for(const s in t)n[s]=ue(e[s],t[s]);return n}function si(){return{app:null,config:{isNativeTag:Fi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let tl=0;function nl(e,t){return function(s,r=null){k(s)||(s=ae({},s)),r!=null&&!X(r)&&(r=null);const i=si(),o=new WeakSet,l=[];let c=!1;const d=i.app={_uid:tl++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:Nl,get config(){return i.config},set config(u){},use(u,...p){return o.has(u)||(u&&k(u.install)?(o.add(u),u.install(d,...p)):k(u)&&(o.add(u),u(d,...p))),d},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),d},component(u,p){return p?(i.components[u]=p,d):i.components[u]},directive(u,p){return p?(i.directives[u]=p,d):i.directives[u]},mount(u,p,S){if(!c){const w=d._ceVNode||pe(s,r);return w.appContext=i,S===!0?S="svg":S===!1&&(S=void 0),e(w,u,S),c=!0,d._container=u,u.__vue_app__=d,Rn(w.component)}},onUnmount(u){l.push(u)},unmount(){c&&(De(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(u,p){return i.provides[u]=p,d},runWithContext(u){const p=lt;lt=d;try{return u()}finally{lt=p}}};return d}}let lt=null;function sl(e,t){if(se){let n=se.provides;const s=se.parent&&se.parent.provides;s===n&&(n=se.provides=Object.create(s)),n[e]=t}}function Lt(e,t,n=!1){const s=se||he;if(s||lt){let r=lt?lt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&k(t)?t.call(s&&s.proxy):t}}function rl(){return!!(se||he||lt)}const ri={},ii=()=>Object.create(ri),oi=e=>Object.getPrototypeOf(e)===ri;function il(e,t,n,s=!1){const r={},i=ii();e.propsDefaults=Object.create(null),li(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:ho(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function ol(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=U(r),[c]=e.propsOptions;let d=!1;if((s||o>0)&&!(o&16)){if(o&8){const u=e.vnode.dynamicProps;for(let p=0;p<u.length;p++){let S=u[p];if(An(e.emitsOptions,S))continue;const w=t[S];if(c)if(W(i,S))w!==i[S]&&(i[S]=w,d=!0);else{const R=Se(S);r[R]=Zn(c,l,R,w,e,!1)}else w!==i[S]&&(i[S]=w,d=!0)}}}else{li(e,t,r,i)&&(d=!0);let u;for(const p in l)(!t||!W(t,p)&&((u=at(p))===p||!W(t,u)))&&(c?n&&(n[p]!==void 0||n[u]!==void 0)&&(r[p]=Zn(c,l,p,void 0,e,!0)):delete r[p]);if(i!==l)for(const p in i)(!t||!W(t,p))&&(delete i[p],d=!0)}d&&Le(e.attrs,"set","")}function li(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(kt(c))continue;const d=t[c];let u;r&&W(r,u=Se(c))?!i||!i.includes(u)?n[u]=d:(l||(l={}))[u]=d:An(e.emitsOptions,c)||(!(c in s)||d!==s[c])&&(s[c]=d,o=!0)}if(i){const c=U(n),d=l||B;for(let u=0;u<i.length;u++){const p=i[u];n[p]=Zn(r,c,p,d[p],e,!W(d,p))}}return o}function Zn(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=W(o,"default");if(l&&s===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&k(c)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const u=zt(r);s=d[n]=c.call(null,t),u()}}else s=c;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===at(n))&&(s=!0))}return s}const ll=new WeakMap;function ci(e,t,n=!1){const s=n?ll:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let c=!1;if(!k(e)){const u=p=>{c=!0;const[S,w]=ci(p,t,!0);ae(o,S),w&&l.push(...w)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!c)return X(e)&&s.set(e,gt),gt;if(O(i))for(let u=0;u<i.length;u++){const p=Se(i[u]);qs(p)&&(o[p]=B)}else if(i)for(const u in i){const p=Se(u);if(qs(p)){const S=i[u],w=o[p]=O(S)||k(S)?{type:S}:ae({},S),R=w.type;let A=!1,H=!0;if(O(R))for(let D=0;D<R.length;++D){const M=R[D],T=k(M)&&M.name;if(T==="Boolean"){A=!0;break}else T==="String"&&(H=!1)}else A=k(R)&&R.name==="Boolean";w[0]=A,w[1]=H,(A||W(w,"default"))&&l.push(p)}}const d=[o,l];return X(e)&&s.set(e,d),d}function qs(e){return e[0]!=="$"&&!kt(e)}const As=e=>e[0]==="_"||e==="$stable",Ms=e=>O(e)?e.map(Oe):[Oe(e)],cl=(e,t,n)=>{if(t._n)return t;const s=Ro((...r)=>Ms(t(...r)),n);return s._c=!1,s},fi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(As(r))continue;const i=e[r];if(k(i))t[r]=cl(r,i,s);else if(i!=null){const o=Ms(i);t[r]=()=>o}}},ai=(e,t)=>{const n=Ms(t);e.slots.default=()=>n},ui=(e,t,n)=>{for(const s in t)(n||!As(s))&&(e[s]=t[s])},fl=(e,t,n)=>{const s=e.slots=ii();if(e.vnode.shapeFlag&32){const r=t._;r?(ui(s,t,n),n&&wr(s,"_",r,!0)):fi(t,s)}else t&&ai(e,t)},al=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=B;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:ui(r,t,n):(i=!t.$stable,fi(t,r)),o=t}else t&&(ai(e,t),o={default:1});if(i)for(const l in r)!As(l)&&o[l]==null&&delete r[l]},ie=Tl;function ul(e){return dl(e)}function dl(e,t){const n=xn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:c,setText:d,setElementText:u,parentNode:p,nextSibling:S,setScopeId:w=Fe,insertStaticContent:R}=e,A=(f,a,h,_=null,g=null,m=null,C=void 0,x=null,y=!!a.dynamicChildren)=>{if(f===a)return;f&&!pt(f,a)&&(_=Xt(f),Ee(f,g,m,!0),f=null),a.patchFlag===-2&&(y=!1,a.dynamicChildren=null);const{type:b,ref:P,shapeFlag:E}=a;switch(b){case Mn:H(f,a,h,_);break;case Ke:D(f,a,h,_);break;case sn:f==null&&M(a,h,_,C);break;case Pe:Ce(f,a,h,_,g,m,C,x,y);break;default:E&1?V(f,a,h,_,g,m,C,x,y):E&6?qe(f,a,h,_,g,m,C,x,y):(E&64||E&128)&&b.process(f,a,h,_,g,m,C,x,y,At)}P!=null&&g&&hn(P,f&&f.ref,m,a||f,!a)},H=(f,a,h,_)=>{if(f==null)s(a.el=l(a.children),h,_);else{const g=a.el=f.el;a.children!==f.children&&d(g,a.children)}},D=(f,a,h,_)=>{f==null?s(a.el=c(a.children||""),h,_):a.el=f.el},M=(f,a,h,_)=>{[f.el,f.anchor]=R(f.children,a,h,_,f.el,f.anchor)},T=({el:f,anchor:a},h,_)=>{let g;for(;f&&f!==a;)g=S(f),s(f,h,_),f=g;s(a,h,_)},v=({el:f,anchor:a})=>{let h;for(;f&&f!==a;)h=S(f),r(f),f=h;r(a)},V=(f,a,h,_,g,m,C,x,y)=>{a.type==="svg"?C="svg":a.type==="math"&&(C="mathml"),f==null?ne(a,h,_,g,m,C,x,y):N(f,a,g,m,C,x,y)},ne=(f,a,h,_,g,m,C,x)=>{let y,b;const{props:P,shapeFlag:E,transition:I,dirs:F}=f;if(y=f.el=o(f.type,m,P&&P.is,P),E&8?u(y,f.children):E&16&&j(f.children,y,null,_,g,$n(f,m),C,x),F&&nt(f,null,_,"created"),q(y,f,f.scopeId,C,_),P){for(const J in P)J!=="value"&&!kt(J)&&i(y,J,null,P[J],m,_);"value"in P&&i(y,"value",null,P.value,m),(b=P.onVnodeBeforeMount)&&we(b,_,f)}F&&nt(f,null,_,"beforeMount");const $=hl(g,I);$&&I.beforeEnter(y),s(y,a,h),((b=P&&P.onVnodeMounted)||$||F)&&ie(()=>{b&&we(b,_,f),$&&I.enter(y),F&&nt(f,null,_,"mounted")},g)},q=(f,a,h,_,g)=>{if(h&&w(f,h),_)for(let m=0;m<_.length;m++)w(f,_[m]);if(g){let m=g.subTree;if(a===m||mn(m.type)&&(m.ssContent===a||m.ssFallback===a)){const C=g.vnode;q(f,C,C.scopeId,C.slotScopeIds,g.parent)}}},j=(f,a,h,_,g,m,C,x,y=0)=>{for(let b=y;b<f.length;b++){const P=f[b]=x?Je(f[b]):Oe(f[b]);A(null,P,a,h,_,g,m,C,x)}},N=(f,a,h,_,g,m,C)=>{const x=a.el=f.el;let{patchFlag:y,dynamicChildren:b,dirs:P}=a;y|=f.patchFlag&16;const E=f.props||B,I=a.props||B;let F;if(h&&st(h,!1),(F=I.onVnodeBeforeUpdate)&&we(F,h,a,f),P&&nt(a,f,h,"beforeUpdate"),h&&st(h,!0),(E.innerHTML&&I.innerHTML==null||E.textContent&&I.textContent==null)&&u(x,""),b?G(f.dynamicChildren,b,x,h,_,$n(a,g),m):C||z(f,a,x,null,h,_,$n(a,g),m,!1),y>0){if(y&16)le(x,E,I,h,g);else if(y&2&&E.class!==I.class&&i(x,"class",null,I.class,g),y&4&&i(x,"style",E.style,I.style,g),y&8){const $=a.dynamicProps;for(let J=0;J<$.length;J++){const K=$[J],_e=E[K],ge=I[K];(ge!==_e||K==="value")&&i(x,K,_e,ge,g,h)}}y&1&&f.children!==a.children&&u(x,a.children)}else!C&&b==null&&le(x,E,I,h,g);((F=I.onVnodeUpdated)||P)&&ie(()=>{F&&we(F,h,a,f),P&&nt(a,f,h,"updated")},_)},G=(f,a,h,_,g,m,C)=>{for(let x=0;x<a.length;x++){const y=f[x],b=a[x],P=y.el&&(y.type===Pe||!pt(y,b)||y.shapeFlag&198)?p(y.el):h;A(y,b,P,null,_,g,m,C,!0)}},le=(f,a,h,_,g)=>{if(a!==h){if(a!==B)for(const m in a)!kt(m)&&!(m in h)&&i(f,m,a[m],null,g,_);for(const m in h){if(kt(m))continue;const C=h[m],x=a[m];C!==x&&m!=="value"&&i(f,m,x,C,g,_)}"value"in h&&i(f,"value",a.value,h.value,g)}},Ce=(f,a,h,_,g,m,C,x,y)=>{const b=a.el=f?f.el:l(""),P=a.anchor=f?f.anchor:l("");let{patchFlag:E,dynamicChildren:I,slotScopeIds:F}=a;F&&(x=x?x.concat(F):F),f==null?(s(b,h,_),s(P,h,_),j(a.children||[],h,P,g,m,C,x,y)):E>0&&E&64&&I&&f.dynamicChildren?(G(f.dynamicChildren,I,h,g,m,C,x),(a.key!=null||g&&a===g.subTree)&&di(f,a,!0)):z(f,a,h,P,g,m,C,x,y)},qe=(f,a,h,_,g,m,C,x,y)=>{a.slotScopeIds=x,f==null?a.shapeFlag&512?g.ctx.activate(a,h,_,C,y):Ct(a,h,_,g,m,C,y):Jt(f,a,y)},Ct=(f,a,h,_,g,m,C)=>{const x=f.component=Ol(f,_,g);if(Ts(f)&&(x.ctx.renderer=At),kl(x,!1,C),x.asyncDep){if(g&&g.registerDep(x,ee,C),!f.el){const y=x.subTree=pe(Ke);D(null,y,a,h)}}else ee(x,f,a,h,g,m,C)},Jt=(f,a,h)=>{const _=a.component=f.component;if(xl(f,a,h))if(_.asyncDep&&!_.asyncResolved){L(_,a,h);return}else _.next=a,_.update();else a.el=f.el,_.vnode=a},ee=(f,a,h,_,g,m,C)=>{const x=()=>{if(f.isMounted){let{next:E,bu:I,u:F,parent:$,vnode:J}=f;{const Me=hi(f);if(Me){E&&(E.el=J.el,L(f,E,C)),Me.asyncDep.then(()=>{f.isUnmounted||x()});return}}let K=E,_e;st(f,!1),E?(E.el=J.el,L(f,E,C)):E=J,I&&_t(I),(_e=E.props&&E.props.onVnodeBeforeUpdate)&&we(_e,$,E,J),st(f,!0);const ge=Gs(f),Ae=f.subTree;f.subTree=ge,A(Ae,ge,p(Ae.el),Xt(Ae),f,g,m),E.el=ge.el,K===null&&Sl(f,ge.el),F&&ie(F,g),(_e=E.props&&E.props.onVnodeUpdated)&&ie(()=>we(_e,$,E,J),g)}else{let E;const{el:I,props:F}=a,{bm:$,m:J,parent:K,root:_e,type:ge}=f,Ae=wt(a);st(f,!1),$&&_t($),!Ae&&(E=F&&F.onVnodeBeforeMount)&&we(E,K,a),st(f,!0);{_e.ce&&_e.ce._injectChildStyle(ge);const Me=f.subTree=Gs(f);A(null,Me,h,_,f,g,m),a.el=Me.el}if(J&&ie(J,g),!Ae&&(E=F&&F.onVnodeMounted)){const Me=a;ie(()=>we(E,K,Me),g)}(a.shapeFlag&256||K&&wt(K.vnode)&&K.vnode.shapeFlag&256)&&f.a&&ie(f.a,g),f.isMounted=!0,a=h=_=null}};f.scope.on();const y=f.effect=new Mr(x);f.scope.off();const b=f.update=y.run.bind(y),P=f.job=y.runIfDirty.bind(y);P.i=f,P.id=f.uid,y.scheduler=()=>xs(P),st(f,!0),b()},L=(f,a,h)=>{a.component=f;const _=f.vnode.props;f.vnode=a,f.next=null,ol(f,a.props,_,h),al(f,a.children,h),Ue(),Ns(f),We()},z=(f,a,h,_,g,m,C,x,y=!1)=>{const b=f&&f.children,P=f?f.shapeFlag:0,E=a.children,{patchFlag:I,shapeFlag:F}=a;if(I>0){if(I&128){Yt(b,E,h,_,g,m,C,x,y);return}else if(I&256){et(b,E,h,_,g,m,C,x,y);return}}F&8?(P&16&&Et(b,g,m),E!==b&&u(h,E)):P&16?F&16?Yt(b,E,h,_,g,m,C,x,y):Et(b,g,m,!0):(P&8&&u(h,""),F&16&&j(E,h,_,g,m,C,x,y))},et=(f,a,h,_,g,m,C,x,y)=>{f=f||gt,a=a||gt;const b=f.length,P=a.length,E=Math.min(b,P);let I;for(I=0;I<E;I++){const F=a[I]=y?Je(a[I]):Oe(a[I]);A(f[I],F,h,null,g,m,C,x,y)}b>P?Et(f,g,m,!0,!1,E):j(a,h,_,g,m,C,x,y,E)},Yt=(f,a,h,_,g,m,C,x,y)=>{let b=0;const P=a.length;let E=f.length-1,I=P-1;for(;b<=E&&b<=I;){const F=f[b],$=a[b]=y?Je(a[b]):Oe(a[b]);if(pt(F,$))A(F,$,h,null,g,m,C,x,y);else break;b++}for(;b<=E&&b<=I;){const F=f[E],$=a[I]=y?Je(a[I]):Oe(a[I]);if(pt(F,$))A(F,$,h,null,g,m,C,x,y);else break;E--,I--}if(b>E){if(b<=I){const F=I+1,$=F<P?a[F].el:_;for(;b<=I;)A(null,a[b]=y?Je(a[b]):Oe(a[b]),h,$,g,m,C,x,y),b++}}else if(b>I)for(;b<=E;)Ee(f[b],g,m,!0),b++;else{const F=b,$=b,J=new Map;for(b=$;b<=I;b++){const be=a[b]=y?Je(a[b]):Oe(a[b]);be.key!=null&&J.set(be.key,b)}let K,_e=0;const ge=I-$+1;let Ae=!1,Me=0;const Mt=new Array(ge);for(b=0;b<ge;b++)Mt[b]=0;for(b=F;b<=E;b++){const be=f[b];if(_e>=ge){Ee(be,g,m,!0);continue}let Re;if(be.key!=null)Re=J.get(be.key);else for(K=$;K<=I;K++)if(Mt[K-$]===0&&pt(be,a[K])){Re=K;break}Re===void 0?Ee(be,g,m,!0):(Mt[Re-$]=b+1,Re>=Me?Me=Re:Ae=!0,A(be,a[Re],h,null,g,m,C,x,y),_e++)}const Fs=Ae?pl(Mt):gt;for(K=Fs.length-1,b=ge-1;b>=0;b--){const be=$+b,Re=a[be],ks=be+1<P?a[be+1].el:_;Mt[b]===0?A(null,Re,h,ks,g,m,C,x,y):Ae&&(K<0||b!==Fs[K]?tt(Re,h,ks,2):K--)}}},tt=(f,a,h,_,g=null)=>{const{el:m,type:C,transition:x,children:y,shapeFlag:b}=f;if(b&6){tt(f.component.subTree,a,h,_);return}if(b&128){f.suspense.move(a,h,_);return}if(b&64){C.move(f,a,h,At);return}if(C===Pe){s(m,a,h);for(let E=0;E<y.length;E++)tt(y[E],a,h,_);s(f.anchor,a,h);return}if(C===sn){T(f,a,h);return}if(_!==2&&b&1&&x)if(_===0)x.beforeEnter(m),s(m,a,h),ie(()=>x.enter(m),g);else{const{leave:E,delayLeave:I,afterLeave:F}=x,$=()=>{f.ctx.isUnmounted?r(m):s(m,a,h)},J=()=>{E(m,()=>{$(),F&&F()})};I?I(m,$,J):J()}else s(m,a,h)},Ee=(f,a,h,_=!1,g=!1)=>{const{type:m,props:C,ref:x,children:y,dynamicChildren:b,shapeFlag:P,patchFlag:E,dirs:I,cacheIndex:F}=f;if(E===-2&&(g=!1),x!=null&&(Ue(),hn(x,null,h,f,!0),We()),F!=null&&(a.renderCache[F]=void 0),P&256){a.ctx.deactivate(f);return}const $=P&1&&I,J=!wt(f);let K;if(J&&(K=C&&C.onVnodeBeforeUnmount)&&we(K,a,f),P&6)Oi(f.component,h,_);else{if(P&128){f.suspense.unmount(h,_);return}$&&nt(f,null,a,"beforeUnmount"),P&64?f.type.remove(f,a,h,At,_):b&&!b.hasOnce&&(m!==Pe||E>0&&E&64)?Et(b,a,h,!1,!0):(m===Pe&&E&384||!g&&P&16)&&Et(y,a,h),_&&Ps(f)}(J&&(K=C&&C.onVnodeUnmounted)||$)&&ie(()=>{K&&we(K,a,f),$&&nt(f,null,a,"unmounted")},h)},Ps=f=>{const{type:a,el:h,anchor:_,transition:g}=f;if(a===Pe){Pi(h,_);return}if(a===sn){v(f);return}const m=()=>{r(h),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(f.shapeFlag&1&&g&&!g.persisted){const{leave:C,delayLeave:x}=g,y=()=>C(h,m);x?x(f.el,m,y):y()}else m()},Pi=(f,a)=>{let h;for(;f!==a;)h=S(f),r(f),f=h;r(a)},Oi=(f,a,h)=>{const{bum:_,scope:g,job:m,subTree:C,um:x,m:y,a:b,parent:P,slots:{__:E}}=f;gn(y),gn(b),_&&_t(_),P&&O(E)&&E.forEach(I=>{P.renderCache[I]=void 0}),g.stop(),m&&(m.flags|=8,Ee(C,f,a,h)),x&&ie(x,a),ie(()=>{f.isUnmounted=!0},a),a&&a.pendingBranch&&!a.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===a.pendingId&&(a.deps--,a.deps===0&&a.resolve())},Et=(f,a,h,_=!1,g=!1,m=0)=>{for(let C=m;C<f.length;C++)Ee(f[C],a,h,_,g)},Xt=f=>{if(f.shapeFlag&6)return Xt(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const a=S(f.anchor||f.el),h=a&&a[Io];return h?S(h):a};let Pn=!1;const Os=(f,a,h)=>{f==null?a._vnode&&Ee(a._vnode,null,null,!0):A(a._vnode||null,f,a,null,null,null,h),a._vnode=f,Pn||(Pn=!0,Ns(),zr(),Pn=!1)},At={p:A,um:Ee,m:tt,r:Ps,mt:Ct,mc:j,pc:z,pbc:G,n:Xt,o:e};return{render:Os,hydrate:void 0,createApp:nl(Os)}}function $n({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function st({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function hl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function di(e,t,n=!1){const s=e.children,r=t.children;if(O(s)&&O(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=Je(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&di(o,l)),l.type===Mn&&(l.el=o.el),l.type===Ke&&!l.el&&(l.el=o.el)}}function pl(e){const t=e.slice(),n=[0];let s,r,i,o,l;const c=e.length;for(s=0;s<c;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<d?i=l+1:o=l;d<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function hi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:hi(t)}function gn(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const gl=Symbol.for("v-scx"),ml=()=>Lt(gl);function Nt(e,t,n){return pi(e,t,n)}function pi(e,t,n=B){const{immediate:s,deep:r,flush:i,once:o}=n,l=ae({},n),c=t&&s||!t&&i!=="post";let d;if(yt){if(i==="sync"){const w=ml();d=w.__watcherHandles||(w.__watcherHandles=[])}else if(!c){const w=()=>{};return w.stop=Fe,w.resume=Fe,w.pause=Fe,w}}const u=se;l.call=(w,R,A)=>De(w,u,R,A);let p=!1;i==="post"?l.scheduler=w=>{ie(w,u&&u.suspense)}:i!=="sync"&&(p=!0,l.scheduler=(w,R)=>{R?w():xs(w)}),l.augmentJob=w=>{t&&(w.flags|=4),p&&(w.flags|=2,u&&(w.id=u.uid,w.i=u))};const S=Co(e,t,l);return yt&&(d?d.push(S):c&&S()),S}function _l(e,t,n){const s=this.proxy,r=Q(e)?e.includes(".")?gi(s,e):()=>s[e]:e.bind(s,s);let i;k(t)?i=t:(i=t.handler,n=t);const o=zt(this),l=pi(r,i.bind(s),n);return o(),l}function gi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const bl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Se(t)}Modifiers`]||e[`${at(t)}Modifiers`];function vl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||B;let r=n;const i=t.startsWith("update:"),o=i&&bl(s,t.slice(7));o&&(o.trim&&(r=n.map(u=>Q(u)?u.trim():u)),o.number&&(r=n.map(ln)));let l,c=s[l=On(t)]||s[l=On(Se(t))];!c&&i&&(c=s[l=On(at(t))]),c&&De(c,e,6,r);const d=s[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,De(d,e,6,r)}}function mi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!k(e)){const c=d=>{const u=mi(d,t,!0);u&&(l=!0,ae(o,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(X(e)&&s.set(e,null),null):(O(i)?i.forEach(c=>o[c]=null):ae(o,i),X(e)&&s.set(e,o),o)}function An(e,t){return!e||!vn(t)?!1:(t=t.slice(2).replace(/Once$/,""),W(e,t[0].toLowerCase()+t.slice(1))||W(e,at(t))||W(e,t))}function Gs(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:c,render:d,renderCache:u,props:p,data:S,setupState:w,ctx:R,inheritAttrs:A}=e,H=dn(e);let D,M;try{if(n.shapeFlag&4){const v=r||s,V=v;D=Oe(d.call(V,v,u,p,w,S,R)),M=l}else{const v=t;D=Oe(v.length>1?v(p,{attrs:l,slots:o,emit:c}):v(p,null)),M=t.props?l:wl(l)}}catch(v){Ht.length=0,Gt(v,e,1),D=pe(Ke)}let T=D;if(M&&A!==!1){const v=Object.keys(M),{shapeFlag:V}=T;v.length&&V&7&&(i&&v.some(cs)&&(M=yl(M,i)),T=ft(T,M,!1,!0))}return n.dirs&&(T=ft(T,null,!1,!0),T.dirs=T.dirs?T.dirs.concat(n.dirs):n.dirs),n.transition&&Cn(T,n.transition),D=T,dn(H),D}const wl=e=>{let t;for(const n in e)(n==="class"||n==="style"||vn(n))&&((t||(t={}))[n]=e[n]);return t},yl=(e,t)=>{const n={};for(const s in e)(!cs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function xl(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:c}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?zs(s,o,d):!!o;if(c&8){const u=t.dynamicProps;for(let p=0;p<u.length;p++){const S=u[p];if(o[S]!==s[S]&&!An(d,S))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?zs(s,o,d):!0:!!o;return!1}function zs(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!An(n,i))return!0}return!1}function Sl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const mn=e=>e.__isSuspense;function Tl(e,t){t&&t.pendingBranch?O(e)?t.effects.push(...e):t.effects.push(e):Mo(e)}const Pe=Symbol.for("v-fgt"),Mn=Symbol.for("v-txt"),Ke=Symbol.for("v-cmt"),sn=Symbol.for("v-stc"),Ht=[];let ve=null;function re(e=!1){Ht.push(ve=e?null:[])}function Cl(){Ht.pop(),ve=Ht[Ht.length-1]||null}let Bt=1;function Js(e,t=!1){Bt+=e,e<0&&ve&&t&&(ve.hasOnce=!0)}function _i(e){return e.dynamicChildren=Bt>0?ve||gt:null,Cl(),Bt>0&&ve&&ve.push(e),e}function me(e,t,n,s,r,i){return _i(te(e,t,n,s,r,i,!0))}function Qn(e,t,n,s,r){return _i(pe(e,t,n,s,r,!0))}function Rs(e){return e?e.__v_isVNode===!0:!1}function pt(e,t){return e.type===t.type&&e.key===t.key}const bi=({key:e})=>e??null,rn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Q(e)||Z(e)||k(e)?{i:he,r:e,k:t,f:!!n}:e:null);function te(e,t=null,n=null,s=0,r=null,i=e===Pe?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&bi(t),ref:t&&rn(t),scopeId:Yr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:he};return l?(Is(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=Q(n)?8:16),Bt>0&&!o&&ve&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&ve.push(c),c}const pe=El;function El(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===ei)&&(e=Ke),Rs(e)){const l=ft(e,t,!0);return n&&Is(l,n),Bt>0&&!i&&ve&&(l.shapeFlag&6?ve[ve.indexOf(e)]=l:ve.push(l)),l.patchFlag=-2,l}if(Ll(e)&&(e=e.__vccOpts),t){t=Al(t);let{class:l,style:c}=t;l&&!Q(l)&&(t.class=it(l)),X(c)&&(vs(c)&&!O(c)&&(c=ae({},c)),t.style=us(c))}const o=Q(e)?1:mn(e)?128:Po(e)?64:X(e)?4:k(e)?2:0;return te(e,t,n,s,r,o,i,!0)}function Al(e){return e?vs(e)||oi(e)?ae({},e):e:null}function ft(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:c}=e,d=t?Rl(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&bi(d),ref:t&&t.ref?n&&i?O(i)?i.concat(rn(t)):[i,rn(t)]:rn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Pe?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ft(e.ssContent),ssFallback:e.ssFallback&&ft(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Cn(u,c.clone(u)),u}function Ml(e=" ",t=0){return pe(Mn,null,e,t)}function Zc(e,t){const n=pe(sn,null,e);return n.staticCount=t,n}function It(e="",t=!1){return t?(re(),Qn(Ke,null,e)):pe(Ke,null,e)}function Oe(e){return e==null||typeof e=="boolean"?pe(Ke):O(e)?pe(Pe,null,e.slice()):Rs(e)?Je(e):pe(Mn,null,String(e))}function Je(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ft(e)}function Is(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(O(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Is(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!oi(t)?t._ctx=he:r===3&&he&&(he.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else k(t)?(t={default:t,_ctx:he},n=32):(t=String(t),s&64?(n=16,t=[Ml(t)]):n=8);e.children=t,e.shapeFlag|=n}function Rl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=it([t.class,s.class]));else if(r==="style")t.style=us([t.style,s.style]);else if(vn(r)){const i=t[r],o=s[r];o&&i!==o&&!(O(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function we(e,t,n,s=null){De(e,t,7,[n,s])}const Il=si();let Pl=0;function Ol(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Il,i={uid:Pl++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Cr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ci(s,r),emitsOptions:mi(s,r),emit:null,emitted:null,propsDefaults:B,inheritAttrs:s.inheritAttrs,ctx:B,data:B,props:B,attrs:B,slots:B,refs:B,setupState:B,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=vl.bind(null,i),e.ce&&e.ce(i),i}let se=null;const Fl=()=>se||he;let _n,es;{const e=xn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};_n=t("__VUE_INSTANCE_SETTERS__",n=>se=n),es=t("__VUE_SSR_SETTERS__",n=>yt=n)}const zt=e=>{const t=se;return _n(e),e.scope.on(),()=>{e.scope.off(),_n(t)}},Ys=()=>{se&&se.scope.off(),_n(null)};function vi(e){return e.vnode.shapeFlag&4}let yt=!1;function kl(e,t=!1,n=!1){t&&es(t);const{props:s,children:r}=e.vnode,i=vi(e);il(e,s,i,t),fl(e,r,n||t);const o=i?Dl(e,t):void 0;return t&&es(!1),o}function Dl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Jo);const{setup:s}=n;if(s){Ue();const r=e.setupContext=s.length>1?jl(e):null,i=zt(e),o=qt(s,e,0,[e.props,r]),l=_r(o);if(We(),i(),(l||e.sp)&&!wt(e)&&Ss(e),l){if(o.then(Ys,Ys),t)return o.then(c=>{Xs(e,c)}).catch(c=>{Gt(c,e,0)});e.asyncDep=o}else Xs(e,o)}else wi(e)}function Xs(e,t,n){k(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:X(t)&&(e.setupState=Kr(t)),wi(e)}function wi(e,t,n){const s=e.type;e.render||(e.render=s.render||Fe);{const r=zt(e);Ue();try{Yo(e)}finally{We(),r()}}}const Vl={get(e,t){return fe(e,"get",""),e[t]}};function jl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Vl),slots:e.slots,emit:e.emit,expose:t}}function Rn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Kr(ws(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in jt)return jt[n](e)},has(t,n){return n in t||n in jt}})):e.proxy}function ts(e,t=!0){return k(e)?e.displayName||e.name:e.name||t&&e.__name}function Ll(e){return k(e)&&"__vccOpts"in e}const Xe=(e,t)=>So(e,t,yt),Nl="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ns;const Zs=typeof window<"u"&&window.trustedTypes;if(Zs)try{ns=Zs.createPolicy("vue",{createHTML:e=>e})}catch{}const yi=ns?e=>ns.createHTML(e):e=>e,Hl="http://www.w3.org/2000/svg",$l="http://www.w3.org/1998/Math/MathML",je=typeof document<"u"?document:null,Qs=je&&je.createElement("template"),Ul={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?je.createElementNS(Hl,e):t==="mathml"?je.createElementNS($l,e):n?je.createElement(e,{is:n}):je.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>je.createTextNode(e),createComment:e=>je.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>je.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Qs.innerHTML=yi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Qs.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Wl=Symbol("_vtc");function Kl(e,t,n){const s=e[Wl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const bn=Symbol("_vod"),xi=Symbol("_vsh"),Qc={beforeMount(e,{value:t},{transition:n}){e[bn]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Pt(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),Pt(e,!0),s.enter(e)):s.leave(e,()=>{Pt(e,!1)}):Pt(e,t))},beforeUnmount(e,{value:t}){Pt(e,t)}};function Pt(e,t){e.style.display=t?e[bn]:"none",e[xi]=!t}const Bl=Symbol(""),ql=/(^|;)\s*display\s*:/;function Gl(e,t,n){const s=e.style,r=Q(n);let i=!1;if(n&&!r){if(t)if(Q(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&on(s,l,"")}else for(const o in t)n[o]==null&&on(s,o,"");for(const o in n)o==="display"&&(i=!0),on(s,o,n[o])}else if(r){if(t!==n){const o=s[Bl];o&&(n+=";"+o),s.cssText=n,i=ql.test(n)}}else t&&e.removeAttribute("style");bn in e&&(e[bn]=i?s.display:"",e[xi]&&(s.display="none"))}const er=/\s*!important$/;function on(e,t,n){if(O(n))n.forEach(s=>on(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=zl(e,t);er.test(n)?e.setProperty(at(s),n.replace(er,""),"important"):e[s]=n}}const tr=["Webkit","Moz","ms"],Un={};function zl(e,t){const n=Un[t];if(n)return n;let s=Se(t);if(s!=="filter"&&s in e)return Un[t]=s;s=yn(s);for(let r=0;r<tr.length;r++){const i=tr[r]+s;if(i in e)return Un[t]=i}return t}const nr="http://www.w3.org/1999/xlink";function sr(e,t,n,s,r,i=Ki(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(nr,t.slice(6,t.length)):e.setAttributeNS(nr,t,n):n==null||i&&!yr(n)?e.removeAttribute(t):e.setAttribute(t,i?"":ke(n)?String(n):n)}function rr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?yi(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=yr(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function He(e,t,n,s){e.addEventListener(t,n,s)}function Jl(e,t,n,s){e.removeEventListener(t,n,s)}const ir=Symbol("_vei");function Yl(e,t,n,s,r=null){const i=e[ir]||(e[ir]={}),o=i[t];if(s&&o)o.value=s;else{const[l,c]=Xl(t);if(s){const d=i[t]=ec(s,r);He(e,l,d,c)}else o&&(Jl(e,l,o,c),i[t]=void 0)}}const or=/(?:Once|Passive|Capture)$/;function Xl(e){let t;if(or.test(e)){t={};let s;for(;s=e.match(or);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):at(e.slice(2)),t]}let Wn=0;const Zl=Promise.resolve(),Ql=()=>Wn||(Zl.then(()=>Wn=0),Wn=Date.now());function ec(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;De(tc(s,n.value),t,5,[s])};return n.value=e,n.attached=Ql(),n}function tc(e,t){if(O(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const lr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,nc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?Kl(e,s,o):t==="style"?Gl(e,n,s):vn(t)?cs(t)||Yl(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):sc(e,t,s,o))?(rr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&sr(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Q(s))?rr(e,Se(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),sr(e,t,s,o))};function sc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&lr(t)&&k(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return lr(t)&&Q(n)?!1:t in e}const Qe=e=>{const t=e.props["onUpdate:modelValue"]||!1;return O(t)?n=>_t(t,n):t};function rc(e){e.target.composing=!0}function cr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const xe=Symbol("_assign"),fr={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[xe]=Qe(r);const i=s||r.props&&r.props.type==="number";He(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;n&&(l=l.trim()),i&&(l=ln(l)),e[xe](l)}),n&&He(e,"change",()=>{e.value=e.value.trim()}),t||(He(e,"compositionstart",rc),He(e,"compositionend",cr),He(e,"change",cr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:i}},o){if(e[xe]=Qe(o),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?ln(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},ic={deep:!0,created(e,t,n){e[xe]=Qe(n),He(e,"change",()=>{const s=e._modelValue,r=xt(e),i=e.checked,o=e[xe];if(O(s)){const l=ds(s,r),c=l!==-1;if(i&&!c)o(s.concat(r));else if(!i&&c){const d=[...s];d.splice(l,1),o(d)}}else if(St(s)){const l=new Set(s);i?l.add(r):l.delete(r),o(l)}else o(Si(e,i))})},mounted:ar,beforeUpdate(e,t,n){e[xe]=Qe(n),ar(e,t,n)}};function ar(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(O(t))r=ds(t,s.props.value)>-1;else if(St(t))r=t.has(s.props.value);else{if(t===n)return;r=ct(t,Si(e,!0))}e.checked!==r&&(e.checked=r)}const oc={created(e,{value:t},n){e.checked=ct(t,n.props.value),e[xe]=Qe(n),He(e,"change",()=>{e[xe](xt(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[xe]=Qe(s),t!==n&&(e.checked=ct(t,s.props.value))}},lc={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=St(t);He(e,"change",()=>{const i=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?ln(xt(o)):xt(o));e[xe](e.multiple?r?new Set(i):i:i[0]),e._assigning=!0,ys(()=>{e._assigning=!1})}),e[xe]=Qe(s)},mounted(e,{value:t}){ur(e,t)},beforeUpdate(e,t,n){e[xe]=Qe(n)},updated(e,{value:t}){e._assigning||ur(e,t)}};function ur(e,t){const n=e.multiple,s=O(t);if(!(n&&!s&&!St(t))){for(let r=0,i=e.options.length;r<i;r++){const o=e.options[r],l=xt(o);if(n)if(s){const c=typeof l;c==="string"||c==="number"?o.selected=t.some(d=>String(d)===String(l)):o.selected=ds(t,l)>-1}else o.selected=t.has(l);else if(ct(xt(o),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function xt(e){return"_value"in e?e._value:e.value}function Si(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const ef={created(e,t,n){nn(e,t,n,null,"created")},mounted(e,t,n){nn(e,t,n,null,"mounted")},beforeUpdate(e,t,n,s){nn(e,t,n,s,"beforeUpdate")},updated(e,t,n,s){nn(e,t,n,s,"updated")}};function cc(e,t){switch(e){case"SELECT":return lc;case"TEXTAREA":return fr;default:switch(t){case"checkbox":return ic;case"radio":return oc;default:return fr}}}function nn(e,t,n,s,r){const o=cc(e.tagName,n.props&&n.props.type)[r];o&&o(e,t,n,s)}const fc=["ctrl","shift","alt","meta"],ac={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>fc.some(n=>e[`${n}Key`]&&!t.includes(n))},uc=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...i)=>{for(let o=0;o<t.length;o++){const l=ac[t[o]];if(l&&l(r,t))return}return e(r,...i)})},dc=ae({patchProp:nc},Ul);let dr;function hc(){return dr||(dr=ul(dc))}const pc=(...e)=>{const t=hc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=mc(s);if(!r)return;const i=t._component;!k(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,gc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function gc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function mc(e){return Q(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Ti;const In=e=>Ti=e,Ci=Symbol();function ss(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var $t;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})($t||($t={}));function _c(){const e=Er(!0),t=e.run(()=>bt({}));let n=[],s=[];const r=ws({install(i){In(r),r._a=i,i.provide(Ci,r),i.config.globalProperties.$pinia=r,s.forEach(o=>n.push(o)),s=[]},use(i){return this._a?n.push(i):s.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const Ei=()=>{};function hr(e,t,n,s=Ei){e.push(t);const r=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),s())};return!n&&Ar()&&qi(r),r}function dt(e,...t){e.slice().forEach(n=>{n(...t)})}const bc=e=>e(),pr=Symbol(),Kn=Symbol();function rs(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];ss(r)&&ss(s)&&e.hasOwnProperty(n)&&!Z(s)&&!$e(s)?e[n]=rs(r,s):e[n]=s}return e}const vc=Symbol();function wc(e){return!ss(e)||!e.hasOwnProperty(vc)}const{assign:Ge}=Object;function yc(e){return!!(Z(e)&&e.effect)}function xc(e,t,n,s){const{state:r,actions:i,getters:o}=t,l=n.state.value[e];let c;function d(){l||(n.state.value[e]=r?r():{});const u=bo(n.state.value[e]);return Ge(u,i,Object.keys(o||{}).reduce((p,S)=>(p[S]=ws(Xe(()=>{In(n);const w=n._s.get(e);return o[S].call(w,w)})),p),{}))}return c=Ai(e,d,t,n,s,!0),c}function Ai(e,t,n={},s,r,i){let o;const l=Ge({actions:{}},n),c={deep:!0};let d,u,p=[],S=[],w;const R=s.state.value[e];!i&&!R&&(s.state.value[e]={}),bt({});let A;function H(j){let N;d=u=!1,typeof j=="function"?(j(s.state.value[e]),N={type:$t.patchFunction,storeId:e,events:w}):(rs(s.state.value[e],j),N={type:$t.patchObject,payload:j,storeId:e,events:w});const G=A=Symbol();ys().then(()=>{A===G&&(d=!0)}),u=!0,dt(p,N,s.state.value[e])}const D=i?function(){const{state:N}=n,G=N?N():{};this.$patch(le=>{Ge(le,G)})}:Ei;function M(){o.stop(),p=[],S=[],s._s.delete(e)}const T=(j,N="")=>{if(pr in j)return j[Kn]=N,j;const G=function(){In(s);const le=Array.from(arguments),Ce=[],qe=[];function Ct(L){Ce.push(L)}function Jt(L){qe.push(L)}dt(S,{args:le,name:G[Kn],store:V,after:Ct,onError:Jt});let ee;try{ee=j.apply(this&&this.$id===e?this:V,le)}catch(L){throw dt(qe,L),L}return ee instanceof Promise?ee.then(L=>(dt(Ce,L),L)).catch(L=>(dt(qe,L),Promise.reject(L))):(dt(Ce,ee),ee)};return G[pr]=!0,G[Kn]=N,G},v={_p:s,$id:e,$onAction:hr.bind(null,S),$patch:H,$reset:D,$subscribe(j,N={}){const G=hr(p,j,N.detached,()=>le()),le=o.run(()=>Nt(()=>s.state.value[e],Ce=>{(N.flush==="sync"?u:d)&&j({storeId:e,type:$t.direct,events:w},Ce)},Ge({},c,N)));return G},$dispose:M},V=Tn(v);s._s.set(e,V);const q=(s._a&&s._a.runWithContext||bc)(()=>s._e.run(()=>(o=Er()).run(()=>t({action:T}))));for(const j in q){const N=q[j];if(Z(N)&&!yc(N)||$e(N))i||(R&&wc(N)&&(Z(N)?N.value=R[j]:rs(N,R[j])),s.state.value[e][j]=N);else if(typeof N=="function"){const G=T(N,j);q[j]=G,l.actions[j]=N}}return Ge(V,q),Ge(U(V),q),Object.defineProperty(V,"$state",{get:()=>s.state.value[e],set:j=>{H(N=>{Ge(N,j)})}}),s._p.forEach(j=>{Ge(V,o.run(()=>j({store:V,app:s._a,pinia:s,options:l})))}),R&&i&&n.hydrate&&n.hydrate(V.$state,R),d=!0,u=!0,V}/*! #__NO_SIDE_EFFECTS__ */function Sc(e,t,n){let s,r;const i=typeof t=="function";typeof e=="string"?(s=e,r=i?n:t):(r=e,s=e.id);function o(l,c){const d=rl();return l=l||(d?Lt(Ci,null):null),l&&In(l),l=Ti,l._s.has(s)||(i?Ai(s,t,r,l):xc(s,r,l)),l._s.get(s)}return o.$id=s,o}function tf(e){{const t=U(e),n={};for(const s in t){const r=t[s];r.effect?n[s]=Xe({get:()=>e[s],set(i){e[s]=i}}):(Z(r)||$e(r))&&(n[s]=yo(e,s))}return n}}const Tc="modulepreload",Cc=function(e,t){return new URL(e,t).href},gr={},Bn=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){const o=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),c=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));r=Promise.allSettled(n.map(d=>{if(d=Cc(d,s),d in gr)return;gr[d]=!0;const u=d.endsWith(".css"),p=u?'[rel="stylesheet"]':"";if(!!s)for(let R=o.length-1;R>=0;R--){const A=o[R];if(A.href===d&&(!u||A.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${d}"]${p}`))return;const w=document.createElement("link");if(w.rel=u?"stylesheet":Tc,u||(w.as="script"),w.crossOrigin="",w.href=d,c&&w.setAttribute("nonce",c),document.head.appendChild(w),u)return new Promise((R,A)=>{w.addEventListener("load",R),w.addEventListener("error",()=>A(new Error(`Unable to preload CSS for ${d}`)))})}))}function i(o){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=o,window.dispatchEvent(l),!l.defaultPrevented)throw o}return r.then(o=>{for(const l of o||[])l.status==="rejected"&&i(l.reason);return t().catch(i)})},Mi=Sc("ui",{state:()=>({activeView:"SubtitlerView",availableViews:[{name:"SubtitlerView",displayName:"字幕工具",icon:"icon-subtitles"},{name:"GrpcTestView",displayName:"gRPC 测试",icon:"icon-grpc"},{name:"AISettingsView",displayName:"AI 服务配置",icon:"icon-ai-settings"}],isSidebarCollapsed:!1}),getters:{getActiveViewName:e=>e.activeView,getAvailableViews:e=>e.availableViews,getIsSidebarCollapsed:e=>e.isSidebarCollapsed},actions:{setActiveView(e){this.availableViews.some(t=>t.name===e)?this.activeView=e:console.warn(`View "${e}" is not available.`)},toggleSidebar(){this.isSidebarCollapsed=!this.isSidebarCollapsed}}}),Ri=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Ec={class:"flex items-center justify-between p-4 h-16 border-b border-white/20 backdrop-blur-sm"},Ac={key:0,class:"flex items-center space-x-2"},Mc={key:0,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5"},Rc={key:1,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5"},Ic={class:"flex-grow pt-6 px-3"},Pc={class:"space-y-2"},Oc=["onClick"],Fc={xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5"},kc={key:0,"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z"},Dc={key:1,"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z"},Vc={key:2,"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065zM15 12a3 3 0 11-6 0 3 3 0 016 0z"},jc={key:0,class:"font-medium text-white/90 group-hover:text-white transition-colors duration-200"},Lc={class:"p-4 border-t border-white/10"},Nc={key:0,class:"flex items-center space-x-2"},Hc={key:1,class:"flex justify-center"},$c={__name:"SidebarNav",setup(e){const t=Mi(),n=Xe(()=>t.getAvailableViews),s=Xe(()=>t.getActiveViewName),r=Xe(()=>t.getIsSidebarCollapsed),i=l=>{t.setActiveView(l)},o=()=>{t.toggleSidebar()};return(l,c)=>(re(),me("div",{class:it(["flex flex-col bg-gradient-to-b from-indigo-900 via-purple-900 to-pink-900 text-white transition-all duration-300 ease-in-out shadow-lg",r.value?"w-16":"w-56"])},[te("div",Ec,[r.value?It("",!0):(re(),me("div",Ac,c[0]||(c[0]=[te("div",{class:"w-8 h-8 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center"},[te("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5"},[te("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"})])],-1),te("span",{class:"text-lg font-bold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent"},"应用导航",-1)]))),te("button",{onClick:o,class:"p-2 rounded-lg hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-all duration-200"},[r.value?(re(),me("svg",Rc,c[2]||(c[2]=[te("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 4.5l7.5 7.5-7.5 7.5"},null,-1)]))):(re(),me("svg",Mc,c[1]||(c[1]=[te("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 19.5L8.25 12l7.5-7.5"},null,-1)])))])]),te("nav",Ic,[te("ul",Pc,[(re(!0),me(Pe,null,zo(n.value,d=>(re(),me("li",{key:d.name},[te("a",{href:"#",onClick:uc(u=>i(d.name),["prevent"]),class:it(["group flex items-center transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm hover:scale-105",[r.value?"px-2 py-2 rounded-lg justify-center":"px-3 py-3 rounded-xl",s.value===d.name?r.value?"bg-gradient-to-r from-cyan-400 to-blue-500 shadow-lg shadow-cyan-500/50 scale-110":"bg-gradient-to-r from-cyan-500/20 to-blue-500/20 bg-white/10 backdrop-blur-sm border border-white/20 shadow-lg shadow-cyan-500/20":""]])},[te("div",{class:it(["flex-shrink-0",{"mr-3":!r.value}])},[te("div",{class:it(["w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200",[s.value===d.name?r.value?"bg-white/20":"bg-gradient-to-r from-cyan-400 to-blue-500":"bg-white/10 group-hover:bg-white/20"]])},[(re(),me("svg",Fc,[d.icon==="icon-subtitles"?(re(),me("path",kc)):It("",!0),d.icon==="icon-grpc"?(re(),me("path",Dc)):It("",!0),d.icon==="icon-ai-settings"?(re(),me("path",Vc)):It("",!0)]))],2)],2),r.value?It("",!0):(re(),me("span",jc,Sr(d.displayName),1))],10,Oc)]))),128))])]),te("div",Lc,[r.value?(re(),me("div",Hc,c[4]||(c[4]=[te("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"},null,-1)]))):(re(),me("div",Nc,c[3]||(c[3]=[te("div",{class:"w-2 h-2 bg-green-400 rounded-full animate-pulse"},null,-1),te("p",{class:"text-xs text-white/60"},"版本 1.0.0 - 运行中",-1)])))])],2))}},Uc=Ri($c,[["__scopeId","data-v-04d5bc98"]]),Wc={id:"app-container",class:"flex h-screen bg-gradient-to-br from-slate-50 to-blue-50"},Kc={class:"flex-1 p-6 overflow-y-auto"},Bc={__name:"App",setup(e){const t=jn(()=>Bn(()=>import("./SubtitlerView-1izcaWE_.js"),__vite__mapDeps([0,1]),import.meta.url)),n=jn(()=>Bn(()=>import("./GrpcTestView-DfnSBKaq.js"),__vite__mapDeps([2,3]),import.meta.url)),s=jn(()=>Bn(()=>import("./AISettingsView-CsPB72Se.js"),__vite__mapDeps([4,5]),import.meta.url)),r=Mi(),i=Xe(()=>r.getActiveViewName),o=po({SubtitlerView:t,GrpcTestView:n,AISettingsView:s}),l=Xe(()=>o.value[i.value]||null),c=u=>{console.log("[App] 收到导航请求:",u),r.setActiveView(u)};let d=null;return Cs(()=>{window.electronAPI&&window.electronAPI.on&&(d=window.electronAPI.on("navigate-to-view",c))}),Es(()=>{d&&d()}),(u,p)=>(re(),me("div",Wc,[pe(Uc),te("main",Kc,[(re(),Qn(Do,null,[(re(),Qn(qo(l.value)))],1024))])]))}},qc=Ri(Bc,[["__scopeId","data-v-805a736a"]]);function is(){return Date.now().toString(36)+Math.random().toString(36).substring(2,7)}typeof window.workflowIntermediateResults>"u"&&(window.workflowIntermediateResults={transcript:null,segments:null,srt:null,translation:null});function os(e){(typeof e!="number"||isNaN(e)||e<0)&&(e=0);const t=new Date(e),n=String(t.getUTCHours()).padStart(2,"0"),s=String(t.getUTCMinutes()).padStart(2,"0"),r=String(t.getUTCSeconds()).padStart(2,"0"),i=String(t.getUTCMilliseconds()).padStart(3,"0");return`${n}:${s}:${r},${i}`}function Gc(e){if(!Array.isArray(e)||e.length===0)return"";let t="";return e.forEach((n,s)=>{let r,i;n.startTimeMs!==void 0&&n.endTimeMs!==void 0?(r=n.startTimeMs,i=n.endTimeMs):n.start_time_ms!==void 0&&n.end_time_ms!==void 0?(r=n.start_time_ms,i=n.end_time_ms):n.start!==void 0&&n.end!==void 0?(r=Math.round(n.start*1e3),i=Math.round(n.end*1e3)):(r=0,i=0);const o=os(r),l=os(i),c=n.text||"";t+=`${s+1}
`,t+=`${o} --> ${l}
`,t+=`${c}

`}),t.trim()}console.log("subtitler/utils.js loaded");const nf=Object.defineProperty({__proto__:null,formatMillisecondsToSrtTime:os,generateUniqueId:is,segmentsToSrt:Gc},Symbol.toStringTag,{value:"Module"}),mr={finalOriginalSRT:null,finalTranslatedSRT:null,finalSegments:[],finalTranscript:null,reset(){console.warn("[WorkflowData] RESETTING ALL DATA. Call stack trace:",new Error("WorkflowData.reset() called").stack),this.finalOriginalSRT=null,this.finalTranslatedSRT=null,this.finalSegments=[],this.finalTranscript=null,console.info("[WorkflowData] Reset all data (completed)")},setFinalResults(e,t,n,s){var r,i,o,l,c,d;this.finalOriginalSRT=e,this.finalTranslatedSRT=t,n!==void 0&&(this.finalSegments=(n||[]).map(u=>({...u,id:u.id||is()}))),this.finalTranscript=s,console.info("[WorkflowData] Set final results (summary):",{originalSRT_exists:!!e,originalSRTLength:(e==null?void 0:e.length)||0,translatedSRT_exists:!!t,translatedSRTLength:(t==null?void 0:t.length)||0,segments_count_to_set:n?n.length:this.finalSegments?this.finalSegments.length:"undefined/untouched",finalSegments_count_after_set:this.finalSegments.length,transcript_exists:!!s,firstFinalSegment_summary:this.finalSegments.length>0?{id:this.finalSegments[0].id,text_length:(r=this.finalSegments[0].text)==null?void 0:r.length,text_preview:((i=this.finalSegments[0].text)==null?void 0:i.substring(0,50))+(((o=this.finalSegments[0].text)==null?void 0:o.length)>50?"...":""),translatedText_length:(l=this.finalSegments[0].translatedText)==null?void 0:l.length,translatedText_preview:((c=this.finalSegments[0].translatedText)==null?void 0:c.substring(0,50))+(((d=this.finalSegments[0].translatedText)==null?void 0:d.length)>50?"...":""),start_time_ms:this.finalSegments[0].start_time_ms,end_time_ms:this.finalSegments[0].end_time_ms}:null}),typeof window<"u"&&(window.workflowIntermediateResults={segments:this.finalSegments,originalSRT:this.finalOriginalSRT,translatedSRT:this.finalTranslatedSRT,transcript:this.finalTranscript},console.info("[WorkflowData] Updated window.workflowIntermediateResults"))},updateFinalSegments(e){var t,n,s,r,i,o;e&&e.length>0?(this.finalSegments=e.map(l=>({...l,id:l.id||is()})),console.info("[WorkflowData] Updated finalSegments (summary):",{segmentsCount:this.finalSegments.length,firstSegment_summary:this.finalSegments.length>0?{id:this.finalSegments[0].id,text_length:(t=this.finalSegments[0].text)==null?void 0:t.length,text_preview:((n=this.finalSegments[0].text)==null?void 0:n.substring(0,50))+(((s=this.finalSegments[0].text)==null?void 0:s.length)>50?"...":""),translatedText_length:(r=this.finalSegments[0].translatedText)==null?void 0:r.length,translatedText_preview:((i=this.finalSegments[0].translatedText)==null?void 0:i.substring(0,50))+(((o=this.finalSegments[0].translatedText)==null?void 0:o.length)>50?"...":"")}:null}),typeof window<"u"&&window.workflowIntermediateResults&&(window.workflowIntermediateResults.segments=this.finalSegments,console.info("[WorkflowData] Updated window.workflowIntermediateResults.segments via updateFinalSegments"))):console.info("[WorkflowData] Attempted to update finalSegments, but no new segments provided or segments array was empty.")},getSegmentsForSave(){var t,n,s,r,i,o;if(!this.finalSegments||this.finalSegments.length===0)return console.error("[WorkflowData] No segments available for save (finalSegments is empty)"),[];console.info("[WorkflowData] Getting segments for save from finalSegments count:",this.finalSegments.length);const e=this.finalSegments.map(l=>{const c=l.text||l.original_text||"",d=l.translatedText||l.translated_text||"";return{text:c,translatedText:d,original_text:c,translated_text:d,startTimeMs:l.start_time_ms||l.startTimeMs||0,endTimeMs:l.end_time_ms||l.endTimeMs||0,start_time_ms:l.start_time_ms||l.startTimeMs||0,end_time_ms:l.end_time_ms||l.endTimeMs||0,id:l.id}});if(console.info("[WorkflowData] Generated segments for save count:",e.length),e.length>0){const l=e[0];console.info("[WorkflowData] First save segment (summary):",{original_text_length:(t=l.original_text)==null?void 0:t.length,original_text_preview:((n=l.original_text)==null?void 0:n.substring(0,50))+(((s=l.original_text)==null?void 0:s.length)>50?"...":""),translated_text_length:(r=l.translated_text)==null?void 0:r.length,translated_text_preview:((i=l.translated_text)==null?void 0:i.substring(0,50))+(((o=l.translated_text)==null?void 0:o.length)>50?"...":""),startTimeMs:l.startTimeMs,endTimeMs:l.endTimeMs,id:l.id})}return e}},zc={reset(){mr.reset()},videoToAudio:function(e,t){console.log("[SubtitlerClient] videoToAudio called with request:",e);const n=new EventTarget;return window.electronAPI.invoke("subtitler-full-workflow",{file_path:e.video_path,workflow_type:"vid_to_audio",request_word_timestamps:!1}).then(s=>{console.log("[SubtitlerClient] videoToAudio result:",s);const r={getStageName:()=>"VideoToAudio",getPercentage:()=>100,getMessage:()=>"Conversion complete",getIsError:()=>!1,getErrorMessage:()=>"",video_to_audio_response:{getAudioPath:()=>s.audio_path||e.video_path.replace(/\.[^/.]+$/,".wav"),audio_path:s.audio_path||e.video_path.replace(/\.[^/.]+$/,".wav")}};n.dispatchEvent(new CustomEvent("data",{detail:r})),n.dispatchEvent(new Event("end"))}).catch(s=>{console.error("[SubtitlerClient] videoToAudio error:",s),n.dispatchEvent(new CustomEvent("error",{detail:s}))}),n.on=function(s,r){return this.addEventListener(s,i=>r(i.detail||i)),this},n},audioToText:function(e,t){console.log("[SubtitlerClient] audioToText called with request:",e);const n=new EventTarget;return window.electronAPI.invoke("subtitler-full-workflow",{file_path:e.audio_path,workflow_type:"audio_to_text",request_word_timestamps:e.request_word_timestamps||!0}).then(s=>{console.log("[SubtitlerClient] audioToText result:",s);const r={getStageName:()=>"AudioToText",getPercentage:()=>100,getMessage:()=>"Transcription complete",getIsError:()=>!1,getErrorMessage:()=>"",audio_to_text_response:{getTranscript:()=>s.transcript||"",getSegmentsList:()=>s.segments||[],transcript:s.transcript||"",segments:s.segments||[]}};n.dispatchEvent(new CustomEvent("data",{detail:r})),n.dispatchEvent(new Event("end"))}).catch(s=>{console.error("[SubtitlerClient] audioToText error:",s),n.dispatchEvent(new CustomEvent("error",{detail:s}))}),n.on=function(s,r){return this.addEventListener(s,i=>r(i.detail||i)),this},n},generateSubtitles:function(e,t){console.log("[SubtitlerClient] generateSubtitles called with request:",e);const n=new EventTarget;return window.electronAPI.invoke("subtitler-full-workflow",{file_path:e.audio_path||e.video_path,workflow_type:"audio_to_srt",request_word_timestamps:!0}).then(s=>{console.log("[SubtitlerClient] generateSubtitles result:",s);const r={getStageName:()=>"GenerateSubtitles",getPercentage:()=>100,getMessage:()=>"Subtitle generation complete",getIsError:()=>!1,getErrorMessage:()=>"",generate_subtitles_response:{getSubtitleContent:()=>s.srt_content||"",srt_content:s.srt_content||""}};n.dispatchEvent(new CustomEvent("data",{detail:r})),n.dispatchEvent(new Event("end"))}).catch(s=>{console.error("[SubtitlerClient] generateSubtitles error:",s),n.dispatchEvent(new CustomEvent("error",{detail:s}))}),n.on=function(s,r){return this.addEventListener(s,i=>r(i.detail||i)),this},n},translateSubtitles:function(e,t){console.log("[SubtitlerClient] translateSubtitles called with request:",e);const n=new EventTarget;return window.electronAPI.invoke("subtitler-full-workflow",{file_path:e.audio_path||e.video_path,workflow_type:"audio_to_srt_trans",target_language:e.target_language||"zh-CN",request_word_timestamps:!0}).then(s=>{console.log("[SubtitlerClient] translateSubtitles result:",s);const r={getStageName:()=>"TranslateSubtitles",getPercentage:()=>100,getMessage:()=>"Translation complete",getIsError:()=>!1,getErrorMessage:()=>"",translate_subtitles_response:{getTranslatedContent:()=>s.translated_subtitle_content||"",translated_subtitle_content:s.translated_subtitle_content||""}};n.dispatchEvent(new CustomEvent("data",{detail:r})),n.dispatchEvent(new Event("end"))}).catch(s=>{console.error("[SubtitlerClient] translateSubtitles error:",s),n.dispatchEvent(new CustomEvent("error",{detail:s}))}),n.on=function(s,r){return this.addEventListener(s,i=>r(i.detail||i)),this},n},fullWorkflow:function(e,t){console.log("[SubtitlerClient] fullWorkflow called with request:",e),mr.reset();const n=new EventTarget;n.on=function(i,o){return this.addEventListener(i,l=>o(l.detail||l)),this};const s=i=>{console.log("[SubtitlerClient] Progress update:",i),n.dispatchEvent(new CustomEvent("data",{detail:i}))},r=window.electronAPI.onProgressUpdate(s);return window.electronAPI.invoke("subtitler-full-workflow",e).then(i=>{console.log("[SubtitlerClient] fullWorkflow result:",i);const o={getStageName:()=>"WorkflowComplete",getPercentage:()=>100,getMessage:()=>"Workflow completed successfully",getIsError:()=>!1,getErrorMessage:()=>""};n.dispatchEvent(new CustomEvent("data",{detail:o})),n.dispatchEvent(new Event("end")),r()}).catch(i=>{console.error("[SubtitlerClient] fullWorkflow error:",i),n.dispatchEvent(new CustomEvent("error",{detail:i})),r()}),n}};console.log("subtitler/apiClient.js (enhanced) loaded");typeof window<"u"&&(window.subtitlerClient=zc,console.log("[Vue Main] Made subtitlerClient globally available for Vue components"));const Ii=pc(qc),Jc=_c();Ii.use(Jc);Ii.mount("#vue-app");console.log("Vue app initialized and mounted to #vue-app");export{lc as A,fr as B,Es as C,tf as D,Zc as E,Pe as F,oc as G,ef as H,nf as I,Do as K,Bn as _,Ri as a,me as b,Xe as c,Sc as d,re as e,te as f,zo as g,It as h,it as i,us as j,Ml as k,Xc as l,Tn as m,ys as n,Cs as o,Qn as p,uc as q,bt as r,Gc as s,Sr as t,mo as u,Qc as v,Nt as w,qo as x,pe as y,ic as z};
