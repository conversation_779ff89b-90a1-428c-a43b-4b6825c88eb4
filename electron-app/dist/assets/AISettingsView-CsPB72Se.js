import{a as H,r as h,c as B,w as E,o as K,b as a,e as o,f as t,t as m,h as x,l as q,A as J,F as N,g as X,B as G,z as W,i as k,k as z,H as F,q as L,p as T,n as Q}from"./index-5pfIJIcs.js";const Y={class:"ai-service-config-form p-4 border border-gray-200 rounded-lg shadow-sm"},Z={class:"text-lg font-semibold mb-6"},ee={class:"mb-4"},te=["disabled"],se=["value"],le={class:"mb-4"},oe={class:"mb-4"},re={key:0,class:"text-xs text-red-500 mt-1"},ae={key:1,class:"text-xs text-gray-500 mt-1"},ie={class:"mb-6"},ne={for:"is_enabled",class:"flex items-center cursor-pointer"},de={class:"relative"},ue={key:0},ce={key:0,class:"mb-6 p-4 border border-yellow-300 bg-yellow-50 rounded-md"},ve=["for"],pe={key:0,class:"text-red-500"},me={class:"relative"},be=["type","id","onUpdate:modelValue","placeholder","required"],ge=["onClick"],xe={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},fe={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},_e={key:0,class:"text-xs text-gray-500 mt-1"},ye={key:1,class:"mb-6 p-4 border border-gray-200 bg-gray-50 rounded-md"},he=["for"],we={key:0,class:"text-red-500"},ke={key:0,class:"space-y-2"},Ie={class:"flex items-center space-x-2"},Ae=["id","onUpdate:modelValue","required"],$e={key:0,label:"可用模型"},Ce=["value"],Se={key:1,label:"常用模型"},qe=["value"],Pe=["onClick","disabled"],Ve={key:0,class:"flex items-center"},Me={key:1},De={class:"flex items-center space-x-2"},Ne=["onUpdate:modelValue","placeholder"],Xe={key:0,class:"text-xs text-red-600"},Oe={key:1,class:"text-xs text-green-600"},Be=["type","id","onUpdate:modelValue","placeholder","required","step","min","max"],ze={key:2,class:"text-xs text-gray-500 mt-1"},Le={key:1,class:"text-center text-gray-500 py-4"},Ue={key:2,class:"mt-6 p-4 border border-blue-200 bg-blue-50 rounded-md"},Re={class:"flex items-start"},je={key:0,class:"h-5 w-5 text-green-500 mt-0.5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ee={key:1,class:"h-5 w-5 text-red-500 mt-0.5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ge={class:"flex-1"},Je={class:"mt-1 whitespace-pre-wrap"},Fe=["disabled"],He={key:0,class:"flex items-center"},Ke={key:1},We={class:"mt-8 flex justify-end space-x-3"},Te=["disabled"],Qe={__name:"AIServiceConfigForm",props:{serviceConfig:{type:Object,default:()=>null},isNew:{type:Boolean,default:!1}},emits:["save","cancel"],setup(O,{emit:f}){const g=O,y=f,e=h({}),C=h(""),w=h(""),I=h(!1),p=h(null),A=h({}),V=h({}),P=h({}),U={OpenAI:{credentials:{api_key:{label:"API Key",type:"password",required:!0,placeholder:"sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",description:"您的 OpenAI API 密钥。"}},attributes:{api_base_url:{label:"API Base URL",type:"url",required:!1,placeholder:"https://api.openai.com/v1",description:"OpenAI API 的基础地址，留空使用官方默认。"},default_model:{label:"默认模型",type:"model_select",required:!0,placeholder:"gpt-4o",description:"选择或输入模型名称",fallback_models:["gpt-4o","gpt-4o-mini","gpt-4-turbo","gpt-3.5-turbo","gpt-3.5-turbo-16k"]},timeout_seconds:{label:"超时时间 (秒)",type:"number",required:!1,placeholder:"60",min:1},max_tokens:{label:"最大 Tokens",type:"number",required:!1,placeholder:"4096",min:1},temperature:{label:"Temperature",type:"number",required:!1,placeholder:"0.7",min:0,max:2,step:.1}}},DeepSeek:{credentials:{api_key:{label:"API Key",type:"password",required:!0,placeholder:"sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",description:"您的 DeepSeek API 密钥。"}},attributes:{api_base_url:{label:"API Base URL",type:"url",required:!1,placeholder:"https://api.deepseek.com",description:"DeepSeek API 的基础地址，留空使用官方默认。"},default_model:{label:"默认模型",type:"model_select",required:!0,placeholder:"deepseek-chat",description:"选择或输入模型名称",fallback_models:["deepseek-chat","deepseek-coder","deepseek-math"]}}},Gemini:{credentials:{api_key:{label:"API Key",type:"password",required:!0,placeholder:"AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXX",description:"您的 Google AI Gemini API 密钥。"}},attributes:{api_base_url:{label:"API Base URL",type:"url",required:!1,placeholder:"https://generativelanguage.googleapis.com/v1beta",description:"Gemini API 的基础地址，留空使用官方默认。"},default_model:{label:"默认模型",type:"model_select",required:!0,placeholder:"gemini-2.0-flash",description:"选择或输入模型名称",fallback_models:["gemini-2.0-flash","gemini-2.0-flash-001","gemini-1.5-pro-latest","gemini-1.5-flash-latest","gemini-1.5-flash-8b-latest","gemini-1.0-pro"]}}},VolcEngine:{credentials:{api_key:{label:"API Key",type:"password",required:!0,placeholder:"ark_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",description:"您的火山大模型 API 密钥，在方舟控制台获取。"}},attributes:{api_base_url:{label:"API Base URL",type:"url",required:!0,placeholder:"https://ark.cn-beijing.volces.com/api/v3",description:"火山大模型 API 的基础地址，根据您的业务所在地域进行配置。"},default_model:{label:"默认模型",type:"model_select",required:!0,placeholder:"deepseek-v3-250324",description:"选择或输入模型名称",fallback_models:["deepseek-v3-250324","moonshot-v1-128k","qwen-v2-72b","qwen-v2-7b"]},timeout_seconds:{label:"超时时间 (秒)",type:"number",required:!1,placeholder:"60",min:1},temperature:{label:"Temperature",type:"number",required:!1,placeholder:"0.7",min:0,max:2,step:.1}}}},R=h([{value:"OpenAI",label:"OpenAI"},{value:"DeepSeek",label:"DeepSeek"},{value:"Gemini",label:"Gemini"},{value:"VolcEngine",label:"火山大模型"}]),c=B(()=>e.value.provider_type?U[e.value.provider_type]:null),M=B(()=>{if(!c.value||!c.value.credentials)return!1;for(const i in c.value.credentials)if(c.value.credentials[i].required&&(!e.value.credentials[i]||e.value.credentials[i].trim()===""))return!1;return!0}),S=B(()=>e.value.provider_type?e.value.provider_type==="Gemini"?!0:M.value:!1),$=h({}),r=B(()=>g.isNew?"添加新的 AI 服务":`编辑 ${e.value.display_name||"服务"} 配置`);E(()=>g.serviceConfig,i=>{i?(e.value=JSON.parse(JSON.stringify(i)),e.value.credentials||(e.value.credentials={}),e.value.attributes||(e.value.attributes={}),e.value.metadata||(e.value.metadata={}),g.isNew?(C.value="",e.value.provider_type&&(e.value.provider_id=`${e.value.provider_type.toLowerCase()}_${Date.now().toString().slice(-5)}`)):C.value=i.provider_id,$.value={},c.value&&c.value.credentials&&Object.keys(c.value.credentials).forEach(s=>{c.value.credentials[s].type==="password"&&($.value[s]=!1)}),e.value.provider_type&&S.value&&(console.log("Auto-loading models for existing config:",e.value.provider_type),setTimeout(()=>{v(e.value.provider_type)},500))):(e.value={},C.value=""),w.value="",p.value=null},{immediate:!0,deep:!0}),E(()=>e.value.credentials,(i,s)=>{p.value&&(p.value=null),i!=null&&i.api_key&&i.api_key!==(s==null?void 0:s.api_key)&&e.value.provider_type&&S.value&&setTimeout(()=>{S.value&&(console.log("Auto-loading models due to API key change"),v(e.value.provider_type))},1e3)},{deep:!0}),E(()=>e.value.attributes,()=>{p.value&&(p.value=null)},{deep:!0});const n=()=>{g.isNew&&(e.value.credentials={},e.value.attributes={},e.value.provider_type?e.value.provider_id=`${e.value.provider_type.toLowerCase()}_${Date.now().toString().slice(-5)}`:e.value.provider_id=`new_service_${Date.now().toString().slice(-5)}`),$.value={},c.value&&c.value.credentials&&Object.keys(c.value.credentials).forEach(i=>{c.value.credentials[i].type==="password"&&($.value[i]=!1)}),w.value="",p.value=null,e.value.provider_type&&(A.value[e.value.provider_type]=[],P.value[e.value.provider_type]=null,e.value.provider_type==="Gemini"&&setTimeout(()=>{console.log("Auto-loading Gemini models after provider type selection"),v(e.value.provider_type)},500))},d=()=>{if(!e.value.provider_id||e.value.provider_id.trim()===""){w.value="服务 ID 不能为空。";return}if(/\s/.test(e.value.provider_id)){w.value="服务 ID 不能包含空格。";return}w.value=""},D=i=>{$.value[i]=!$.value[i]},v=async(i,s)=>{if(!(!i||!S.value)){V.value[i]=!0,P.value[i]=null;try{const l={provider_type:i,credentials:{...e.value.credentials},attributes:{...e.value.attributes}};console.log("Loading models for provider:",i);const u=await window.electronAPI.invoke("load-ai-models",l);u.success&&u.models?(A.value[i]=u.models,console.log(`Loaded ${u.models.length} models for ${i}`)):(P.value[i]=u.message||"获取模型列表失败",console.error("Failed to load models:",u.message))}catch(l){console.error("Error loading models:",l),P.value[i]=l.message||"网络错误"}finally{V.value[i]=!1}}},_=async()=>{if(!M.value){p.value={success:!1,message:"请先填写所有必需的凭证信息",details:null};return}I.value=!0,p.value=null;try{const i={provider_id:e.value.provider_id||"test_connection",provider_type:e.value.provider_type,display_name:e.value.display_name||"Test Connection",is_enabled:!0,credentials:{...e.value.credentials},attributes:{...e.value.attributes},metadata:{test_mode:!0}};console.log("Testing AI service connection:",i.provider_type);const s=await window.electronAPI.invoke("test-ai-service-connection",i);s.success?p.value={success:!0,message:s.message||`${i.provider_type} API 连接成功！`,details:s.details||null}:p.value={success:!1,message:s.message||"连接测试失败",details:s.details||s.error||null}}catch(i){console.error("Error testing AI service connection:",i),p.value={success:!1,message:"测试连接时发生错误",details:i.message||i.toString()}}finally{I.value=!1}},j=()=>{if(d(),w.value)return;if(c.value){for(const s of["credentials","attributes"])if(c.value[s])for(const l in c.value[s]){const u=c.value[s][l];if(u.required&&(e.value[s][l]===void 0||e.value[s][l]==="")){alert(`错误：字段 "${u.label}" 是必填项。`);return}}}const i=JSON.parse(JSON.stringify(e.value));g.isNew||(i.provider_id_original=C.value),y("save",i)};return K(()=>{g.isNew&&e.value.provider_type&&n()}),(i,s)=>(o(),a("div",Y,[t("h3",Z,m(r.value),1),t("form",{onSubmit:L(j,["prevent"])},[t("div",ee,[s[6]||(s[6]=t("label",{for:"provider_type",class:"block text-sm font-medium text-gray-700 mb-1"},"服务类型",-1)),q(t("select",{id:"provider_type","onUpdate:modelValue":s[0]||(s[0]=l=>e.value.provider_type=l),onChange:n,disabled:!O.isNew,class:"mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:""},[s[5]||(s[5]=t("option",{value:"",disabled:""},"请选择服务类型",-1)),(o(!0),a(N,null,X(R.value,l=>(o(),a("option",{key:l.value,value:l.value},m(l.label),9,se))),128))],40,te),[[J,e.value.provider_type]])]),t("div",le,[s[7]||(s[7]=t("label",{for:"display_name",class:"block text-sm font-medium text-gray-700 mb-1"},"显示名称",-1)),q(t("input",{type:"text",id:"display_name","onUpdate:modelValue":s[1]||(s[1]=l=>e.value.display_name=l),class:"mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:"",placeholder:"例如：我的主力 OpenAI"},null,512),[[G,e.value.display_name]])]),t("div",oe,[s[8]||(s[8]=t("label",{for:"provider_id",class:"block text-sm font-medium text-gray-700 mb-1"},"服务 ID",-1)),q(t("input",{type:"text",id:"provider_id","onUpdate:modelValue":s[2]||(s[2]=l=>e.value.provider_id=l),onBlur:d,class:"mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:"",placeholder:"例如：openai_default_01 (需唯一)"},null,544),[[G,e.value.provider_id]]),w.value?(o(),a("p",re,m(w.value),1)):(o(),a("p",ae,"唯一标识符，创建后若无必要请勿修改。"))]),t("div",ie,[t("label",ne,[t("div",de,[q(t("input",{type:"checkbox",id:"is_enabled",class:"sr-only","onUpdate:modelValue":s[3]||(s[3]=l=>e.value.is_enabled=l)},null,512),[[W,e.value.is_enabled]]),s[9]||(s[9]=t("div",{class:"block bg-gray-600 w-10 h-6 rounded-full"},null,-1)),t("div",{class:k([{"translate-x-full":e.value.is_enabled},"dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"])},null,2)]),s[10]||(s[10]=t("span",{class:"ml-3 text-sm font-medium text-gray-700"},"启用此服务",-1))])]),e.value.provider_type&&c.value?(o(),a("div",ue,[c.value.credentials&&Object.keys(c.value.credentials).length>0?(o(),a("div",ce,[s[13]||(s[13]=t("h4",{class:"text-md font-semibold mb-3 text-yellow-800"},"凭证 (Credentials)",-1)),(o(!0),a(N,null,X(c.value.credentials,(l,u)=>(o(),a("div",{key:u,class:"mb-4"},[t("label",{for:`cred-${u}`,class:"block text-sm font-medium text-gray-700 mb-1"},[z(m(l.label)+" ",1),l.required?(o(),a("span",pe,"*")):x("",!0)],8,ve),t("div",me,[q(t("input",{type:l.type==="password"&&!$.value[u]?"password":"text",id:`cred-${u}`,"onUpdate:modelValue":b=>e.value.credentials[u]=b,class:"mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:l.placeholder||"",required:l.required},null,8,be),[[F,e.value.credentials[u]]]),l.type==="password"?(o(),a("button",{key:0,type:"button",onClick:b=>D(u),class:"absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5 text-gray-500 hover:text-gray-700"},[$.value[u]?(o(),a("svg",fe,s[12]||(s[12]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7 .946-3.118 3.703-5.434 6.932-6.018M15 12a3 3 0 11-6 0 3 3 0 016 0zm6.042-1.042c.14-.392.268-.801.372-1.228M3.958 13.042c-.14.392-.268.801-.372 1.228"},null,-1),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3l18 18"},null,-1)]))):(o(),a("svg",xe,s[11]||(s[11]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))],8,ge)):x("",!0)]),l.description?(o(),a("p",_e,m(l.description),1)):x("",!0)]))),128))])):x("",!0),c.value.attributes&&Object.keys(c.value.attributes).length>0?(o(),a("div",ye,[s[17]||(s[17]=t("h4",{class:"text-md font-semibold mb-3 text-gray-700"},"属性 (Attributes)",-1)),(o(!0),a(N,null,X(c.value.attributes,(l,u)=>(o(),a("div",{key:u,class:"mb-4"},[t("label",{for:`attr-${u}`,class:"block text-sm font-medium text-gray-700 mb-1"},[z(m(l.label)+" ",1),l.required?(o(),a("span",we,"*")):x("",!0)],8,he),l.type==="model_select"?(o(),a("div",ke,[t("div",Ie,[q(t("select",{id:`attr-${u}`,"onUpdate:modelValue":b=>e.value.attributes[u]=b,class:"flex-1 py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:l.required},[s[14]||(s[14]=t("option",{value:""},"请选择模型",-1)),A.value[e.value.provider_type]&&A.value[e.value.provider_type].length>0?(o(),a("optgroup",$e,[(o(!0),a(N,null,X(A.value[e.value.provider_type],b=>(o(),a("option",{key:b.id,value:b.id},m(b.displayName||b.id)+" "+m(b.owned_by?`(${b.owned_by})`:""),9,Ce))),128))])):x("",!0),l.fallback_models?(o(),a("optgroup",Se,[(o(!0),a(N,null,X(l.fallback_models,b=>(o(),a("option",{key:b,value:b},m(b),9,qe))),128))])):x("",!0)],8,Ae),[[J,e.value.attributes[u]]]),t("button",{type:"button",onClick:b=>v(e.value.provider_type),disabled:!S.value||V.value[e.value.provider_type],class:"px-3 py-2 bg-green-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",title:"获取最新模型列表"},[V.value[e.value.provider_type]?(o(),a("span",Ve,s[15]||(s[15]=[t("svg",{class:"animate-spin h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):(o(),a("span",Me,"🔄"))],8,Pe)]),t("div",De,[s[16]||(s[16]=t("span",{class:"text-sm text-gray-500"},"或输入自定义模型:",-1)),q(t("input",{type:"text","onUpdate:modelValue":b=>e.value.attributes[u]=b,class:"flex-1 py-1 px-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:l.placeholder||""},null,8,Ne),[[G,e.value.attributes[u]]])]),P.value[e.value.provider_type]?(o(),a("div",Xe," 获取模型列表失败: "+m(P.value[e.value.provider_type]),1)):A.value[e.value.provider_type]&&A.value[e.value.provider_type].length>0?(o(),a("div",Oe," 已加载 "+m(A.value[e.value.provider_type].length)+" 个可用模型 ",1)):x("",!0)])):l.type==="text"||l.type==="number"||l.type==="url"?q((o(),a("input",{key:1,type:l.type==="number"?"number":"text",id:`attr-${u}`,"onUpdate:modelValue":b=>e.value.attributes[u]=b,class:"mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:l.placeholder||"",required:l.required,step:l.type==="number"&&l.step?l.step:void 0,min:l.type==="number"&&l.min!==void 0?l.min:void 0,max:l.type==="number"&&l.max!==void 0?l.max:void 0},null,8,Be)),[[F,e.value.attributes[u]]]):x("",!0),l.description?(o(),a("p",ze,m(l.description),1)):x("",!0)]))),128))])):x("",!0)])):e.value.provider_type&&!c.value?(o(),a("div",Le,' 未找到针对 "'+m(e.value.provider_type)+'" 类型的配置定义。 ',1)):x("",!0),e.value.provider_type&&c.value&&M.value?(o(),a("div",Ue,[s[22]||(s[22]=t("h4",{class:"text-md font-semibold mb-3 text-blue-800"},"测试连接",-1)),s[23]||(s[23]=t("p",{class:"text-sm text-blue-700 mb-3"},"在保存配置前，您可以测试 API 连接是否正常工作。",-1)),p.value?(o(),a("div",{key:0,class:k(["mb-3 p-3 rounded-md",p.value.success?"bg-green-100 border border-green-300":"bg-red-100 border border-red-300"])},[t("div",Re,[p.value.success?(o(),a("svg",je,s[18]||(s[18]=[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(o(),a("svg",Ee,s[19]||(s[19]=[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"},null,-1)]))),t("div",Ge,[t("p",{class:k(["text-sm font-medium",p.value.success?"text-green-800":"text-red-800"])},m(p.value.success?"✅ 连接测试成功！":"❌ 连接测试失败"),3),t("p",{class:k(["text-sm mt-1",p.value.success?"text-green-700":"text-red-700"])},m(p.value.message),3),p.value.details?(o(),a("div",{key:0,class:k(["mt-2 text-xs",p.value.success?"text-green-600":"text-red-600"])},[t("details",null,[s[20]||(s[20]=t("summary",{class:"cursor-pointer hover:underline"},"查看详细信息",-1)),t("pre",Je,m(p.value.details),1)])],2)):x("",!0)])])],2)):x("",!0),t("button",{type:"button",onClick:_,disabled:I.value||!M.value,class:"px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"},[I.value?(o(),a("span",He,s[21]||(s[21]=[t("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),z(" 测试中... ")]))):(o(),a("span",Ke,"测试连接"))],8,Fe)])):x("",!0),t("div",We,[t("button",{type:"button",onClick:s[4]||(s[4]=l=>i.$emit("cancel")),class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"}," 取消 "),t("button",{type:"submit",class:"px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",disabled:!!w.value},m(O.isNew?"添加服务":"保存更改"),9,Te)])],32)]))}},Ye=H(Qe,[["__scopeId","data-v-de873527"]]),Ze={class:"ai-settings-view p-4 flex h-full"},et={class:"space-y-3"},tt=["onClick"],st={class:"flex items-center justify-between"},lt={class:"flex-1 min-w-0"},ot={class:"flex items-center space-x-3"},rt={class:"font-semibold text-gray-900 mt-2 truncate"},at={class:"text-sm text-gray-600 truncate"},it={key:0,class:"ml-2"},nt={class:"flex items-center space-x-2 ml-4"},dt=["for"],ut={class:"relative"},ct=["id","onUpdate:modelValue","onChange"],vt=["onClick"],pt=["onClick"],mt={key:0,class:"text-center py-12"},bt={key:0,class:"w-2/3 pl-6 overflow-y-auto"},gt={__name:"AISettingsView",setup(O){const f=h([]),g=h(null),y=h(null),e=h(!1),C=h(0),w=async()=>{try{const r=await window.electronAPI.invoke("load-ai-configs");f.value=r||[],f.value.length>0&&g.value}catch(r){console.error("Failed to load AI configs:",r)}},I=async(r,n=0)=>{try{const v=JSON.parse(JSON.stringify(r)),_=await window.electronAPI.invoke("save-ai-configs",v);if(_.success)console.log("AI configs saved successfully.");else if(console.error("Failed to save AI configs:",_.message),_.message&&_.message.includes("gRPC client")&&n<3)return console.log(`Retrying save operation (attempt ${n+1}/3) in 2000ms...`),await new Promise(j=>setTimeout(j,2e3)),await I(r,n+1);return _.success}catch(v){return console.error("Error saving AI configs:",v),n<3&&(v.message.includes("gRPC")||v.message.includes("client"))?(console.log(`Retrying save operation due to error (attempt ${n+1}/3) in 2000ms...`),await new Promise(_=>setTimeout(_,2e3)),await I(r,n+1)):!1}},p=async()=>{try{const r=await window.electronAPI.invoke("check-ai-config-service");return console.log("AI Config Service availability:",r),r.available}catch(r){return console.error("Error checking AI Config Service availability:",r),!1}};K(async()=>{w(),await p()||console.warn("AI Config Service is not available yet. Some features may not work until the backend is ready.")});const A=r=>{g.value=r,e.value=!1,y.value=JSON.parse(JSON.stringify(r)),C.value++},V=()=>{g.value=null,e.value=!0,y.value={provider_id:`new_service_${Date.now()}`,provider_type:"",display_name:"",is_enabled:!0,credentials:{},attributes:{},metadata:{created_at:new Date().toISOString()}},C.value++},P=r=>{g.value=r,e.value=!1,y.value=JSON.parse(JSON.stringify(r)),C.value++},U=async r=>{if(!confirm("确定要删除此 AI 服务配置吗？"))return;f.value=f.value.filter(d=>d.provider_id!==r),await I(f.value)?(S(),g.value&&g.value.provider_id===r&&(g.value=null,y.value=null),y.value&&y.value.provider_id===r&&(y.value=null)):w()},R=async r=>{const n=f.value.findIndex(v=>v.provider_id===r.provider_id_original||v.provider_id===r.provider_id);if(r.provider_id_original&&r.provider_id_original!==r.provider_id){if(f.value.find(_=>_.provider_id===r.provider_id&&_.provider_id!==r.provider_id_original)){alert(`错误：服务 ID "${r.provider_id}" 已存在。请使用唯一的 ID。`);return}}else if(e.value&&f.value.find(_=>_.provider_id===r.provider_id)){alert(`错误：服务 ID "${r.provider_id}" 已存在。请使用唯一的 ID。`);return}const d={...r};if(delete d.provider_id_original,n!==-1?f.value.splice(n,1,d):f.value.push(d),await I(f.value)){y.value=null,g.value=d,e.value=!1,S(),await w(),await Q();const v=f.value.find(_=>_.provider_id===d.provider_id);v?g.value=v:y.value=null}},c=()=>{y.value=null,e.value=!1},M=async r=>{await I(f.value)?S():r.is_enabled=!r.is_enabled},S=()=>{try{window.electronAPI.invoke("broadcast-ai-config-updated"),console.log("[AISettingsView] AI配置更新事件已发送")}catch(r){console.warn("[AISettingsView] 发送AI配置更新事件失败:",r)}},$=r=>{const n={OpenAI:"bg-green-100 text-green-800",DeepSeek:"bg-purple-100 text-purple-800",Gemini:"bg-blue-100 text-blue-800",default:"bg-gray-100 text-gray-800"};return n[r]||n.default};return(r,n)=>(o(),a("div",Ze,[t("div",{class:k([y.value?"w-1/3 pr-4 border-r border-gray-300":"w-full","overflow-y-auto transition-all duration-300"])},[t("div",{class:"flex justify-between items-center mb-6"},[n[2]||(n[2]=t("h2",{class:"text-2xl font-bold text-gray-800"},"AI 服务管理",-1)),t("button",{onClick:V,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"},n[1]||(n[1]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),t("span",null,"添加服务",-1)]))]),t("div",et,[(o(!0),a(N,null,X(f.value,d=>{var D;return o(),a("div",{key:d.provider_id,onClick:v=>A(d),class:k(["p-4 rounded-lg cursor-pointer transition-all duration-200 border-2",g.value&&g.value.provider_id===d.provider_id?"bg-blue-50 border-blue-300 shadow-md":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm"])},[t("div",st,[t("div",lt,[t("div",ot,[t("span",{class:k([$(d.provider_type),"px-2 py-1 text-xs font-medium rounded-full"])},m(d.provider_type),3),t("div",{class:k([d.is_enabled?"bg-green-500":"bg-gray-400","w-2 h-2 rounded-full"])},null,2)]),t("h3",rt,m(d.display_name),1),t("p",at,[z(" ID: "+m(d.provider_id)+" ",1),(D=d.attributes)!=null&&D.default_model?(o(),a("span",it," • 模型: "+m(d.attributes.default_model),1)):x("",!0)])]),t("div",nt,[t("label",{for:"toggle-"+d.provider_id,class:"flex items-center cursor-pointer",onClick:n[0]||(n[0]=L(()=>{},["stop"]))},[t("div",ut,[q(t("input",{type:"checkbox",id:"toggle-"+d.provider_id,class:"sr-only","onUpdate:modelValue":v=>d.is_enabled=v,onChange:v=>M(d)},null,40,ct),[[W,d.is_enabled]]),t("div",{class:k([d.is_enabled?"bg-green-500":"bg-gray-400","block w-11 h-6 rounded-full transition-colors duration-200"])},null,2),t("div",{class:k([{"translate-x-5":d.is_enabled,"translate-x-0":!d.is_enabled},"dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform duration-200 shadow-sm"])},null,2)])],8,dt),t("button",{onClick:L(v=>P(d),["stop"]),class:"p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"},n[3]||(n[3]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]),8,vt),t("button",{onClick:L(v=>U(d.provider_id),["stop"]),class:"p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"},n[4]||(n[4]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)]),8,pt)])])],10,tt)}),128)),f.value.length?x("",!0):(o(),a("div",mt,[n[5]||(n[5]=t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-16 w-16 mx-auto text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1)),n[6]||(n[6]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"暂无 AI 服务",-1)),n[7]||(n[7]=t("p",{class:"text-gray-600 mb-4"},"开始添加您的第一个 AI 服务配置",-1)),t("button",{onClick:V,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"}," 添加服务 ")]))])],2),y.value?(o(),a("div",bt,[(o(),T(Ye,{key:C.value,"service-config":y.value,"is-new":e.value,onSave:R,onCancel:c},null,8,["service-config","is-new"]))])):x("",!0)]))}},ft=H(gt,[["__scopeId","data-v-5c8c1fb2"]]);export{ft as default};
