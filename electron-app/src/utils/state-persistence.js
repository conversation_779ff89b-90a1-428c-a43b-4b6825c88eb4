/**
 * 状态持久化工具
 * 
 * 提供状态保存、恢复和管理功能，支持操作中断后的状态恢复
 */

const STORAGE_KEYS = {
  WORKFLOW_STATE: 'subtitlesWorkflowState',
  PROGRESS_STATE: 'subtitlesProgressState',
  OPERATION_HISTORY: 'subtitlesOperationHistory',
  USER_PREFERENCES: 'subtitlesUserPreferences',
  OPERATION_METRICS: 'subtitlesOperationMetrics'
}

const MAX_HISTORY_ITEMS = 50
const STATE_VERSION = '1.0.0'

/**
 * 状态持久化管理器
 */
class StatePersistenceManager {
  constructor() {
    this.isInitialized = false
    this.storageType = this.detectStorageType()
    this.compressionEnabled = true
  }

  /**
   * 初始化持久化管理器
   */
  async initialize() {
    try {
      await this.validateStorage()
      await this.migrateOldData()
      this.isInitialized = true
      console.log('状态持久化管理器初始化成功')
    } catch (error) {
      console.error('状态持久化管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 检测存储类型
   */
  detectStorageType() {
    if (window.electronAPI) {
      return 'electron'
    } else if (window.localStorage) {
      return 'localStorage'
    } else {
      return 'memory'
    }
  }

  /**
   * 验证存储可用性
   */
  async validateStorage() {
    const testKey = '__storage_test__'
    const testValue = { test: true, timestamp: Date.now() }
    
    try {
      await this.setItem(testKey, testValue)
      const retrieved = await this.getItem(testKey)
      
      if (!retrieved || retrieved.test !== true) {
        throw new Error('存储验证失败')
      }
      
      await this.removeItem(testKey)
    } catch (error) {
      throw new Error(`存储不可用: ${error.message}`)
    }
  }

  /**
   * 迁移旧数据
   */
  async migrateOldData() {
    try {
      const currentVersion = await this.getItem('__persistence_version__')
      
      if (!currentVersion || currentVersion !== STATE_VERSION) {
        console.log('检测到版本变更，执行数据迁移...')
        
        // 这里可以添加具体的迁移逻辑
        await this.setItem('__persistence_version__', STATE_VERSION)
        
        console.log('数据迁移完成')
      }
    } catch (error) {
      console.warn('数据迁移失败:', error)
    }
  }

  /**
   * 保存工作流状态
   */
  async saveWorkflowState(state) {
    const stateData = {
      ...state,
      timestamp: Date.now(),
      version: STATE_VERSION
    }
    
    return await this.setItem(STORAGE_KEYS.WORKFLOW_STATE, stateData)
  }

  /**
   * 恢复工作流状态
   */
  async restoreWorkflowState() {
    const state = await this.getItem(STORAGE_KEYS.WORKFLOW_STATE)
    
    if (!state) {
      return null
    }

    // 检查状态是否过期（24小时）
    const isExpired = Date.now() - state.timestamp > 24 * 60 * 60 * 1000
    if (isExpired) {
      await this.removeItem(STORAGE_KEYS.WORKFLOW_STATE)
      return null
    }

    return state
  }

  /**
   * 保存进度状态
   */
  async saveProgressState(progressData) {
    const stateData = {
      progressUpdates: progressData.progressUpdates || [],
      currentProcessState: progressData.currentProcessState || null,
      editableSegments: progressData.editableSegments || [],
      timestamp: Date.now(),
      version: STATE_VERSION
    }
    
    return await this.setItem(STORAGE_KEYS.PROGRESS_STATE, stateData)
  }

  /**
   * 恢复进度状态
   */
  async restoreProgressState() {
    const state = await this.getItem(STORAGE_KEYS.PROGRESS_STATE)
    
    if (!state) {
      return {
        progressUpdates: [],
        currentProcessState: null,
        editableSegments: []
      }
    }

    return {
      progressUpdates: state.progressUpdates || [],
      currentProcessState: state.currentProcessState || null,
      editableSegments: state.editableSegments || []
    }
  }

  /**
   * 添加操作历史记录
   */
  async addOperationHistory(operation) {
    const history = await this.getOperationHistory()
    
    const newEntry = {
      ...operation,
      id: this.generateId(),
      timestamp: Date.now()
    }
    
    history.unshift(newEntry)
    
    // 限制历史记录数量
    if (history.length > MAX_HISTORY_ITEMS) {
      history.splice(MAX_HISTORY_ITEMS)
    }
    
    return await this.setItem(STORAGE_KEYS.OPERATION_HISTORY, history)
  }

  /**
   * 获取操作历史记录
   */
  async getOperationHistory() {
    const history = await this.getItem(STORAGE_KEYS.OPERATION_HISTORY)
    return history || []
  }

  /**
   * 清理操作历史记录
   */
  async clearOperationHistory() {
    return await this.removeItem(STORAGE_KEYS.OPERATION_HISTORY)
  }

  /**
   * 保存用户偏好设置
   */
  async saveUserPreferences(preferences) {
    const prefsData = {
      ...preferences,
      timestamp: Date.now(),
      version: STATE_VERSION
    }
    
    return await this.setItem(STORAGE_KEYS.USER_PREFERENCES, prefsData)
  }

  /**
   * 恢复用户偏好设置
   */
  async restoreUserPreferences() {
    const prefs = await this.getItem(STORAGE_KEYS.USER_PREFERENCES)
    return prefs || {}
  }

  /**
   * 保存操作指标
   */
  async saveOperationMetrics(metrics) {
    const metricsData = {
      ...metrics,
      timestamp: Date.now(),
      version: STATE_VERSION
    }
    
    return await this.setItem(STORAGE_KEYS.OPERATION_METRICS, metricsData)
  }

  /**
   * 获取操作指标
   */
  async getOperationMetrics() {
    const metrics = await this.getItem(STORAGE_KEYS.OPERATION_METRICS)
    return metrics || {}
  }

  /**
   * 清理所有持久化数据
   */
  async clearAllData() {
    const keys = Object.values(STORAGE_KEYS)
    
    for (const key of keys) {
      await this.removeItem(key)
    }
    
    console.log('所有持久化数据已清理')
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage() {
    try {
      const usage = {
        totalSize: 0,
        itemCount: 0,
        items: {}
      }

      for (const [name, key] of Object.entries(STORAGE_KEYS)) {
        const data = await this.getItem(key)
        if (data) {
          const size = this.calculateObjectSize(data)
          usage.items[name] = {
            key,
            size,
            itemCount: Array.isArray(data) ? data.length : 1
          }
          usage.totalSize += size
          usage.itemCount += usage.items[name].itemCount
        }
      }

      return usage
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
      return { totalSize: 0, itemCount: 0, items: {} }
    }
  }

  /**
   * 计算对象大小（字节）
   */
  calculateObjectSize(obj) {
    return new Blob([JSON.stringify(obj)]).size
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 底层存储操作 - 设置项目
   */
  async setItem(key, value) {
    try {
      const serializedValue = this.compressionEnabled 
        ? this.compress(JSON.stringify(value))
        : JSON.stringify(value)

      if (this.storageType === 'electron' && window.electronAPI.storage) {
        return await window.electronAPI.storage.set(key, serializedValue)
      } else if (this.storageType === 'localStorage') {
        localStorage.setItem(key, serializedValue)
        return true
      } else {
        // 内存存储（fallback）
        this.memoryStorage = this.memoryStorage || {}
        this.memoryStorage[key] = serializedValue
        return true
      }
    } catch (error) {
      console.error(`设置存储项目失败 [${key}]:`, error)
      throw error
    }
  }

  /**
   * 底层存储操作 - 获取项目
   */
  async getItem(key) {
    try {
      let serializedValue

      if (this.storageType === 'electron' && window.electronAPI.storage) {
        serializedValue = await window.electronAPI.storage.get(key)
      } else if (this.storageType === 'localStorage') {
        serializedValue = localStorage.getItem(key)
      } else {
        // 内存存储（fallback）
        this.memoryStorage = this.memoryStorage || {}
        serializedValue = this.memoryStorage[key]
      }

      if (!serializedValue) {
        return null
      }

      const jsonString = this.compressionEnabled 
        ? this.decompress(serializedValue)
        : serializedValue

      return JSON.parse(jsonString)
    } catch (error) {
      console.error(`获取存储项目失败 [${key}]:`, error)
      return null
    }
  }

  /**
   * 底层存储操作 - 移除项目
   */
  async removeItem(key) {
    try {
      if (this.storageType === 'electron' && window.electronAPI.storage) {
        return await window.electronAPI.storage.remove(key)
      } else if (this.storageType === 'localStorage') {
        localStorage.removeItem(key)
        return true
      } else {
        // 内存存储（fallback）
        this.memoryStorage = this.memoryStorage || {}
        delete this.memoryStorage[key]
        return true
      }
    } catch (error) {
      console.error(`移除存储项目失败 [${key}]:`, error)
      throw error
    }
  }

  /**
   * 简单压缩（Base64编码）
   */
  compress(text) {
    try {
      return btoa(unescape(encodeURIComponent(text)))
    } catch (error) {
      console.warn('压缩失败，使用原始文本:', error)
      return text
    }
  }

  /**
   * 简单解压缩
   */
  decompress(compressedText) {
    try {
      return decodeURIComponent(escape(atob(compressedText)))
    } catch (error) {
      console.warn('解压缩失败，可能是未压缩的文本:', error)
      return compressedText
    }
  }
}

// 创建全局实例
const statePersistence = new StatePersistenceManager()

// 导出便捷函数
export {
  statePersistence,
  StatePersistenceManager,
  STORAGE_KEYS
}

export default statePersistence 