/**
 * 节流管理器
 * 
 * 用于优化频繁的进度更新和UI操作，提高性能和用户体验
 */

/**
 * 节流管理器类
 */
class ThrottleManager {
  constructor() {
    this.throttledFunctions = new Map()
    this.activeTimeouts = new Map()
    this.config = {
      progressUpdateInterval: 100,    // 进度更新节流间隔(ms)
      uiUpdateInterval: 50,          // UI更新节流间隔(ms)
      apiCallInterval: 200,          // API调用节流间隔(ms)
      loggingInterval: 1000,         // 日志记录节流间隔(ms)
      defaultInterval: 100           // 默认节流间隔(ms)
    }
  }

  /**
   * 创建节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} interval - 节流间隔(ms)
   * @param {string} key - 唯一标识符
   * @param {Object} options - 节流选项
   */
  throttle(func, interval = this.config.defaultInterval, key = null, options = {}) {
    const {
      leading = true,      // 是否在开始时立即执行
      trailing = true,     // 是否在结束时执行
      maxWait = null      // 最大等待时间
    } = options

    const throttleKey = key || this.generateKey(func)
    
    if (this.throttledFunctions.has(throttleKey)) {
      return this.throttledFunctions.get(throttleKey)
    }

    let lastCallTime = 0
    let lastInvokeTime = 0
    let timerId = null
    let lastArgs = null
    let lastThis = null

    const throttledFunction = function(...args) {
      const now = Date.now()
      const sinceLastCall = now - lastCallTime
      const sinceLastInvoke = now - lastInvokeTime

      lastThis = this
      lastArgs = args
      lastCallTime = now

      const shouldInvoke = () => {
        return (lastInvokeTime === 0 && leading) ||
               sinceLastCall >= interval ||
               (maxWait && sinceLastInvoke >= maxWait)
      }

      const invokeFunc = () => {
        lastInvokeTime = Date.now()
        timerId = null
        return func.apply(lastThis, lastArgs)
      }

      const startTimer = (pendingFunc, wait) => {
        timerId = setTimeout(pendingFunc, wait)
      }

      const cancelTimer = () => {
        if (timerId !== null) {
          clearTimeout(timerId)
          timerId = null
        }
      }

      if (shouldInvoke()) {
        if (timerId === null) {
          return invokeFunc()
        }
        if (maxWait && sinceLastInvoke >= maxWait) {
          cancelTimer()
          return invokeFunc()
        }
      }

      if (timerId === null && trailing) {
        const remainingWait = interval - sinceLastCall
        startTimer(invokeFunc, remainingWait)
      }
    }

    // 添加取消方法
    throttledFunction.cancel = () => {
      if (timerId !== null) {
        clearTimeout(timerId)
        timerId = null
      }
      lastCallTime = 0
      lastInvokeTime = 0
    }

    // 添加立即执行方法
    throttledFunction.flush = () => {
      if (timerId !== null) {
        clearTimeout(timerId)
        return func.apply(lastThis, lastArgs)
      }
    }

    this.throttledFunctions.set(throttleKey, throttledFunction)
    return throttledFunction
  }

  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 防抖延迟(ms)
   * @param {string} key - 唯一标识符
   */
  debounce(func, delay = this.config.defaultInterval, key = null) {
    const debounceKey = key || this.generateKey(func)
    
    if (this.throttledFunctions.has(debounceKey)) {
      return this.throttledFunctions.get(debounceKey)
    }

    let timeoutId = null

    const debouncedFunction = function(...args) {
      const executeFunc = () => {
        timeoutId = null
        func.apply(this, args)
      }

      if (timeoutId !== null) {
        clearTimeout(timeoutId)
      }

      timeoutId = setTimeout(executeFunc, delay)
    }

    // 添加取消方法
    debouncedFunction.cancel = () => {
      if (timeoutId !== null) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
    }

    // 添加立即执行方法
    debouncedFunction.flush = () => {
      if (timeoutId !== null) {
        clearTimeout(timeoutId)
        timeoutId = null
        func.apply(this, arguments)
      }
    }

    this.throttledFunctions.set(debounceKey, debouncedFunction)
    return debouncedFunction
  }

  /**
   * 创建进度更新节流器
   * @param {Function} updateFunction - 进度更新函数
   * @param {string} key - 唯一标识符
   */
  createProgressThrottle(updateFunction, key = 'progress') {
    return this.throttle(
      updateFunction,
      this.config.progressUpdateInterval,
      `progress_${key}`,
      { leading: true, trailing: true }
    )
  }

  /**
   * 创建UI更新节流器
   * @param {Function} updateFunction - UI更新函数
   * @param {string} key - 唯一标识符
   */
  createUIThrottle(updateFunction, key = 'ui') {
    return this.throttle(
      updateFunction,
      this.config.uiUpdateInterval,
      `ui_${key}`,
      { leading: true, trailing: true }
    )
  }

  /**
   * 创建API调用节流器
   * @param {Function} apiFunction - API调用函数
   * @param {string} key - 唯一标识符
   */
  createAPIThrottle(apiFunction, key = 'api') {
    return this.throttle(
      apiFunction,
      this.config.apiCallInterval,
      `api_${key}`,
      { leading: false, trailing: true }
    )
  }

  /**
   * 创建日志记录节流器
   * @param {Function} logFunction - 日志记录函数
   * @param {string} key - 唯一标识符
   */
  createLogThrottle(logFunction, key = 'log') {
    return this.throttle(
      logFunction,
      this.config.loggingInterval,
      `log_${key}`,
      { leading: false, trailing: true }
    )
  }

  /**
   * 批量操作管理器
   */
  createBatchProcessor(processorFunction, options = {}) {
    const {
      batchSize = 10,
      maxWait = 1000,
      key = 'batch'
    } = options

    let batch = []
    let timeoutId = null

    const processBatch = () => {
      if (batch.length > 0) {
        const currentBatch = [...batch]
        batch = []
        processorFunction(currentBatch)
      }
      timeoutId = null
    }

    const batchProcessor = (item) => {
      batch.push(item)

      if (batch.length >= batchSize) {
        if (timeoutId !== null) {
          clearTimeout(timeoutId)
        }
        processBatch()
      } else if (timeoutId === null) {
        timeoutId = setTimeout(processBatch, maxWait)
      }
    }

    // 添加立即处理方法
    batchProcessor.flush = () => {
      if (timeoutId !== null) {
        clearTimeout(timeoutId)
      }
      processBatch()
    }

    // 添加取消方法
    batchProcessor.cancel = () => {
      if (timeoutId !== null) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      batch = []
    }

    this.throttledFunctions.set(`batch_${key}`, batchProcessor)
    return batchProcessor
  }

  /**
   * 智能进度更新器
   * 根据进度变化幅度动态调整更新频率
   */
  createSmartProgressUpdater(updateFunction, key = 'smart_progress') {
    let lastProgress = 0
    let lastUpdateTime = 0
    
    const smartUpdater = (currentProgress, ...args) => {
      const now = Date.now()
      const progressDiff = Math.abs(currentProgress - lastProgress)
      const timeDiff = now - lastUpdateTime

      // 根据进度变化幅度调整更新间隔
      let interval = this.config.progressUpdateInterval
      
      if (progressDiff >= 10) {
        // 大幅度变化，更频繁更新
        interval = 50
      } else if (progressDiff >= 5) {
        // 中等变化
        interval = 100
      } else if (progressDiff >= 1) {
        // 小幅度变化
        interval = 200
      } else {
        // 微小变化，较少更新
        interval = 500
      }

      // 强制更新条件
      const shouldForceUpdate = 
        timeDiff > 1000 ||  // 超过1秒未更新
        currentProgress === 0 ||  // 开始
        currentProgress === 100 ||  // 完成
        progressDiff >= 10  // 大幅度变化

      if (shouldForceUpdate || timeDiff >= interval) {
        updateFunction(currentProgress, ...args)
        lastProgress = currentProgress
        lastUpdateTime = now
      }
    }

    this.throttledFunctions.set(`smart_progress_${key}`, smartUpdater)
    return smartUpdater
  }

  /**
   * 生成函数唯一标识符
   */
  generateKey(func) {
    return `${func.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 更新节流配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.config }
  }

  /**
   * 清理指定的节流函数
   */
  clear(key) {
    if (this.throttledFunctions.has(key)) {
      const throttledFunc = this.throttledFunctions.get(key)
      if (throttledFunc.cancel) {
        throttledFunc.cancel()
      }
      this.throttledFunctions.delete(key)
    }
  }

  /**
   * 清理所有节流函数
   */
  clearAll() {
    for (const [key, throttledFunc] of this.throttledFunctions) {
      if (throttledFunc.cancel) {
        throttledFunc.cancel()
      }
    }
    this.throttledFunctions.clear()
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalThrottledFunctions: this.throttledFunctions.size,
      config: this.config,
      functionKeys: Array.from(this.throttledFunctions.keys())
    }
  }
}

// 创建全局实例
const throttleManager = new ThrottleManager()

// 导出便捷函数
export const throttle = (func, interval, key, options) => 
  throttleManager.throttle(func, interval, key, options)

export const debounce = (func, delay, key) => 
  throttleManager.debounce(func, delay, key)

export const createProgressThrottle = (func, key) => 
  throttleManager.createProgressThrottle(func, key)

export const createUIThrottle = (func, key) => 
  throttleManager.createUIThrottle(func, key)

export const createBatchProcessor = (func, options) => 
  throttleManager.createBatchProcessor(func, options)

export const createSmartProgressUpdater = (func, key) => 
  throttleManager.createSmartProgressUpdater(func, key)

export {
  throttleManager,
  ThrottleManager
}

export default throttleManager 