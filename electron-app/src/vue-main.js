import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './vue/App.vue';
import { subtitlerClient } from './js/renderer-modules/subtitler/apiClient.js';

// Import CSS files
// Import Tailwind CSS input file (not output.css)
import './css/input.css'; 
// Import custom layout CSS
import './css/layout.css';

// Make subtitlerClient globally available for Vue components
if (typeof window !== 'undefined') {
    window.subtitlerClient = subtitlerClient;
    console.log('[Vue Main] Made subtitlerClient globally available for Vue components');
}

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.mount('#vue-app');

console.log('Vue app initialized and mounted to #vue-app');