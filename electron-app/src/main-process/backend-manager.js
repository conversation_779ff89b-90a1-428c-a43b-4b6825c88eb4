const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const { app } = require('electron'); // Added app import
const portManager = require('./port-manager');
// Logger and mainWindow will be initialized/passed.
let windowManagerRef; // To access getMainWindow() and logToRenderer()
let grpcManagerRef; // To access sendClientStatusToRenderer() - this creates a circular dependency risk if not handled carefully.
let userDataPath; // To store app.getPath('userData')

// Backend executable names and paths
const PYTHON_PEX_NAME = 'server.pex';
const GO_EXECUTABLE_NAME = 'go_grpc_server';
const JAVA_EXECUTABLE_NAME = 'java-grpc-backend';
let pythonBackendPath;
let goBackendPath;
let javaBackendPath;

// To track backend processes
let backendProcesses = {
  python: null,
  go: null,
  java: null
};

function initializeBackendManager(winManager, grpcMgr) {
    windowManagerRef = winManager;
    grpcManagerRef = grpcMgr; // For sendClientStatusToRenderer
    portManager.initializeLogger(windowManagerRef.logToRenderer); // Initialize logger for portManager
    userDataPath = app.getPath('userData'); // Get and store userDataPath
    windowManagerRef.logToRenderer(`[BackendManager] User data path: ${userDataPath}`);

    // Determine backend paths
    if (!process.defaultApp) { // Packaged environment
        pythonBackendPath = path.join(process.resourcesPath, 'dist', PYTHON_PEX_NAME);
        goBackendPath = path.join(process.resourcesPath, 'dist', GO_EXECUTABLE_NAME);
        javaBackendPath = path.join(process.resourcesPath, 'dist', JAVA_EXECUTABLE_NAME);
    } else { // Development environment
        const projectRoot = path.join(__dirname, '..', '..', '..'); // electron-app/src/main-process -> electron-python-grpc-pex-demo
        pythonBackendPath = path.join(projectRoot, 'dist', PYTHON_PEX_NAME);
        goBackendPath = path.join(projectRoot, 'dist', GO_EXECUTABLE_NAME);
        javaBackendPath = path.join(projectRoot, 'dist', JAVA_EXECUTABLE_NAME);
    }
    windowManagerRef.logToRenderer(`[Path Init] Is Packaged (!process.defaultApp): ${!process.defaultApp}`);
    windowManagerRef.logToRenderer(`[Path Init] Current working directory: ${process.cwd()}`);
    windowManagerRef.logToRenderer(`[Path Init] __dirname: ${__dirname}`);
    windowManagerRef.logToRenderer(`[Path Init] process.resourcesPath: ${process.resourcesPath || 'undefined'}`);
    windowManagerRef.logToRenderer(`[Path Init] Python backend path: ${pythonBackendPath}`);
    windowManagerRef.logToRenderer(`[Path Init] Go backend path: ${goBackendPath}`);
    windowManagerRef.logToRenderer(`[Path Init] Java backend path: ${javaBackendPath}`);
}

function getBackendProcesses() {
    return backendProcesses;
}

function updateServiceStatus(serviceKey, statusText, bgColor) {
  windowManagerRef.logToRenderer(`[Main Process] Updating status for ${serviceKey}: ${statusText}`);
  if (windowManagerRef.getMainWindow() && windowManagerRef.getMainWindow().webContents) {
      windowManagerRef.getMainWindow().webContents.send('service-status-update', {
        service: serviceKey,
        status: statusText,
        bgColor: bgColor,
      });
  }
  if (serviceKey === 'java') {
      let javaSpecificStatus = 'unknown';
      if (statusText.toLowerCase().includes('running') || statusText.toLowerCase().includes('ready')) {
          javaSpecificStatus = 'running';
      } else if (statusText.toLowerCase().includes('error') || statusText.toLowerCase().includes('failed') || statusText.toLowerCase().includes('conflict')) {
          javaSpecificStatus = 'error';
      } else if (statusText.toLowerCase().includes('starting') || statusText.toLowerCase().includes('initializing') || statusText.toLowerCase().includes('connecting')) {
          javaSpecificStatus = 'initializing-client';
      } else if (statusText.toLowerCase().includes('stopped') || statusText.toLowerCase().includes('cancelled')) {
            javaSpecificStatus = 'stopped';
      }
      if (windowManagerRef.getMainWindow() && windowManagerRef.getMainWindow().webContents) {
        windowManagerRef.getMainWindow().webContents.send('java-status', javaSpecificStatus);
      }
  }
}


function startBackendProcess(options) {
  const {
    executablePath,
    processName,
    args = [],
    spawnOptions = {},
    onClientStatusError, // This callback is for gRPC client status, will be called by generic backend starter
  } = options;

  windowManagerRef.logToRenderer(`[Process Start] Attempting to start ${processName}. Path: ${executablePath}`);
  if (!fs.existsSync(executablePath)) {
    const errorMsg = `[Process Start] ERROR: ${processName} executable NOT FOUND at ${executablePath}.`;
    windowManagerRef.logToRenderer(errorMsg);
    updateServiceStatus(processName.toLowerCase().split(' ')[0], 'Not Found', 'bg-red-700');
    if (onClientStatusError) { // This is actually grpcManagerRef.sendClientStatusToRenderer
      onClientStatusError(false, `${processName} executable not found: ${executablePath}`);
    }
    return null;
  }
  windowManagerRef.logToRenderer(`[Process Start] Executable found at: ${executablePath}`);

  if (process.platform !== 'win32') {
    try {
      windowManagerRef.logToRenderer(`[Process Start] Attempting to set execute permissions (755) for: ${executablePath}`);
      fs.chmodSync(executablePath, '755');
      windowManagerRef.logToRenderer(`[Process Start] Successfully set execute permissions for: ${executablePath}`);
    } catch (err) {
      windowManagerRef.logToRenderer(`[Process Start] ERROR setting execute permissions for ${executablePath}: ${err.message}. Attempting to start anyway...`);
    }
  }

  let finalSpawnOptions = { ...spawnOptions };

  // Set working directory based on the process type
  if (!process.defaultApp) {
    if (processName.toLowerCase().includes('python')) {
      // Python backend needs to run from the resource directory to access api-protos
      finalSpawnOptions.cwd = process.resourcesPath;
    } else {
      // Other backends can run from resource directory
      finalSpawnOptions.cwd = process.resourcesPath;
    }
  }

  if (processName.toLowerCase().includes('python')) {
    const commonPythonPaths = ['/opt/homebrew/bin', '/usr/local/bin', '/usr/bin'];
    const existingPath = process.env.PATH || '/usr/bin:/bin:/usr/sbin:/sbin';
    const pathParts = [...new Set([...commonPythonPaths, ...existingPath.split(':').filter(p => p.length > 0)])];
    const newPath = pathParts.join(':');

    // --- BEGIN MODIFICATION: Securely set environment variables for Python process ---
    const envForPython = {
      PATH: newPath, // Essential for finding system executables
      HOME: process.env.HOME || '/Users/' + require('os').userInfo().username,
      USER: process.env.USER || require('os').userInfo().username,
      // Add other environment variables Python backend explicitly needs from process.env
    };

    // Explicitly pass known API keys if they exist in the Electron process's environment
    if (process.env.OPENAI_API_KEY) {
      envForPython.OPENAI_API_KEY = process.env.OPENAI_API_KEY;
      windowManagerRef.logToRenderer(`[Process Start] Passing OPENAI_API_KEY to Python process.`);
    } else {
      windowManagerRef.logToRenderer(`[Process Start] WARNING: OPENAI_API_KEY is not set in Electron's environment. Python backend might require it.`);
    }
    // Add other sensitive keys similarly, e.g., GEMINI_API_KEY
    if (process.env.GEMINI_API_KEY) {
      envForPython.GEMINI_API_KEY = process.env.GEMINI_API_KEY;
      windowManagerRef.logToRenderer(`[Process Start] Passing GEMINI_API_KEY to Python process.`);
    }
    // Add any other necessary non-sensitive environment variables that Python specifically needs
    // Example: envForPython.PYTHON_ENV = process.env.PYTHON_ENV || 'development';

    finalSpawnOptions.env = envForPython;
    windowManagerRef.logToRenderer(`[Process Start] Using extended PATH for Python: ${newPath}`);
    windowManagerRef.logToRenderer(`[Process Start] Working directory: ${finalSpawnOptions.cwd || process.cwd()}`);

    // --- UNIFIED LOGGING: Smart stdio configuration based on environment ---
    if (!process.defaultApp) {
      // 生产环境：静默模式，不输出到控制台
      windowManagerRef.logToRenderer(`[Process Start] Production mode: Python logs will only go to unified logging files.`);
      finalSpawnOptions.stdio = ['pipe', 'ignore', 'ignore']; // 忽略stdout/stderr
    } else {
      // 开发环境：允许控制台输出，统一显示
      windowManagerRef.logToRenderer(`[Process Start] Development mode: Python logs will show in console and unified logging files.`);
      finalSpawnOptions.stdio = ['pipe', 'inherit', 'inherit']; // 继承到主进程控制台
    }
    // --- END UNIFIED LOGGING MODIFICATION ---
  } else {
    // For Go and Java backends, also ensure proper environment
    const basicEnv = {
      PATH: process.env.PATH || '/usr/bin:/bin:/usr/sbin:/sbin',
      HOME: process.env.HOME || '/Users/' + require('os').userInfo().username,
      USER: process.env.USER || require('os').userInfo().username,
    };
    finalSpawnOptions.env = basicEnv;
  }

  // Additional debugging for Python backend
  if (processName.toLowerCase().includes('python')) {
    windowManagerRef.logToRenderer(`[Process Start] Python Debug - Current working directory: ${process.cwd()}`);
    windowManagerRef.logToRenderer(`[Process Start] Python Debug - Executable exists: ${require('fs').existsSync(executablePath)}`);
    try {
      const stats = require('fs').statSync(executablePath);
      windowManagerRef.logToRenderer(`[Process Start] Python Debug - Executable permissions: ${stats.mode.toString(8)}`);
      windowManagerRef.logToRenderer(`[Process Start] Python Debug - File size: ${stats.size} bytes`);
    } catch (e) {
      windowManagerRef.logToRenderer(`[Process Start] Python Debug - Error getting file stats: ${e.message}`);
    }
    windowManagerRef.logToRenderer(`[Process Start] Python Debug - PATH: ${finalSpawnOptions.env?.PATH || 'undefined'}`);
    windowManagerRef.logToRenderer(`[Process Start] Python Debug - Working directory: ${finalSpawnOptions.cwd || 'undefined'}`);
  }

  windowManagerRef.logToRenderer(`[Process Start] Spawning process: ${executablePath} with args: ${JSON.stringify(args)} and options: ${JSON.stringify(finalSpawnOptions)}`);
  const backendProcessInstance = spawn(executablePath, args, finalSpawnOptions); // Renamed to avoid conflict

  if (backendProcessInstance.stdout) {
    backendProcessInstance.stdout.on('data', (data) => {
      const chunk = data.toString().trim();
      // For processes where stdout is piped (not redirected to file like Python now), this will log.
      // For Python, this block might not be hit if stdout is fully redirected.
      const logMsg = `[${processName} STDOUT (piped)] ${chunk}`;
      windowManagerRef.logToRenderer(logMsg);
      let unifiedLogPrefix = `[${processName} STDOUT]:`;
      if (processName.toLowerCase().includes('python')) unifiedLogPrefix = '[Python STDOUT]:';
      else if (processName.toLowerCase().includes('go')) unifiedLogPrefix = '[Go STDOUT]:';
      else if (processName.toLowerCase().includes('java')) unifiedLogPrefix = '[Java Log]:';
      if (windowManagerRef.getMainWindow() && windowManagerRef.getMainWindow().webContents) {
          windowManagerRef.getMainWindow().webContents.send('unified-log', `${unifiedLogPrefix} ${chunk}`);
      }
    });
  } else {
    windowManagerRef.logToRenderer(`[Process Start] ${processName} stdout is not available for .on('data') listeners (likely redirected to file).`);
  }

  if (backendProcessInstance.stderr) {
    backendProcessInstance.stderr.on('data', (data) => {
      const chunk = data.toString().trim();
      // For processes where stderr is piped, this will log.
      // For Python, this block might not be hit if stderr is fully redirected.
      const errorMsg = `[${processName} STDERR (piped)] ${chunk}`;
      windowManagerRef.logToRenderer(errorMsg);
      let unifiedLogPrefix = `[${processName} STDERR]:`;
      if (processName.toLowerCase().includes('python')) unifiedLogPrefix = '[Python STDERR]:';
      else if (processName.toLowerCase().includes('go')) unifiedLogPrefix = '[Go STDERR]:';
      else if (processName.toLowerCase().includes('java')) unifiedLogPrefix = '[Java STDERR]:';
      if (windowManagerRef.getMainWindow() && windowManagerRef.getMainWindow().webContents) {
          windowManagerRef.getMainWindow().webContents.send('unified-log', `${unifiedLogPrefix} ${chunk}`);
      }
    });
  } else {
    windowManagerRef.logToRenderer(`[Process Start] ${processName} stderr is not available for .on('data') listeners (likely redirected to file).`);
  }

  // 'close' and 'error' events are on backendProcessInstance itself, not its streams.
  backendProcessInstance.on('close', (code) => {
    const closeMsg = `[Main Process] ${processName} process exited with code: ${code}`;
    windowManagerRef.logToRenderer(closeMsg);
    const serviceKey = processName.toLowerCase().split(' ')[0];
    const isErrorExit = code !== 0;
    const exitMessage = `${processName} process exited with code: ${code}${isErrorExit ? " (Error)" : ""}`;

    if (backendProcesses[serviceKey] !== undefined) {
        backendProcesses[serviceKey] = null;
        if (grpcManagerRef) grpcManagerRef.sendClientStatusToRenderer(`${serviceKey}-grpc-client-status`, false, exitMessage);
        const statusText = isErrorExit ? `Exited with error (${code})` : 'Stopped';
        const bgColor = isErrorExit ? 'bg-red-700' : 'bg-gray-500';
        updateServiceStatus(serviceKey, statusText, bgColor);
        if (serviceKey === 'java') {
            if (windowManagerRef.getMainWindow() && windowManagerRef.getMainWindow().webContents) {
                windowManagerRef.getMainWindow().webContents.send('java-status', isErrorExit ? 'error' : 'stopped');
            }
        }
    }
  });

  backendProcessInstance.on('error', (err) => {
    const errorMsg = `[Process Start] ERROR spawning ${processName} (PID: ${backendProcessInstance.pid}): ${err.message}`;
    windowManagerRef.logToRenderer(errorMsg);
    updateServiceStatus(processName.toLowerCase().split(' ')[0], 'Spawn Error', 'bg-red-700');
    if (onClientStatusError) { // This is grpcManagerRef.sendClientStatusToRenderer
      onClientStatusError(false, `Failed to spawn ${processName}: ${err.message}`);
    }
    const processKey = processName.toLowerCase().split(' ')[0];
    if (backendProcesses[processKey]) {
        backendProcesses[processKey] = null;
    }
  });

  if (backendProcessInstance.pid) {
    windowManagerRef.logToRenderer(`[Process Start] ${processName} process spawned successfully. PID: ${backendProcessInstance.pid}`);
  } else {
    windowManagerRef.logToRenderer(`[Process Start] ${processName} process failed to spawn (no PID). Check 'error' event logs.`);
    updateServiceStatus(processName.toLowerCase().split(' ')[0], 'Spawn Fail (No PID)', 'bg-red-700');
  }
  return backendProcessInstance;
}

async function startGenericBackend(config) {
  const {
    serviceName,
    serviceKey,
    address,
    executablePath,
    readyMessageDetector,
    spawnOptions = {},
    onSpecificLog, // For Java specific log channel
    onSpecificStatus, // For Java specific status channel
  } = config;

  const port = portManager.extractPort(address);

  if (backendProcesses[serviceKey]) {
    windowManagerRef.logToRenderer(`[${serviceName}] Already running (PID: ${backendProcesses[serviceKey].pid}).`);
    return;
  }

  updateServiceStatus(serviceKey, 'Port Check...', 'bg-yellow-500');
  // Pass mainWindow instance and updateServiceStatus callback to checkAndManagePort
  const canProceed = await portManager.checkAndManagePort(port, serviceName, windowManagerRef.getMainWindow(), updateServiceStatus);
  if (!canProceed) {
    windowManagerRef.logToRenderer(`[${serviceName}] Startup cancelled due to port ${port} issue.`);
    return;
  }

  updateServiceStatus(serviceKey, 'Starting...', 'bg-yellow-500');
  if (onSpecificStatus) {
    onSpecificStatus('starting-process', true);
  }

  let serverReadyNotified = false;

  const processInstance = startBackendProcess({
    executablePath: executablePath,
    processName: serviceName,
    args: [], 
    spawnOptions: spawnOptions,
    onClientStatusError: (ready, message) => { // This is for gRPC client status
        if (grpcManagerRef) grpcManagerRef.sendClientStatusToRenderer(`${serviceKey}-grpc-client-status`, ready, message);
        if (!ready) {
            const isCriticalError = message.toLowerCase().includes("error") || message.toLowerCase().includes("failed") || message.toLowerCase().includes("not found");
            const statusText = isCriticalError ? `Error: ${message}` : `Failed/Stopped: ${message}`;
            updateServiceStatus(serviceKey, statusText, 'bg-red-700');
            if (onSpecificStatus && isCriticalError) {
                onSpecificStatus('error', true);
            }
        }
    },
  });

  if (processInstance) {
    backendProcesses[serviceKey] = processInstance;
    windowManagerRef.logToRenderer(`[${serviceName}] Process assigned (PID: ${processInstance.pid})`);
    updateServiceStatus(serviceKey, 'Process Started', 'bg-blue-500');

    // Check if stdout is available before attaching listeners (it won't be for Python due to file redirection)
    if (processInstance.stdout) {
      processInstance.stdout.on('data', (data) => {
        const message = data.toString();
        if (onSpecificLog) onSpecificLog('stdout', message.trim()); // For Java
        // This readyMessageDetector is crucial for non-Python backends
        if (readyMessageDetector(message)) {
          if (!serverReadyNotified) {
            windowManagerRef.logToRenderer(`[${serviceName}] Detected ready message.`);
            if (grpcManagerRef) grpcManagerRef.sendClientStatusToRenderer(`${serviceKey}-grpc-client-status`, true, `${serviceName} server reported ready.`);
            updateServiceStatus(serviceKey, 'Running', 'bg-green-500');
            if (onSpecificStatus) onSpecificStatus('running', true); // For Java
            serverReadyNotified = true;
          }
        }
      });
    } else {
      windowManagerRef.logToRenderer(`[${serviceName}] stdout is not available for .on('data') in startGenericBackend (likely redirected). Relying on file logs for Python.`);
      // For Python, ready message detection might need to come from polling the log file or a different mechanism
      // if direct stdout/stderr piping is bypassed. For now, we assume the file logs will give us the info.
      // If Python is the only one redirected, this 'else' primarily affects Python.
      // We still need a way to know if Python is "ready". The current readyMessageDetector won't work for it here.
      // This is a known limitation of the current stdout/stderr redirection for Python if ready messages are ONLY on stdout.
      // However, the primary goal now is to get Python's own error logs.
    }

    // Check if stderr is available before attaching listeners
    if (processInstance.stderr) {
      if (onSpecificLog) { // This was primarily for Java's specific log channel
          processInstance.stderr.on('data', (data) => {
              const message = data.toString().trim();
              onSpecificLog('stderr', `ERROR: ${message}`);
          });
      }
    } else {
        windowManagerRef.logToRenderer(`[${serviceName}] stderr is not available for .on('data') in startGenericBackend (likely redirected).`);
    }

  } else {
    windowManagerRef.logToRenderer(`[${serviceName}] Failed to start process.`);
    updateServiceStatus(serviceKey, 'Start Failed', 'bg-red-500');
    if (grpcManagerRef) grpcManagerRef.sendClientStatusToRenderer(`${serviceKey}-grpc-client-status`, false, `${serviceName} process failed to start.`);
    if (onSpecificStatus) onSpecificStatus('error', true);
  }
}

async function startPythonBackend() {
  let address = 'localhost:50051'; // Default fallback
  if (grpcManagerRef && typeof grpcManagerRef.getServiceConfig === 'function') {
    const pyGreeterConfig = grpcManagerRef.getServiceConfig('greeterServicePython');
    // Alternatively, could use 'subtitlerService' if that's the primary service for this backend
    if (pyGreeterConfig && pyGreeterConfig.serverUrl) {
      address = pyGreeterConfig.serverUrl;
    } else {
      windowManagerRef.logToRenderer('[BackendManager] Could not get Python service address from grpcManager. Using default.');
    }
  }
  await startGenericBackend({
    serviceName: 'Python Backend',
    serviceKey: 'python',
    address: address,
    executablePath: pythonBackendPath,
    readyMessageDetector: (message) => message.includes('Python gRPC server started and listening on port') || message.includes('Python gRPC server listening at'),
    spawnOptions: { shell: global.process.platform === 'win32' },
  });
}

async function startGoBackend() {
  let address = 'localhost:50052'; // Default fallback
  if (grpcManagerRef && typeof grpcManagerRef.getServiceConfig === 'function') {
    const goConfig = grpcManagerRef.getServiceConfig('greeterServiceGo');
    if (goConfig && goConfig.serverUrl) {
      address = goConfig.serverUrl;
    } else {
      windowManagerRef.logToRenderer('[BackendManager] Could not get Go service address from grpcManager. Using default.');
    }
  }
  await startGenericBackend({
    serviceName: 'Go Backend',
    serviceKey: 'go',
    address: address,
    executablePath: goBackendPath,
    readyMessageDetector: (message) =>
      message.includes('Go gRPC server successfully started and listening at') ||
      message.includes('Go gRPC server listening at'),
  });
}

async function startJavaBackend() {
  let address = 'localhost:50053'; // Default fallback
  if (grpcManagerRef && typeof grpcManagerRef.getServiceConfig === 'function') {
    const javaConfig = grpcManagerRef.getServiceConfig('greeterServiceJava');
    if (javaConfig && javaConfig.serverUrl) {
      address = javaConfig.serverUrl;
    } else {
      windowManagerRef.logToRenderer('[BackendManager] Could not get Java service address from grpcManager. Using default.');
    }
  }
  await startGenericBackend({
    serviceName: 'Java Backend',
    serviceKey: 'java',
    address: address,
    executablePath: javaBackendPath,
    readyMessageDetector: (message) => {
      const readyMessages = ["Started JavaGrpcBackendApplication", "Server started, listening on", "NettyServer started on port", "Tomcat started on port(s)"];
      return readyMessages.some(readyMsg => message.includes(readyMsg));
    },
    onSpecificLog: (logType, chunk) => {
      if (windowManagerRef.getMainWindow() && windowManagerRef.getMainWindow().webContents) {
        windowManagerRef.getMainWindow().webContents.send('java-log', chunk);
      }
    },
    onSpecificStatus: (statusKey, statusValue) => {
      let javaStatus = 'unknown';
      if (statusKey === 'starting-process' && statusValue) javaStatus = 'starting-process';
      else if (statusKey === 'running' && statusValue) javaStatus = 'running';
      else if (statusKey === 'error' && statusValue) javaStatus = 'error';
      // 'stopped' is handled by the 'close' event in startBackendProcess
      if (windowManagerRef.getMainWindow() && windowManagerRef.getMainWindow().webContents) {
        windowManagerRef.getMainWindow().webContents.send('java-status', javaStatus);
      }
    }
  });
}

function killAllBackendProcesses() {
    windowManagerRef.logToRenderer('[BackendManager] Attempting to terminate all backend services...');
    Object.entries(backendProcesses).forEach(([key, proc]) => {
        if (proc && !proc.killed) {
            windowManagerRef.logToRenderer(`[BackendManager] Terminating ${key} backend process PID: ${proc.pid}`);
            proc.kill('SIGTERM');
        }
    });
}


module.exports = {
  initializeBackendManager,
  startPythonBackend,
  startGoBackend,
  startJavaBackend,
  killAllBackendProcesses,
  getBackendProcesses, // For app-lifecycle to check on quit
  updateServiceStatus, // May be needed by grpc-manager if client init fails
};