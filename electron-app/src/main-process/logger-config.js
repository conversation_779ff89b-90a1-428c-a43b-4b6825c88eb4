/**
 * Electron主进程智能日志配置
 * 
 * 功能：
 * - 开发环境：统一显示所有日志，分类文件输出
 * - 生产环境：静默模式，最小化文件输出
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');

// 日志级别定义
const LOG_LEVELS = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    SILENT: 4
};

class ElectronLogger {
    constructor() {
        this.isDevelopment = process.defaultApp || process.env.NODE_ENV === 'development';
        this.currentLevel = this.isDevelopment ? LOG_LEVELS.DEBUG : LOG_LEVELS.WARN;
        this.logDir = null;
        this.initializeLogDirectory();
    }

    initializeLogDirectory() {
        if (this.isDevelopment) {
            // 开发环境：项目目录
            const projectRoot = path.join(__dirname, '..', '..', '..');
            this.logDir = path.join(projectRoot, 'logs', 'electron');
        } else {
            // 生产环境：系统目录
            const userDataPath = app.getPath('userData');
            this.logDir = path.join(userDataPath, 'logs');
        }

        // 确保日志目录存在
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    shouldLog(level) {
        return LOG_LEVELS[level] >= this.currentLevel;
    }

    formatMessage(level, category, message) {
        const timestamp = new Date().toISOString().substring(11, 23); // HH:mm:ss.SSS
        const prefix = this.isDevelopment ? 
            `[${level}] ${timestamp} [${category}]` : 
            `${timestamp} [${level}] [${category}]`;
        return `${prefix} ${message}`;
    }

    writeToFile(level, message) {
        if (!this.shouldLog(level)) return;

        try {
            const logFile = path.join(this.logDir, 'electron_main.log');
            const formattedMessage = this.formatMessage(level, 'MAIN', message);
            fs.appendFileSync(logFile, formattedMessage + '\n');
        } catch (error) {
            // 忽略文件写入错误
        }
    }

    log(level, category, message, options = {}) {
        if (!this.shouldLog(level)) return;

        const formattedMessage = this.formatMessage(level, category, message);
        
        // 控制台输出（开发环境或错误级别）
        if (this.isDevelopment || LOG_LEVELS[level] >= LOG_LEVELS.ERROR) {
            if (options.skipConsole !== true) {
                console.log(formattedMessage);
            }
        }

        // 文件输出
        this.writeToFile(level, message);

        // 发送到渲染进程（如果需要）
        if (options.sendToRenderer && options.mainWindow) {
            this.sendToRenderer(options.mainWindow, formattedMessage);
        }
    }

    sendToRenderer(mainWindow, message) {
        if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
            if (!mainWindow.webContents.isLoading()) {
                mainWindow.webContents.send('main-process-log', message);
            }
        }
    }

    // 便捷方法
    debug(category, message, options) {
        this.log('DEBUG', category, message, options);
    }

    info(category, message, options) {
        this.log('INFO', category, message, options);
    }

    warn(category, message, options) {
        this.log('WARN', category, message, options);
    }

    error(category, message, options) {
        this.log('ERROR', category, message, options);
    }

    // 创建分类日志器
    createCategoryLogger(category) {
        return {
            debug: (message, options) => this.debug(category, message, options),
            info: (message, options) => this.info(category, message, options),
            warn: (message, options) => this.warn(category, message, options),
            error: (message, options) => this.error(category, message, options)
        };
    }
}

// 全局实例
const logger = new ElectronLogger();

// 创建分类日志器
const loggers = {
    backend: logger.createCategoryLogger('Backend'),
    grpc: logger.createCategoryLogger('gRPC'),
    window: logger.createCategoryLogger('Window'),
    ipc: logger.createCategoryLogger('IPC'),
    app: logger.createCategoryLogger('App')
};

// 兼容旧的logToRenderer函数
function logToRenderer(win, message) {
    // 解析消息类别
    let category = 'General';
    let level = 'INFO';
    
    if (message.includes('[BackendManager]')) category = 'Backend';
    else if (message.includes('[GrpcClient]') || message.includes('[gRPC')) category = 'gRPC';
    else if (message.includes('[WindowManager]')) category = 'Window';
    else if (message.includes('[IPC')) category = 'IPC';
    else if (message.includes('[AppLifecycle]')) category = 'App';
    
    if (message.includes('ERROR') || message.includes('Error')) level = 'ERROR';
    else if (message.includes('WARN') || message.includes('Warning')) level = 'WARN';
    else if (message.includes('DEBUG') || message.includes('Debug')) level = 'DEBUG';

    // 清理消息，移除重复的前缀
    const cleanMessage = message
        .replace(/^\[.*?\]\s*/, '')  // 移除前缀标签
        .replace(/^(ERROR|WARN|INFO|DEBUG):\s*/i, '');  // 移除级别前缀

    logger.log(level, category, cleanMessage, { 
        sendToRenderer: true, 
        mainWindow: win 
    });
}

module.exports = {
    logger,
    loggers,
    logToRenderer,
    LOG_LEVELS
}; 