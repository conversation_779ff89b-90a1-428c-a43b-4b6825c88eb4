const Store = require('electron-store');
const { safeStorage, ipcMain } = require('electron');
const { getAIConfigServiceClient } = require('./grpc-manager'); 
const { logToRenderer } = require('./window-manager'); 

const store = new Store(); 
const CONFIG_STORE_KEY = 'aiServiceConfigs';

/**
 * Loads AI service configurations from electron-store.
 * Sensitive credential values are decrypted using safeStorage.
 */
async function loadAIConfigurations() {
  const storedConfigs = store.get(CONFIG_STORE_KEY, []);
  const decryptedConfigs = [];

  if (!safeStorage.isEncryptionAvailable()) {
    logToRenderer('[AI Config Handler] Warning: safeStorage encryption is not available. Credentials will be stored and handled in plaintext if not already encrypted.', 'warn');
  }

  for (const config of storedConfigs) {
    const decryptedCredentials = {};
    if (config.credentials) {
      for (const key in config.credentials) {
        try {
          if (safeStorage.isEncryptionAvailable() && typeof config.credentials[key] === 'string' && config.credentials[key].length > 0) {
            const buffer = Buffer.from(config.credentials[key], 'latin1');
            decryptedCredentials[key] = safeStorage.decryptString(buffer);
          } else {
            decryptedCredentials[key] = config.credentials[key]; 
          }
        } catch (error) {
          logToRenderer(`[AI Config Handler] Failed to decrypt credential '${key}' for ${config.provider_id}. Storing as is. Error: ${error.message}`, 'error');
          decryptedCredentials[key] = config.credentials[key]; 
        }
      }
    }
    decryptedConfigs.push({ ...config, credentials: decryptedCredentials });
  }
  logToRenderer(`[AI Config Handler] Loaded ${decryptedConfigs.length} AI configurations.`);
  return decryptedConfigs;
}

/**
 * Saves AI service configurations to electron-store and pushes to backend.
 * Sensitive credential values are encrypted using safeStorage before storing locally.
 * Plaintext (decrypted) credentials are sent to the backend via gRPC.
 * @param {Array<Object>} configsToSave - Array of configuration objects from renderer.
 */
async function saveAIConfigurations(configsToSave) {
  const encryptedConfigsForStore = [];
  const configsForBackend = []; 

  if (!safeStorage.isEncryptionAvailable()) {
    logToRenderer('[AI Config Handler] Warning: safeStorage encryption is not available. Credentials will be stored in plaintext locally.', 'warn');
  }

  for (const config of configsToSave) {
    const encryptedCredentials = {};
    const plaintextCredentialsForBackend = {};

    if (config.credentials) {
      for (const key in config.credentials) {
        const plainValue = config.credentials[key];
        plaintextCredentialsForBackend[key] = plainValue; 
        try {
          if (safeStorage.isEncryptionAvailable() && typeof plainValue === 'string' && plainValue.length > 0) {
            encryptedCredentials[key] = safeStorage.encryptString(plainValue).toString('latin1');
          } else {
            encryptedCredentials[key] = plainValue; 
          }
        } catch (error) {
          logToRenderer(`[AI Config Handler] Failed to encrypt credential '${key}' for ${config.provider_id}. Storing as plaintext. Error: ${error.message}`, 'error');
          encryptedCredentials[key] = plainValue;
        }
      }
    }
    encryptedConfigsForStore.push({ ...config, credentials: encryptedCredentials });
    configsForBackend.push({ 
      ...config, 
      credentials: plaintextCredentialsForBackend,
      attributes: config.attributes || {}, 
      metadata: config.metadata || {}   
    });
  }

  store.set(CONFIG_STORE_KEY, encryptedConfigsForStore);
  logToRenderer(`[AI Config Handler] Saved ${encryptedConfigsForStore.length} AI configurations to electron-store.`);

  return await pushConfigsToBackendInternal(configsForBackend, 'saveAIConfigurations');
}

/**
 * Internal helper to push configurations to the backend.
 * @param {Array<Object>} configsWithPlaintextCredentials - Configs with credentials ready for backend.
 * @param {string} initiator - Name of the calling function for logging.
 * @param {number} retryCount - Current retry attempt count.
 */
async function pushConfigsToBackendInternal(configsWithPlaintextCredentials, initiator = 'push', retryCount = 0) {
  const maxRetries = 3;
  const retryDelay = 2000; // 2 seconds

  const client = getAIConfigServiceClient();
  if (!client) {
    if (retryCount < maxRetries) {
      logToRenderer(`[AI Config Handler] (${initiator}) AIConfigService client not available, retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})...`, 'warn');
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      return await pushConfigsToBackendInternal(configsWithPlaintextCredentials, initiator, retryCount + 1);
    } else {
      logToRenderer(`[AI Config Handler] (${initiator}) AIConfigService client not available after ${maxRetries} retries. Cannot push to backend.`, 'error');
      return { success: false, message: 'AIConfigService client not available after retries.' };
    }
  }

  logToRenderer(`[AI Config Handler] (${initiator}) AIConfigService client acquired. Checking methods... Client keys: ${Object.keys(client).join(', ')}`, 'info');
  if (typeof client.updateAIConfigurations === 'function') {
    logToRenderer(`[AI Config Handler] (${initiator}) client.updateAIConfigurations is a function.`, 'info');
  } else {
    logToRenderer(`[AI Config Handler] (${initiator}) client.updateAIConfigurations is NOT a function. Type: ${typeof client.updateAIConfigurations}`, 'warn');
    if (client.UpdateAIConfigurations) {
      logToRenderer(`[AI Config Handler] (${initiator}) Found client.UpdateAIConfigurations (uppercase) instead.`, 'warn');
    }
  }
    const protoConfigs = configsWithPlaintextCredentials.map(c => ({
        provider_id: c.provider_id,
        provider_type: c.provider_type,
        display_name: c.display_name,
        is_enabled: c.is_enabled,
        credentials: c.credentials,
        attributes: c.attributes,
        metadata: c.metadata
    }));
    const request = { configs: protoConfigs };
    
    logToRenderer(`[AI Config Handler] (${initiator}) Pushing ${protoConfigs.length} configurations to backend...`);
    try {
      const response = await new Promise((resolve, reject) => {
        let rpcMethod;
        if (client && typeof client.updateAIConfigurations === 'function') {
          rpcMethod = client.updateAIConfigurations.bind(client);
          logToRenderer(`[AI Config Handler] (${initiator}) Using client.updateAIConfigurations (lowercase).`, 'info');
        } else if (client && typeof client.UpdateAIConfigurations === 'function') {
          rpcMethod = client.UpdateAIConfigurations.bind(client);
          logToRenderer(`[AI Config Handler] (${initiator}) Using client.UpdateAIConfigurations (uppercase).`, 'info');
        }

        if (rpcMethod) {
            rpcMethod(request, (err, resp) => {
              if (err) reject(err); else resolve(resp);
            });
        } else {
            logToRenderer(`[AI Config Handler] (${initiator}) Neither updateAIConfigurations nor UpdateAIConfigurations found on client. Client keys: ${client ? Object.keys(client).join(', ') : 'client is null/undefined'}`, 'error');
            reject(new Error('gRPC client or method not available for AIConfigurationService.'));
        }
      });
      logToRenderer(`[AI Config Handler] (${initiator}) Backend response: ${response.message}, Success: ${response.success}`);
      return { success: response.success, message: response.message };
    } catch (error) {
      logToRenderer(`[AI Config Handler] (${initiator}) Error pushing configurations to backend: ${error.message}`, 'error');

      // Retry on gRPC connection errors
      if (retryCount < maxRetries && (error.message.includes('UNAVAILABLE') || error.message.includes('DEADLINE_EXCEEDED') || error.message.includes('connection'))) {
        logToRenderer(`[AI Config Handler] (${initiator}) Retrying due to connection error in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})...`, 'warn');
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return await pushConfigsToBackendInternal(configsWithPlaintextCredentials, initiator, retryCount + 1);
      }

      return { success: false, message: `Failed to push to backend: ${error.message}` };
    }
}

/**
 * Loads all configurations and pushes them to the backend.
 * Intended for use on app startup after gRPC client is ready.
 */
async function pushInitialConfigsToBackend() {
  logToRenderer('[AI Config Handler] Attempting to push initial configurations to backend...');
  const configs = await loadAIConfigurations(); // Credentials are now plaintext

  if (!configs || configs.length === 0) {
    logToRenderer('[AI Config Handler] No initial configurations to push.');
    return { success: true, message: 'No initial configurations to push.' };
  }
  
  // configs already have plaintext credentials from loadAIConfigurations
  return await pushConfigsToBackendInternal(configs, 'pushInitialConfigsToBackend');
}


/**
 * Check if the AI Config Service is available
 */
async function checkAIConfigServiceAvailability() {
  const client = getAIConfigServiceClient();
  if (!client) {
    return { available: false, message: 'gRPC client not initialized' };
  }

  // Check if the required method exists
  const hasMethod = typeof client.updateAIConfigurations === 'function' || typeof client.UpdateAIConfigurations === 'function';
  if (!hasMethod) {
    return { available: false, message: 'Required gRPC method not available' };
  }

  return { available: true, message: 'AI Config Service is ready' };
}

/**
 * Helper function to make HTTP requests
 */
function makeHttpRequest(requestUrl, options, body) {
  return new Promise((resolve, reject) => {
    const url = require('url');
    const https = require('https');
    const http = require('http');

    const parsedUrl = url.parse(requestUrl);
    const isHttps = parsedUrl.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 30000 // 30 second timeout
    };

    const req = client.request(requestOptions, (res) => {
      let responseBody = '';

      res.on('data', (chunk) => {
        responseBody += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseBody
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (body) {
      req.write(body);
    }

    req.end();
  });
}

/**
 * Load available models from AI service
 */
async function loadAIModels(loadConfig) {
  try {
    logToRenderer(`[AI Config Handler] Loading models for ${loadConfig.provider_type} service...`, 'info');

    // Validate required fields
    if (!loadConfig.provider_type || !loadConfig.credentials) {
      return {
        success: false,
        message: '缺少必需的配置信息',
        models: []
      };
    }

    let apiKey = loadConfig.credentials.api_key;

    // For Gemini, if no API key is provided, use a test key to get model list
    if (loadConfig.provider_type === 'Gemini' && (!apiKey || apiKey.length < 10)) {
      apiKey = 'AIzaSyBMBrRcLktxJVEwYKmLgJyVmq6wv26aC2A'; // Test key for model listing
      logToRenderer('[AI Config Handler] Using test API key for Gemini model listing', 'info');
    } else if (!apiKey || apiKey.length < 10) {
      return {
        success: false,
        message: 'API Key 无效',
        models: []
      };
    }

    let requestUrl, requestOptions;

    switch (loadConfig.provider_type) {
      case 'OpenAI':
        const baseUrl = loadConfig.attributes?.api_base_url || 'https://api.openai.com/v1';
        requestUrl = `${baseUrl}/models`;
        requestOptions = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'User-Agent': 'ElectronApp/1.0'
          }
        };
        break;

      case 'DeepSeek':
        const deepseekBaseUrl = loadConfig.attributes?.api_base_url || 'https://api.deepseek.com';
        requestUrl = `${deepseekBaseUrl}/models`;
        requestOptions = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'User-Agent': 'ElectronApp/1.0'
          }
        };
        break;

      case 'Gemini':
        requestUrl = `https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`;
        requestOptions = {
          method: 'GET',
          headers: {
            'User-Agent': 'ElectronApp/1.0'
          }
        };
        break;

      case 'VolcEngine':
        // 火山大模型可能不支持直接获取模型列表，使用硬编码的模型列表
        logToRenderer('[AI Config Handler] Using hardcoded model list for VolcEngine', 'info');
        return {
          success: true,
          message: '成功获取火山大模型列表',
          models: [
            { id: 'deepseek-v3-250324', owned_by: 'volcengine', displayName: 'DeepSeek V3', description: '火山引擎 DeepSeek V3 模型' },
            { id: 'moonshot-v1-128k', owned_by: 'volcengine', displayName: 'Moonshot V1 (128k)', description: '火山引擎 Moonshot V1 模型' },
            { id: 'qwen-v2-72b', owned_by: 'volcengine', displayName: 'Qwen V2 (72B)', description: '火山引擎 Qwen V2 72B 模型' },
            { id: 'qwen-v2-7b', owned_by: 'volcengine', displayName: 'Qwen V2 (7B)', description: '火山引擎 Qwen V2 7B 模型' }
          ]
        };
        break;

      default:
        return {
          success: false,
          message: `不支持的服务类型: ${loadConfig.provider_type}`,
          models: []
        };
    }

    // Make HTTP request to get models
    const response = await makeHttpRequest(requestUrl, requestOptions);

    if (response.statusCode >= 200 && response.statusCode < 300) {
      const responseData = JSON.parse(response.body);

      // Parse models based on provider
      let models = [];

      if (loadConfig.provider_type === 'Gemini') {
        // Gemini API returns models in a different format
        if (responseData.models && Array.isArray(responseData.models)) {
          models = responseData.models
            .filter(model => {
              // Filter out models that are not suitable for generateContent
              return model.supportedGenerationMethods &&
                     model.supportedGenerationMethods.includes('generateContent');
            })
            .map(model => ({
              id: model.name.replace('models/', ''), // Remove 'models/' prefix
              owned_by: 'google',
              created: null,
              object: 'model',
              description: model.description || '',
              displayName: model.displayName || model.name
            }));
        }
      } else {
        // OpenAI/DeepSeek format
        if (responseData.data && Array.isArray(responseData.data)) {
          models = responseData.data.map(model => ({
            id: model.id,
            owned_by: model.owned_by || loadConfig.provider_type.toLowerCase(),
            created: model.created,
            object: model.object
          }));
        }
      }

      // Sort models by name for better UX
      models.sort((a, b) => a.id.localeCompare(b.id));

      logToRenderer(`[AI Config Handler] Successfully loaded ${models.length} models for ${loadConfig.provider_type}`, 'info');

      return {
        success: true,
        message: `成功获取 ${models.length} 个模型`,
        models: models
      };
    } else {
      return {
        success: false,
        message: `API 返回错误状态码: ${response.statusCode}`,
        models: []
      };
    }

  } catch (error) {
    logToRenderer(`[AI Config Handler] Error loading AI models: ${error.message}`, 'error');
    return {
      success: false,
      message: `获取模型列表失败: ${error.message}`,
      models: []
    };
  }
}

/**
 * Test AI service connection
 */
async function testAIServiceConnection(testConfig) {
  try {
    logToRenderer(`[AI Config Handler] Testing connection for ${testConfig.provider_type} service...`, 'info');

    // Validate required fields
    if (!testConfig.provider_type || !testConfig.credentials) {
      return {
        success: false,
        message: '缺少必需的配置信息',
        details: 'Provider type and credentials are required'
      };
    }

    // Create a simple test request based on provider type
    let testRequest = {};
    let expectedResponse = {};

    switch (testConfig.provider_type) {
      case 'OpenAI':
        testRequest = {
          model: testConfig.attributes?.default_model || 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: 'Hello, this is a connection test.' }],
          max_tokens: 10,
          temperature: 0.1
        };
        break;
      case 'DeepSeek':
        testRequest = {
          model: testConfig.attributes?.default_model || 'deepseek-chat',
          messages: [{ role: 'user', content: 'Hello, this is a connection test.' }],
          max_tokens: 10,
          temperature: 0.1
        };
        break;
      case 'Gemini':
        testRequest = {
          model: testConfig.attributes?.default_model || 'gemini-1.5-pro-latest',
          messages: [{ role: 'user', content: 'Hello, this is a connection test.' }],
          max_tokens: 10
        };
        break;
      case 'VolcEngine':
        testRequest = {
          model: testConfig.attributes?.default_model || 'deepseek-v3-250324',
          messages: [
            { role: 'system', content: '你是人工智能助手.' },
            { role: 'user', content: 'Hello, this is a connection test.' }
          ],
          temperature: 0.1
        };
        break;
      default:
        return {
          success: false,
          message: `不支持的服务类型: ${testConfig.provider_type}`,
          details: 'Unsupported provider type for testing'
        };
    }

    // Perform actual API test
    logToRenderer(`[AI Config Handler] Performing real API test for ${testConfig.provider_type}...`, 'info');

    const apiKey = testConfig.credentials.api_key;
    if (!apiKey || apiKey.length < 10) {
      return {
        success: false,
        message: 'API Key 格式无效',
        details: 'API key appears to be too short or missing'
      };
    }

    // Basic format validation for different providers
    let isValidFormat = false;
    let baseUrl = '';

    switch (testConfig.provider_type) {
      case 'OpenAI':
        isValidFormat = apiKey.startsWith('sk-') && apiKey.length > 20;
        baseUrl = testConfig.attributes?.api_base_url || 'https://api.openai.com/v1';
        break;
      case 'DeepSeek':
        isValidFormat = apiKey.startsWith('sk-') && apiKey.length > 20;
        baseUrl = testConfig.attributes?.api_base_url || 'https://api.deepseek.com';
        break;
      case 'Gemini':
        isValidFormat = apiKey.startsWith('AIza') && apiKey.length > 30;
        baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        break;
      case 'VolcEngine':
        isValidFormat = apiKey.length > 10;
        baseUrl = testConfig.attributes?.api_base_url || 'https://ark.cn-beijing.volces.com/api/v3';
        break;
      default:
        isValidFormat = apiKey.length > 10;
    }

    if (!isValidFormat) {
      return {
        success: false,
        message: `${testConfig.provider_type} API Key 格式不正确`,
        details: `Expected format for ${testConfig.provider_type} not met`
      };
    }

    // Perform actual HTTP request
    try {
      const https = require('https');
      const http = require('http');
      const url = require('url');

      let requestUrl, requestOptions, requestBody;

      switch (testConfig.provider_type) {
        case 'OpenAI':
        case 'DeepSeek':
          requestUrl = `${baseUrl}/chat/completions`;
          requestOptions = {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
              'User-Agent': 'ElectronApp/1.0'
            }
          };
          requestBody = JSON.stringify(testRequest);
          break;

        case 'Gemini':
          requestUrl = `${baseUrl}/models/${testRequest.model}:generateContent?key=${apiKey}`;
          requestOptions = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'ElectronApp/1.0'
            }
          };
          requestBody = JSON.stringify({
            contents: [{
              parts: [{ text: testRequest.messages[0].content }]
            }],
            generationConfig: {
              maxOutputTokens: testRequest.max_tokens
            }
          });
          break;
          
        case 'VolcEngine':
          requestUrl = `${baseUrl}/chat/completions`;
          requestOptions = {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
              'User-Agent': 'ElectronApp/1.0'
            }
          };
          requestBody = JSON.stringify(testRequest);
          break;
      }

      const response = await makeHttpRequest(requestUrl, requestOptions, requestBody);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        const responseData = JSON.parse(response.body);
        return {
          success: true,
          message: `${testConfig.provider_type} API 连接测试成功！服务响应正常。`,
          details: `Status: ${response.statusCode}\nResponse: ${JSON.stringify(responseData, null, 2)}`
        };
      } else {
        return {
          success: false,
          message: `${testConfig.provider_type} API 返回错误状态码: ${response.statusCode}`,
          details: `Status: ${response.statusCode}\nResponse: ${response.body}`
        };
      }

    } catch (httpError) {
      logToRenderer(`[AI Config Handler] HTTP request failed: ${httpError.message}`, 'error');
      return {
        success: false,
        message: `网络请求失败: ${httpError.message}`,
        details: httpError.stack || httpError.toString()
      };
    }

  } catch (error) {
    logToRenderer(`[AI Config Handler] Error testing AI service connection: ${error.message}`, 'error');
    return {
      success: false,
      message: '连接测试时发生错误',
      details: error.message
    };
  }
}

function setupAIConfigIPC() {
  ipcMain.handle('load-ai-configs', async () => {
    return await loadAIConfigurations();
  });

  ipcMain.handle('save-ai-configs', async (event, configs) => {
    return await saveAIConfigurations(configs);
  });

  ipcMain.handle('check-ai-config-service', async () => {
    return await checkAIConfigServiceAvailability();
  });

  ipcMain.handle('test-ai-service-connection', async (event, testConfig) => {
    return await testAIServiceConnection(testConfig);
  });

  ipcMain.handle('load-ai-models', async (event, loadConfig) => {
    return await loadAIModels(loadConfig);
  });

  // 清除AI缓存处理器
  ipcMain.handle('clear-ai-cache', async (event) => {
    try {
      logToRenderer('[AI Config Handler] 正在清除AI缓存...');
      
      // 这里可以调用后端的清除缓存服务
      // 暂时返回成功，实际实现需要与后端缓存系统集成
      
      // 模拟异步清除操作
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      logToRenderer('[AI Config Handler] AI缓存清除成功');
      return { success: true, message: 'AI缓存已清除' };
    } catch (error) {
      const errorMessage = `清除AI缓存失败: ${error.message}`;
      logToRenderer(`[AI Config Handler] ${errorMessage}`);
      return { success: false, message: errorMessage };
    }
  });

  // 广播AI配置更新事件
  ipcMain.handle('broadcast-ai-config-updated', async (event) => {
    const { getMainWindow } = require('./window-manager');
    const mainWindow = getMainWindow();
    
    if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
      // 向渲染进程发送AI配置更新事件
      mainWindow.webContents.send('ai-configs-updated');
      logToRenderer('[AI Config Handler] AI配置更新事件已广播到渲染进程');
    }
    
    return { success: true };
  });

  logToRenderer('[AI Config Handler] IPC handlers for AI configurations registered.');
}

module.exports = {
  setupAIConfigIPC,
  loadAIConfigurations, 
  saveAIConfigurations, 
  pushInitialConfigsToBackend, // Export the new function
};