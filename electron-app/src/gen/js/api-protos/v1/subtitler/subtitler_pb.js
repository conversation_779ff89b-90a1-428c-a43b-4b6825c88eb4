// source: api-protos/v1/subtitler/subtitler.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

var google_protobuf_struct_pb = require('google-protobuf/google/protobuf/struct_pb.js');
goog.object.extend(proto, google_protobuf_struct_pb);
var google_protobuf_any_pb = require('google-protobuf/google/protobuf/any_pb.js');
goog.object.extend(proto, google_protobuf_any_pb);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.AudioToTextRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.AudioToTextResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ClearCacheRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ClearCacheResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ErrorDetail', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.OperationStatus', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProgressUpdate', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.SubtitleSegment', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ErrorDetail, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ErrorDetail.displayName = 'proto.monkeyfx.api.v1.subtitler.ErrorDetail';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ProgressUpdate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ProgressUpdate.displayName = 'proto.monkeyfx.api.v1.subtitler.ProgressUpdate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.AudioToTextRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.AudioToTextRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.displayName = 'proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.repeatedFields_, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.AudioToTextResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.AudioToTextResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.displayName = 'proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.repeatedFields_, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.repeatedFields_, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.SubtitleSegment, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.SubtitleSegment.displayName = 'proto.monkeyfx.api.v1.subtitler.SubtitleSegment';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.repeatedFields_, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.repeatedFields_, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ClearCacheRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.ClearCacheRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ClearCacheResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.ClearCacheResponse';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ErrorDetail} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject = function(includeInstance, msg) {
  var f, obj = {
    errorCode: jspb.Message.getFieldWithDefault(msg, 1, ""),
    technicalMessage: jspb.Message.getFieldWithDefault(msg, 2, ""),
    userMessage: jspb.Message.getFieldWithDefault(msg, 3, ""),
    contextMap: (f = msg.getContextMap()) ? f.toObject(includeInstance, undefined) : []
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
  return proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ErrorDetail} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setErrorCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTechnicalMessage(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setUserMessage(value);
      break;
    case 4:
      var value = msg.getContextMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readString, null, "", "");
         });
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ErrorDetail} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getErrorCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTechnicalMessage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getUserMessage();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getContextMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(4, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeString);
  }
};


/**
 * optional string error_code = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.getErrorCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ErrorDetail} returns this
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.setErrorCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string technical_message = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.getTechnicalMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ErrorDetail} returns this
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.setTechnicalMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string user_message = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.getUserMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ErrorDetail} returns this
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.setUserMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * map<string, string> context = 4;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,string>}
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.getContextMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,string>} */ (
      jspb.Message.getMapField(this, 4, opt_noLazyCreate,
      null));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.ErrorDetail} returns this
 */
proto.monkeyfx.api.v1.subtitler.ErrorDetail.prototype.clearContextMap = function() {
  this.getContextMap().clear();
  return this;};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_ = [[10,11,12,13,14]];

/**
 * @enum {number}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase = {
  FINAL_RESULT_NOT_SET: 0,
  VIDEO_TO_AUDIO_RESPONSE: 10,
  AUDIO_TO_TEXT_RESPONSE: 11,
  GENERATE_SUBTITLES_RESPONSE: 12,
  TRANSLATE_SUBTITLES_RESPONSE: 13,
  PROCESS_VIDEO_TO_TRANSLATED_SUBTITLES_RESPONSE: 14
};

/**
 * @return {proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getFinalResultCase = function() {
  return /** @type {proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase} */(jspb.Message.computeOneofCase(this, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ProgressUpdate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.toObject = function(includeInstance, msg) {
  var f, obj = {
    traceId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    stageName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    percentage: jspb.Message.getFieldWithDefault(msg, 3, 0),
    message: jspb.Message.getFieldWithDefault(msg, 4, ""),
    status: jspb.Message.getFieldWithDefault(msg, 5, 0),
    errorDetail: (f = msg.getErrorDetail()) && proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(includeInstance, f),
    data: (f = msg.getData()) && google_protobuf_any_pb.Any.toObject(includeInstance, f),
    isError: jspb.Message.getBooleanFieldWithDefault(msg, 8, false),
    errorMessage: jspb.Message.getFieldWithDefault(msg, 9, ""),
    videoToAudioResponse: (f = msg.getVideoToAudioResponse()) && proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.toObject(includeInstance, f),
    audioToTextResponse: (f = msg.getAudioToTextResponse()) && proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.toObject(includeInstance, f),
    generateSubtitlesResponse: (f = msg.getGenerateSubtitlesResponse()) && proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.toObject(includeInstance, f),
    translateSubtitlesResponse: (f = msg.getTranslateSubtitlesResponse()) && proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.toObject(includeInstance, f),
    processVideoToTranslatedSubtitlesResponse: (f = msg.getProcessVideoToTranslatedSubtitlesResponse()) && proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ProgressUpdate;
  return proto.monkeyfx.api.v1.subtitler.ProgressUpdate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setStageName(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPercentage(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setMessage(value);
      break;
    case 5:
      var value = /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 6:
      var value = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader);
      msg.setErrorDetail(value);
      break;
    case 7:
      var value = new google_protobuf_any_pb.Any;
      reader.readMessage(value,google_protobuf_any_pb.Any.deserializeBinaryFromReader);
      msg.setData(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsError(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setErrorMessage(value);
      break;
    case 10:
      var value = new proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinaryFromReader);
      msg.setVideoToAudioResponse(value);
      break;
    case 11:
      var value = new proto.monkeyfx.api.v1.subtitler.AudioToTextResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinaryFromReader);
      msg.setAudioToTextResponse(value);
      break;
    case 12:
      var value = new proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinaryFromReader);
      msg.setGenerateSubtitlesResponse(value);
      break;
    case 13:
      var value = new proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinaryFromReader);
      msg.setTranslateSubtitlesResponse(value);
      break;
    case 14:
      var value = new proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinaryFromReader);
      msg.setProcessVideoToTranslatedSubtitlesResponse(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ProgressUpdate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getStageName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getPercentage();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getMessage();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getErrorDetail();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter
    );
  }
  f = message.getData();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      google_protobuf_any_pb.Any.serializeBinaryToWriter
    );
  }
  f = message.getIsError();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
  f = message.getErrorMessage();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getVideoToAudioResponse();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.serializeBinaryToWriter
    );
  }
  f = message.getAudioToTextResponse();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.serializeBinaryToWriter
    );
  }
  f = message.getGenerateSubtitlesResponse();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.serializeBinaryToWriter
    );
  }
  f = message.getTranslateSubtitlesResponse();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.serializeBinaryToWriter
    );
  }
  f = message.getProcessVideoToTranslatedSubtitlesResponse();
  if (f != null) {
    writer.writeMessage(
      14,
      f,
      proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.serializeBinaryToWriter
    );
  }
};


/**
 * optional string trace_id = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string stage_name = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getStageName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setStageName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 percentage = 3;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getPercentage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setPercentage = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string message = 4;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional OperationStatus status = 5;
 * @return {!proto.monkeyfx.api.v1.subtitler.OperationStatus}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getStatus = function() {
  return /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.OperationStatus} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional ErrorDetail error_detail = 6;
 * @return {?proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getErrorDetail = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ErrorDetail} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ErrorDetail, 6));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ErrorDetail|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setErrorDetail = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearErrorDetail = function() {
  return this.setErrorDetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasErrorDetail = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional google.protobuf.Any data = 7;
 * @return {?proto.google.protobuf.Any}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getData = function() {
  return /** @type{?proto.google.protobuf.Any} */ (
    jspb.Message.getWrapperField(this, google_protobuf_any_pb.Any, 7));
};


/**
 * @param {?proto.google.protobuf.Any|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setData = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearData = function() {
  return this.setData(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasData = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional bool is_error = 8;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getIsError = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setIsError = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};


/**
 * optional string error_message = 9;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getErrorMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setErrorMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional VideoToAudioResponse video_to_audio_response = 10;
 * @return {?proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getVideoToAudioResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse, 10));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setVideoToAudioResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 10, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearVideoToAudioResponse = function() {
  return this.setVideoToAudioResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasVideoToAudioResponse = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional AudioToTextResponse audio_to_text_response = 11;
 * @return {?proto.monkeyfx.api.v1.subtitler.AudioToTextResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getAudioToTextResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.AudioToTextResponse, 11));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.AudioToTextResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setAudioToTextResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 11, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearAudioToTextResponse = function() {
  return this.setAudioToTextResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasAudioToTextResponse = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional GenerateSubtitlesResponse generate_subtitles_response = 12;
 * @return {?proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getGenerateSubtitlesResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse, 12));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setGenerateSubtitlesResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 12, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearGenerateSubtitlesResponse = function() {
  return this.setGenerateSubtitlesResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasGenerateSubtitlesResponse = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional TranslateSubtitlesResponse translate_subtitles_response = 13;
 * @return {?proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getTranslateSubtitlesResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse, 13));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setTranslateSubtitlesResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 13, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearTranslateSubtitlesResponse = function() {
  return this.setTranslateSubtitlesResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasTranslateSubtitlesResponse = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional ProcessVideoToTranslatedSubtitlesResponse process_video_to_translated_subtitles_response = 14;
 * @return {?proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getProcessVideoToTranslatedSubtitlesResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse, 14));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setProcessVideoToTranslatedSubtitlesResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 14, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearProcessVideoToTranslatedSubtitlesResponse = function() {
  return this.setProcessVideoToTranslatedSubtitlesResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasProcessVideoToTranslatedSubtitlesResponse = function() {
  return jspb.Message.getField(this, 14) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    videoPath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest;
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setVideoPath(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVideoPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string video_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.getVideoPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.setVideoPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string trace_id = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    audioPath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    audioData: msg.getAudioData_asB64(),
    traceId: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse;
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setAudioPath(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setAudioData(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAudioPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAudioData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string audio_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.setAudioPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bytes audio_data = 2;
 * @return {!(string|Uint8Array)}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes audio_data = 2;
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getAudioData()));
};


/**
 * optional bytes audio_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getAudioData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.setAudioData = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional string trace_id = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    audioPath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    audioData: msg.getAudioData_asB64(),
    requestWordTimestamps: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
    skipCache: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
    traceId: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.AudioToTextRequest;
  return proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setAudioPath(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setAudioData(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRequestWordTimestamps(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSkipCache(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAudioPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAudioData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getRequestWordTimestamps();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getSkipCache();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional string audio_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.setAudioPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bytes audio_data = 2;
 * @return {!(string|Uint8Array)}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes audio_data = 2;
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getAudioData()));
};


/**
 * optional bytes audio_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getAudioData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.setAudioData = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional bool request_word_timestamps = 3;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getRequestWordTimestamps = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.setRequestWordTimestamps = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional bool skip_cache = 4;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getSkipCache = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.setSkipCache = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional string trace_id = 5;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.toObject = function(includeInstance, msg) {
  var f, obj = {
    text: jspb.Message.getFieldWithDefault(msg, 1, ""),
    startTimeMs: jspb.Message.getFieldWithDefault(msg, 2, 0),
    endTimeMs: jspb.Message.getFieldWithDefault(msg, 3, 0),
    status: jspb.Message.getFieldWithDefault(msg, 4, 0),
    errorDetail: (f = msg.getErrorDetail()) && proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment;
  return proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setText(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartTimeMs(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setEndTimeMs(value);
      break;
    case 4:
      var value = /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 5:
      var value = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader);
      msg.setErrorDetail(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getText();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getStartTimeMs();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getEndTimeMs();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getErrorDetail();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter
    );
  }
};


/**
 * optional string text = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.getText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.setText = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int64 start_time_ms = 2;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.getStartTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.setStartTimeMs = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int64 end_time_ms = 3;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.getEndTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.setEndTimeMs = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional OperationStatus status = 4;
 * @return {!proto.monkeyfx.api.v1.subtitler.OperationStatus}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.getStatus = function() {
  return /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.OperationStatus} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional ErrorDetail error_detail = 5;
 * @return {?proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.getErrorDetail = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ErrorDetail} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ErrorDetail, 5));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ErrorDetail|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} returns this
*/
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.setErrorDetail = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.clearErrorDetail = function() {
  return this.setErrorDetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.prototype.hasErrorDetail = function() {
  return jspb.Message.getField(this, 5) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    segmentsList: jspb.Message.toObjectList(msg.getSegmentsList(),
    proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.toObject, includeInstance),
    traceId: jspb.Message.getFieldWithDefault(msg, 2, ""),
    totalSegmentsProcessed: jspb.Message.getFieldWithDefault(msg, 3, 0),
    successfulSegments: jspb.Message.getFieldWithDefault(msg, 4, 0),
    failedSegments: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.AudioToTextResponse;
  return proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.deserializeBinaryFromReader);
      msg.addSegments(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTotalSegmentsProcessed(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSuccessfulSegments(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setFailedSegments(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSegmentsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment.serializeBinaryToWriter
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTotalSegmentsProcessed();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getSuccessfulSegments();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getFailedSegments();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
};


/**
 * repeated TimestampedTextSegment segments = 1;
 * @return {!Array<!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment>}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.getSegmentsList = function() {
  return /** @type{!Array<!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment, 1));
};


/**
 * @param {!Array<!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} returns this
*/
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.setSegmentsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment=} opt_value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.addSegments = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.monkeyfx.api.v1.subtitler.TimestampedTextSegment, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.clearSegmentsList = function() {
  return this.setSegmentsList([]);
};


/**
 * optional string trace_id = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 total_segments_processed = 3;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.getTotalSegmentsProcessed = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.setTotalSegmentsProcessed = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 successful_segments = 4;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.getSuccessfulSegments = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.setSuccessfulSegments = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 failed_segments = 5;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.getFailedSegments = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.setFailedSegments = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    text: jspb.Message.getFieldWithDefault(msg, 1, ""),
    audioPath: jspb.Message.getFieldWithDefault(msg, 2, ""),
    skipCache: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
    traceId: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest;
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setText(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAudioPath(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSkipCache(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getText();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAudioPath();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSkipCache();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string text = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.getText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.setText = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string audio_path = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.getAudioPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.setAudioPath = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional bool skip_cache = 3;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.getSkipCache = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.setSkipCache = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional string trace_id = 4;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    srtContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    assContent: jspb.Message.getFieldWithDefault(msg, 2, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse;
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrtContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAssContent(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSrtContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAssContent();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string srt_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.getSrtContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.setSrtContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string ass_content = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.getAssContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.setAssContent = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string trace_id = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    subtitleContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    targetLanguage: jspb.Message.getFieldWithDefault(msg, 2, ""),
    skipCache: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
    traceId: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest;
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubtitleContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTargetLanguage(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSkipCache(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSubtitleContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTargetLanguage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSkipCache();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string subtitle_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.getSubtitleContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.setSubtitleContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string target_language = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.getTargetLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.setTargetLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional bool skip_cache = 3;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.getSkipCache = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.setSkipCache = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional string trace_id = 4;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    segmentId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    originalText: jspb.Message.getFieldWithDefault(msg, 2, ""),
    translatedText: jspb.Message.getFieldWithDefault(msg, 3, ""),
    status: jspb.Message.getFieldWithDefault(msg, 4, 0),
    errorDetail: (f = msg.getErrorDetail()) && proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult;
  return proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSegmentId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setOriginalText(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedText(value);
      break;
    case 4:
      var value = /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 5:
      var value = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader);
      msg.setErrorDetail(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSegmentId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getOriginalText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTranslatedText();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getErrorDetail();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter
    );
  }
};


/**
 * optional string segment_id = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.getSegmentId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.setSegmentId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string original_text = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.getOriginalText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.setOriginalText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string translated_text = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.getTranslatedText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.setTranslatedText = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional OperationStatus status = 4;
 * @return {!proto.monkeyfx.api.v1.subtitler.OperationStatus}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.getStatus = function() {
  return /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.OperationStatus} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional ErrorDetail error_detail = 5;
 * @return {?proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.getErrorDetail = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ErrorDetail} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ErrorDetail, 5));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ErrorDetail|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} returns this
*/
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.setErrorDetail = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.clearErrorDetail = function() {
  return this.setErrorDetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.prototype.hasErrorDetail = function() {
  return jspb.Message.getField(this, 5) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    translatedSubtitleContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 2, ""),
    segmentResultsList: jspb.Message.toObjectList(msg.getSegmentResultsList(),
    proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.toObject, includeInstance),
    totalSegmentsProcessed: jspb.Message.getFieldWithDefault(msg, 4, 0),
    successfulSegments: jspb.Message.getFieldWithDefault(msg, 5, 0),
    failedSegments: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse;
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedSubtitleContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    case 3:
      var value = new proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.deserializeBinaryFromReader);
      msg.addSegmentResults(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTotalSegmentsProcessed(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setSuccessfulSegments(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setFailedSegments(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTranslatedSubtitleContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSegmentResultsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult.serializeBinaryToWriter
    );
  }
  f = message.getTotalSegmentsProcessed();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getSuccessfulSegments();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getFailedSegments();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
};


/**
 * optional string translated_subtitle_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.getTranslatedSubtitleContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.setTranslatedSubtitleContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string trace_id = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated TranslatedSegmentResult segment_results = 3;
 * @return {!Array<!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult>}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.getSegmentResultsList = function() {
  return /** @type{!Array<!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult, 3));
};


/**
 * @param {!Array<!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
*/
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.setSegmentResultsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult=} opt_value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.addSegmentResults = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.monkeyfx.api.v1.subtitler.TranslatedSegmentResult, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.clearSegmentResultsList = function() {
  return this.setSegmentResultsList([]);
};


/**
 * optional int32 total_segments_processed = 4;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.getTotalSegmentsProcessed = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.setTotalSegmentsProcessed = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 successful_segments = 5;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.getSuccessfulSegments = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.setSuccessfulSegments = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int32 failed_segments = 6;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.getFailedSegments = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.setFailedSegments = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    videoPath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    targetLanguage: jspb.Message.getFieldWithDefault(msg, 2, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest;
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setVideoPath(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTargetLanguage(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVideoPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTargetLanguage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string video_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.getVideoPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.setVideoPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string target_language = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.getTargetLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.setTargetLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string trace_id = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    translatedSubtitleContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse;
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedSubtitleContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTranslatedSubtitleContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string translated_subtitle_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.getTranslatedSubtitleContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.setTranslatedSubtitleContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string trace_id = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.repeatedFields_ = [7];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    subtitleContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    format: jspb.Message.getFieldWithDefault(msg, 2, ""),
    layout: jspb.Message.getFieldWithDefault(msg, 3, ""),
    fileName: jspb.Message.getFieldWithDefault(msg, 4, ""),
    originalContent: jspb.Message.getFieldWithDefault(msg, 5, ""),
    translatedContent: jspb.Message.getFieldWithDefault(msg, 6, ""),
    segmentsList: jspb.Message.toObjectList(msg.getSegmentsList(),
    proto.monkeyfx.api.v1.subtitler.SubtitleSegment.toObject, includeInstance),
    autoSaveToDefault: jspb.Message.getBooleanFieldWithDefault(msg, 8, false),
    assStyleOptions: (f = msg.getAssStyleOptions()) && google_protobuf_struct_pb.Struct.toObject(includeInstance, f),
    traceId: jspb.Message.getFieldWithDefault(msg, 10, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest;
  return proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubtitleContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFormat(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setLayout(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setFileName(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setOriginalContent(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedContent(value);
      break;
    case 7:
      var value = new proto.monkeyfx.api.v1.subtitler.SubtitleSegment;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.SubtitleSegment.deserializeBinaryFromReader);
      msg.addSegments(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setAutoSaveToDefault(value);
      break;
    case 9:
      var value = new google_protobuf_struct_pb.Struct;
      reader.readMessage(value,google_protobuf_struct_pb.Struct.deserializeBinaryFromReader);
      msg.setAssStyleOptions(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSubtitleContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFormat();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getLayout();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getFileName();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getOriginalContent();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getTranslatedContent();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSegmentsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      7,
      f,
      proto.monkeyfx.api.v1.subtitler.SubtitleSegment.serializeBinaryToWriter
    );
  }
  f = message.getAutoSaveToDefault();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
  f = message.getAssStyleOptions();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      google_protobuf_struct_pb.Struct.serializeBinaryToWriter
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
};


/**
 * optional string subtitle_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getSubtitleContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setSubtitleContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string format = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getFormat = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setFormat = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string layout = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getLayout = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setLayout = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string file_name = 4;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getFileName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setFileName = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string original_content = 5;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getOriginalContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setOriginalContent = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string translated_content = 6;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getTranslatedContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setTranslatedContent = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * repeated SubtitleSegment segments = 7;
 * @return {!Array<!proto.monkeyfx.api.v1.subtitler.SubtitleSegment>}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getSegmentsList = function() {
  return /** @type{!Array<!proto.monkeyfx.api.v1.subtitler.SubtitleSegment>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.monkeyfx.api.v1.subtitler.SubtitleSegment, 7));
};


/**
 * @param {!Array<!proto.monkeyfx.api.v1.subtitler.SubtitleSegment>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
*/
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setSegmentsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 7, value);
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment=} opt_value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.addSegments = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 7, opt_value, proto.monkeyfx.api.v1.subtitler.SubtitleSegment, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.clearSegmentsList = function() {
  return this.setSegmentsList([]);
};


/**
 * optional bool auto_save_to_default = 8;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getAutoSaveToDefault = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setAutoSaveToDefault = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};


/**
 * optional google.protobuf.Struct ass_style_options = 9;
 * @return {?proto.google.protobuf.Struct}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getAssStyleOptions = function() {
  return /** @type{?proto.google.protobuf.Struct} */ (
    jspb.Message.getWrapperField(this, google_protobuf_struct_pb.Struct, 9));
};


/**
 * @param {?proto.google.protobuf.Struct|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
*/
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setAssStyleOptions = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.clearAssStyleOptions = function() {
  return this.setAssStyleOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.hasAssStyleOptions = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional string trace_id = 10;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    filePath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    fileData: msg.getFileData_asB64(),
    fileName: jspb.Message.getFieldWithDefault(msg, 3, ""),
    fileSize: jspb.Message.getFieldWithDefault(msg, 4, 0),
    savedToDefault: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
    format: jspb.Message.getFieldWithDefault(msg, 6, ""),
    layout: jspb.Message.getFieldWithDefault(msg, 7, ""),
    contentSource: jspb.Message.getFieldWithDefault(msg, 8, ""),
    originalFilenameOrTitle: jspb.Message.getFieldWithDefault(msg, 9, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 10, ""),
    status: jspb.Message.getFieldWithDefault(msg, 11, 0),
    errorDetail: (f = msg.getErrorDetail()) && proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse;
  return proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilePath(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setFileData(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setFileName(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setFileSize(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSavedToDefault(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setFormat(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setLayout(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setContentSource(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setOriginalFilenameOrTitle(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    case 11:
      var value = /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 12:
      var value = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader);
      msg.setErrorDetail(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFilePath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFileData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getFileName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getFileSize();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getSavedToDefault();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getFormat();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getLayout();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getContentSource();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getOriginalFilenameOrTitle();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      11,
      f
    );
  }
  f = message.getErrorDetail();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter
    );
  }
};


/**
 * optional string file_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getFilePath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setFilePath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bytes file_data = 2;
 * @return {!(string|Uint8Array)}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getFileData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes file_data = 2;
 * This is a type-conversion wrapper around `getFileData()`
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getFileData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getFileData()));
};


/**
 * optional bytes file_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getFileData()`
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getFileData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getFileData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setFileData = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional string file_name = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getFileName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setFileName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int64 file_size = 4;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getFileSize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setFileSize = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional bool saved_to_default = 5;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getSavedToDefault = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setSavedToDefault = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional string format = 6;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getFormat = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setFormat = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string layout = 7;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getLayout = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setLayout = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string content_source = 8;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getContentSource = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setContentSource = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string original_filename_or_title = 9;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getOriginalFilenameOrTitle = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setOriginalFilenameOrTitle = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string trace_id = 10;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional OperationStatus status = 11;
 * @return {!proto.monkeyfx.api.v1.subtitler.OperationStatus}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getStatus = function() {
  return /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.OperationStatus} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 11, value);
};


/**
 * optional ErrorDetail error_detail = 12;
 * @return {?proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.getErrorDetail = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ErrorDetail} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ErrorDetail, 12));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ErrorDetail|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
*/
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.setErrorDetail = function(value) {
  return jspb.Message.setWrapperField(this, 12, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.clearErrorDetail = function() {
  return this.setErrorDetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.prototype.hasErrorDetail = function() {
  return jspb.Message.getField(this, 12) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.SubtitleSegment.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.toObject = function(includeInstance, msg) {
  var f, obj = {
    startTime: jspb.Message.getFieldWithDefault(msg, 1, 0),
    endTime: jspb.Message.getFieldWithDefault(msg, 2, 0),
    originalText: jspb.Message.getFieldWithDefault(msg, 3, ""),
    translatedText: jspb.Message.getFieldWithDefault(msg, 4, ""),
    status: jspb.Message.getFieldWithDefault(msg, 5, 0),
    errorDetail: (f = msg.getErrorDetail()) && proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.SubtitleSegment;
  return proto.monkeyfx.api.v1.subtitler.SubtitleSegment.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setStartTime(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setEndTime(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setOriginalText(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedText(value);
      break;
    case 5:
      var value = /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 6:
      var value = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader);
      msg.setErrorDetail(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.SubtitleSegment.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStartTime();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getEndTime();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getOriginalText();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTranslatedText();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getErrorDetail();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter
    );
  }
};


/**
 * optional int32 start_time = 1;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.getStartTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.setStartTime = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 end_time = 2;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.getEndTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.setEndTime = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string original_text = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.getOriginalText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.setOriginalText = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string translated_text = 4;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.getTranslatedText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.setTranslatedText = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional OperationStatus status = 5;
 * @return {!proto.monkeyfx.api.v1.subtitler.OperationStatus}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.getStatus = function() {
  return /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.OperationStatus} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional ErrorDetail error_detail = 6;
 * @return {?proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.getErrorDetail = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ErrorDetail} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ErrorDetail, 6));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ErrorDetail|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} returns this
*/
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.setErrorDetail = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment} returns this
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.clearErrorDetail = function() {
  return this.setErrorDetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.SubtitleSegment.prototype.hasErrorDetail = function() {
  return jspb.Message.getField(this, 6) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.repeatedFields_ = [1,2,3,7];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    formatsList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
    layoutsList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
    contentSourcesList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f,
    fileNamePrefix: jspb.Message.getFieldWithDefault(msg, 4, ""),
    originalContent: jspb.Message.getFieldWithDefault(msg, 5, ""),
    translatedContent: jspb.Message.getFieldWithDefault(msg, 6, ""),
    segmentsList: jspb.Message.toObjectList(msg.getSegmentsList(),
    proto.monkeyfx.api.v1.subtitler.SubtitleSegment.toObject, includeInstance),
    autoSaveToDefault: jspb.Message.getBooleanFieldWithDefault(msg, 8, false),
    translationRequested: jspb.Message.getBooleanFieldWithDefault(msg, 9, false),
    assStyleOptions: (f = msg.getAssStyleOptions()) && google_protobuf_struct_pb.Struct.toObject(includeInstance, f),
    traceId: jspb.Message.getFieldWithDefault(msg, 11, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest;
  return proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addFormats(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addLayouts(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.addContentSources(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setFileNamePrefix(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setOriginalContent(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedContent(value);
      break;
    case 7:
      var value = new proto.monkeyfx.api.v1.subtitler.SubtitleSegment;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.SubtitleSegment.deserializeBinaryFromReader);
      msg.addSegments(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setAutoSaveToDefault(value);
      break;
    case 9:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setTranslationRequested(value);
      break;
    case 10:
      var value = new google_protobuf_struct_pb.Struct;
      reader.readMessage(value,google_protobuf_struct_pb.Struct.deserializeBinaryFromReader);
      msg.setAssStyleOptions(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFormatsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
  f = message.getLayoutsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
  f = message.getContentSourcesList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      3,
      f
    );
  }
  f = message.getFileNamePrefix();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getOriginalContent();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getTranslatedContent();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getSegmentsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      7,
      f,
      proto.monkeyfx.api.v1.subtitler.SubtitleSegment.serializeBinaryToWriter
    );
  }
  f = message.getAutoSaveToDefault();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
  f = message.getTranslationRequested();
  if (f) {
    writer.writeBool(
      9,
      f
    );
  }
  f = message.getAssStyleOptions();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      google_protobuf_struct_pb.Struct.serializeBinaryToWriter
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
};


/**
 * repeated string formats = 1;
 * @return {!Array<string>}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getFormatsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setFormatsList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.addFormats = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.clearFormatsList = function() {
  return this.setFormatsList([]);
};


/**
 * repeated string layouts = 2;
 * @return {!Array<string>}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getLayoutsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setLayoutsList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.addLayouts = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.clearLayoutsList = function() {
  return this.setLayoutsList([]);
};


/**
 * repeated string content_sources = 3;
 * @return {!Array<string>}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getContentSourcesList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setContentSourcesList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.addContentSources = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.clearContentSourcesList = function() {
  return this.setContentSourcesList([]);
};


/**
 * optional string file_name_prefix = 4;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getFileNamePrefix = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setFileNamePrefix = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string original_content = 5;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getOriginalContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setOriginalContent = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string translated_content = 6;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getTranslatedContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setTranslatedContent = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * repeated SubtitleSegment segments = 7;
 * @return {!Array<!proto.monkeyfx.api.v1.subtitler.SubtitleSegment>}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getSegmentsList = function() {
  return /** @type{!Array<!proto.monkeyfx.api.v1.subtitler.SubtitleSegment>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.monkeyfx.api.v1.subtitler.SubtitleSegment, 7));
};


/**
 * @param {!Array<!proto.monkeyfx.api.v1.subtitler.SubtitleSegment>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
*/
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setSegmentsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 7, value);
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment=} opt_value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.SubtitleSegment}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.addSegments = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 7, opt_value, proto.monkeyfx.api.v1.subtitler.SubtitleSegment, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.clearSegmentsList = function() {
  return this.setSegmentsList([]);
};


/**
 * optional bool auto_save_to_default = 8;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getAutoSaveToDefault = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setAutoSaveToDefault = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};


/**
 * optional bool translation_requested = 9;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getTranslationRequested = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 9, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setTranslationRequested = function(value) {
  return jspb.Message.setProto3BooleanField(this, 9, value);
};


/**
 * optional google.protobuf.Struct ass_style_options = 10;
 * @return {?proto.google.protobuf.Struct}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getAssStyleOptions = function() {
  return /** @type{?proto.google.protobuf.Struct} */ (
    jspb.Message.getWrapperField(this, google_protobuf_struct_pb.Struct, 10));
};


/**
 * @param {?proto.google.protobuf.Struct|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
*/
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setAssStyleOptions = function(value) {
  return jspb.Message.setWrapperField(this, 10, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.clearAssStyleOptions = function() {
  return this.setAssStyleOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.hasAssStyleOptions = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional string trace_id = 11;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    filesList: jspb.Message.toObjectList(msg.getFilesList(),
    proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.toObject, includeInstance),
    traceId: jspb.Message.getFieldWithDefault(msg, 2, ""),
    status: jspb.Message.getFieldWithDefault(msg, 3, 0),
    errorDetail: (f = msg.getErrorDetail()) && proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse;
  return proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.deserializeBinaryFromReader);
      msg.addFiles(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    case 3:
      var value = /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 4:
      var value = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader);
      msg.setErrorDetail(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFilesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse.serializeBinaryToWriter
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getErrorDetail();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter
    );
  }
};


/**
 * repeated SaveSubtitleResponse files = 1;
 * @return {!Array<!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse>}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.getFilesList = function() {
  return /** @type{!Array<!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse, 1));
};


/**
 * @param {!Array<!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse>} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} returns this
*/
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.setFilesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse=} opt_value
 * @param {number=} opt_index
 * @return {!proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.addFiles = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.monkeyfx.api.v1.subtitler.SaveSubtitleResponse, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.clearFilesList = function() {
  return this.setFilesList([]);
};


/**
 * optional string trace_id = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional OperationStatus status = 3;
 * @return {!proto.monkeyfx.api.v1.subtitler.OperationStatus}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.getStatus = function() {
  return /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.OperationStatus} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional ErrorDetail error_detail = 4;
 * @return {?proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.getErrorDetail = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ErrorDetail} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ErrorDetail, 4));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ErrorDetail|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} returns this
*/
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.setErrorDetail = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.clearErrorDetail = function() {
  return this.setErrorDetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse.prototype.hasErrorDetail = function() {
  return jspb.Message.getField(this, 4) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ClearCacheRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    cacheType: jspb.Message.getFieldWithDefault(msg, 1, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheRequest}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ClearCacheRequest;
  return proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ClearCacheRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheRequest}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCacheType(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ClearCacheRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCacheType();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string cache_type = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.prototype.getCacheType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.prototype.setCacheType = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string trace_id = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheRequest.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    success: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
    message: jspb.Message.getFieldWithDefault(msg, 2, ""),
    traceId: jspb.Message.getFieldWithDefault(msg, 3, ""),
    status: jspb.Message.getFieldWithDefault(msg, 4, 0),
    errorDetail: (f = msg.getErrorDetail()) && proto.monkeyfx.api.v1.subtitler.ErrorDetail.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ClearCacheResponse;
  return proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSuccess(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMessage(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTraceId(value);
      break;
    case 4:
      var value = /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (reader.readEnum());
      msg.setStatus(value);
      break;
    case 5:
      var value = new proto.monkeyfx.api.v1.subtitler.ErrorDetail;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ErrorDetail.deserializeBinaryFromReader);
      msg.setErrorDetail(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSuccess();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getMessage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTraceId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getErrorDetail();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.monkeyfx.api.v1.subtitler.ErrorDetail.serializeBinaryToWriter
    );
  }
};


/**
 * optional bool success = 1;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.getSuccess = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.setSuccess = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional string message = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.getMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.setMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string trace_id = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.getTraceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.setTraceId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional OperationStatus status = 4;
 * @return {!proto.monkeyfx.api.v1.subtitler.OperationStatus}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.getStatus = function() {
  return /** @type {!proto.monkeyfx.api.v1.subtitler.OperationStatus} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.monkeyfx.api.v1.subtitler.OperationStatus} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.setStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional ErrorDetail error_detail = 5;
 * @return {?proto.monkeyfx.api.v1.subtitler.ErrorDetail}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.getErrorDetail = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ErrorDetail} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ErrorDetail, 5));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ErrorDetail|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} returns this
*/
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.setErrorDetail = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ClearCacheResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.clearErrorDetail = function() {
  return this.setErrorDetail(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ClearCacheResponse.prototype.hasErrorDetail = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * @enum {number}
 */
proto.monkeyfx.api.v1.subtitler.OperationStatus = {
  OPERATION_STATUS_UNSPECIFIED: 0,
  OPERATION_STATUS_SUCCESS: 1,
  OPERATION_STATUS_ERROR: 2,
  OPERATION_STATUS_PARTIAL_SUCCESS: 3,
  OPERATION_STATUS_IN_PROGRESS: 4
};

goog.object.extend(exports, proto.monkeyfx.api.v1.subtitler);
