// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@grpc/grpc-js');
var api$protos_v1_ai_config_ai_config_service_pb = require('../../../api-protos/v1/ai_config/ai_config_service_pb.js');

function serialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsRequest(arg) {
  if (!(arg instanceof api$protos_v1_ai_config_ai_config_service_pb.UpdateAIConfigurationsRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.ai_config.UpdateAIConfigurationsRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsRequest(buffer_arg) {
  return api$protos_v1_ai_config_ai_config_service_pb.UpdateAIConfigurationsRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsResponse(arg) {
  if (!(arg instanceof api$protos_v1_ai_config_ai_config_service_pb.UpdateAIConfigurationsResponse)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.ai_config.UpdateAIConfigurationsResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsResponse(buffer_arg) {
  return api$protos_v1_ai_config_ai_config_service_pb.UpdateAIConfigurationsResponse.deserializeBinary(new Uint8Array(buffer_arg));
}


var AIConfigurationServiceService = exports.AIConfigurationServiceService = {
  updateAIConfigurations: {
    path: '/monkeyfx.api.v1.ai_config.AIConfigurationService/UpdateAIConfigurations',
    requestStream: false,
    responseStream: false,
    requestType: api$protos_v1_ai_config_ai_config_service_pb.UpdateAIConfigurationsRequest,
    responseType: api$protos_v1_ai_config_ai_config_service_pb.UpdateAIConfigurationsResponse,
    requestSerialize: serialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsRequest,
    responseSerialize: serialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsResponse,
    responseDeserialize: deserialize_monkeyfx_api_v1_ai_config_UpdateAIConfigurationsResponse,
  },
};

exports.AIConfigurationServiceClient = grpc.makeGenericClientConstructor(AIConfigurationServiceService, 'AIConfigurationService');
