/**
 * 操作状态映射工具函数
 * 用于Vue渲染进程中的状态显示
 */

/**
 * 将操作状态枚举值映射为字符串
 * @param {number|string} status - 状态值或状态字符串
 * @returns {string} 中文状态描述
 */
export function mapOperationStatusToString(status) {
  if (status === null || status === undefined) return '未知';
  
  // 如果已经是字符串，直接返回或转换为中文
  if (typeof status === 'string') {
    // 如果是英文状态，转换为中文
    const statusMap = {
      'UNSPECIFIED': '未指定',
      'IN_PROGRESS': '处理中',
      'ERROR': '失败',
      'SUCCESS': '已完成',
      'PARTIAL_SUCCESS': '部分完成',
      'CANCELLED': '已取消',
      'PENDING': '等待中',
      'UNKNOWN': '未知'
    };
    return statusMap[status] || status;
  }
  
  // 枚举值映射
  switch (status) {
    case 0: return '未指定';
    case 1: return '处理中';
    case 2: return '失败';
    case 3: return '已完成';
    case 4: return '部分完成';
    case 5: return '已取消';
    case 6: return '等待中';
    default: return '未知';
  }
}

/**
 * 获取状态对应的颜色类名
 * @param {string} status - 状态字符串
 * @returns {string} Tailwind CSS 颜色类名
 */
export function getStatusColorClass(status) {
  const normalizedStatus = status.toLowerCase();
  
  switch (normalizedStatus) {
    case 'pending':
    case '等待中':
      return 'bg-yellow-100 text-yellow-800';
    case 'in_progress':
    case 'processing':
    case '处理中':
      return 'bg-blue-100 text-blue-800';
    case 'completed':
    case 'success':
    case '已完成':
      return 'bg-green-100 text-green-800';
    case 'failed':
    case 'error':
    case '失败':
      return 'bg-red-100 text-red-800';
    case 'cancelled':
    case '已取消':
      return 'bg-gray-100 text-gray-800';
    case 'paused':
    case '已暂停':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * 获取状态对应的图标SVG模板
 * @param {string} status - 状态字符串
 * @returns {object} Vue组件图标对象
 */
export function getStatusIcon(status) {
  const normalizedStatus = status.toLowerCase();
  
  switch (normalizedStatus) {
    case 'pending':
    case '等待中':
      return {
        template: `
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `
      };
    case 'in_progress':
    case 'processing':
    case '处理中':
      return {
        template: `
          <svg class="animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        `
      };
    case 'completed':
    case 'success':
    case '已完成':
      return {
        template: `
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        `
      };
    case 'failed':
    case 'error':
    case '失败':
      return {
        template: `
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        `
      };
    case 'cancelled':
    case '已取消':
      return {
        template: `
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 715.636 5.636m12.728 12.728L5.636 5.636" />
          </svg>
        `
      };
    case 'paused':
    case '已暂停':
      return {
        template: `
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `
      };
    default:
      return {
        template: `
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `
      };
  }
} 