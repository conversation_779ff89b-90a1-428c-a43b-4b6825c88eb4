/**
 * 操作状态枚举
 * 用于表示工作流操作的各种状态
 */
export const OPERATION_STATUS = {
  IDLE: 'IDLE',                 // 空闲状态
  PENDING: 'PENDING',           // 等待开始
  RUNNING: 'RUNNING',           // 正在运行
  PAUSED: 'PAUSED',             // 已暂停
  COMPLETED: 'COMPLETED',       // 已完成
  ERROR: 'ERROR',               // 出错
  CANCELED: 'CANCELED',         // 已取消
  TIMEOUT: 'TIMEOUT'            // 超时
};

/**
 * 将操作状态映射为用户友好的中文描述
 * @param {string} status - 操作状态
 * @returns {string} 状态的中文描述
 */
export const mapOperationStatusToString = (status) => {
  const statusMap = {
    [OPERATION_STATUS.IDLE]: '未开始',
    [OPERATION_STATUS.PENDING]: '准备中',
    [OPERATION_STATUS.RUNNING]: '处理中',
    [OPERATION_STATUS.PAUSED]: '已暂停',
    [OPERATION_STATUS.COMPLETED]: '已完成',
    [OPERATION_STATUS.ERROR]: '出错',
    [OPERATION_STATUS.CANCELED]: '已取消',
    [OPERATION_STATUS.TIMEOUT]: '已超时'
  };
  
  return statusMap[status] || '未知状态';
};

/**
 * 判断操作是否处于活动状态（正在进行中）
 * @param {string} status - 操作状态
 * @returns {boolean} 是否处于活动状态
 */
export const isOperationActive = (status) => {
  return status === OPERATION_STATUS.PENDING || 
         status === OPERATION_STATUS.RUNNING;
};

/**
 * 判断操作是否已完成（无论成功与否）
 * @param {string} status - 操作状态
 * @returns {boolean} 是否已完成
 */
export const isOperationCompleted = (status) => {
  return status === OPERATION_STATUS.COMPLETED || 
         status === OPERATION_STATUS.ERROR || 
         status === OPERATION_STATUS.CANCELED || 
         status === OPERATION_STATUS.TIMEOUT;
};

/**
 * 判断操作是否成功完成
 * @param {string} status - 操作状态
 * @returns {boolean} 是否成功完成
 */
export const isOperationSuccessful = (status) => {
  return status === OPERATION_STATUS.COMPLETED;
}; 