<template>
  <span 
    class="operation-status-badge inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
    :class="statusClass"
  >
    <component 
      :is="iconComponent" 
      v-if="showIcon" 
      class="h-3 w-3 mr-1"
    />
    {{ statusText }}
  </span>
</template>

<script setup>
import { computed } from 'vue';

// 导入操作状态工具函数
import { mapOperationStatusToString, getStatusColorClass, getStatusIcon } from '@/vue/utils/operationStatus';

const props = defineProps({
  // 操作状态 (可以是数字枚举值或字符串)
  status: {
    type: [Number, String],
    required: true
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 自定义文本 (覆盖默认状态文本)
  customText: {
    type: String,
    default: ''
  },
  // 大小变体
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  }
});

// 计算状态文本
const statusText = computed(() => {
  if (props.customText) return props.customText;
  
  // 如果是数字，使用映射函数
  if (typeof props.status === 'number') {
    return mapOperationStatusToString(props.status);
  }
  
  // 如果是字符串，直接返回
  return props.status;
});

// 计算状态类
const statusClass = computed(() => {
  // 基础大小类
  const sizeClass = {
    'small': 'text-xs',
    'default': 'text-sm',
    'large': 'text-base'
  }[props.size] || 'text-sm';
  
  // 根据状态值或文本确定颜色
  const status = typeof props.status === 'number' 
    ? mapOperationStatusToString(props.status) 
    : props.status;
  
  const colorClass = getStatusColorClass(status);
  
  return `${sizeClass} ${colorClass}`;
});

// 计算图标组件
const iconComponent = computed(() => {
  // 根据状态值或文本确定图标
  const status = typeof props.status === 'number' 
    ? mapOperationStatusToString(props.status) 
    : props.status;
  
  return getStatusIcon(status);
});
</script>

<style scoped>
.operation-status-badge {
  @apply transition-all duration-200;
}
</style> 