<template>
  <div v-if="errorDetail" class="error-detail-panel bg-white rounded-lg shadow-md border border-red-200 overflow-hidden">
    <!-- 标题栏 -->
    <div class="bg-red-50 px-4 py-3 border-b border-red-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <svg class="h-5 w-5 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h3 class="text-sm font-medium text-red-800">{{ title || '处理过程中发生错误' }}</h3>
        </div>
        <button 
          v-if="showClose" 
          @click="$emit('close')" 
          class="text-red-400 hover:text-red-600 focus:outline-none"
        >
          <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- 错误内容 -->
    <div class="p-4">
      <!-- 用户友好消息 -->
      <div class="mb-4">
        <p class="text-sm font-medium text-gray-800 mb-1">错误信息</p>
        <p class="text-sm text-gray-700">{{ errorDetail.userMessage || errorMessage }}</p>
      </div>

      <!-- 错误代码 -->
      <div v-if="errorDetail.errorCode" class="mb-4">
        <p class="text-xs font-medium text-gray-500 mb-1">错误代码</p>
        <code class="text-xs bg-gray-100 px-2 py-1 rounded font-mono">{{ errorDetail.errorCode }}</code>
      </div>

      <!-- 上下文信息 -->
      <div v-if="hasContext" class="mb-4">
        <p class="text-xs font-medium text-gray-500 mb-1">上下文信息</p>
        <div class="bg-gray-50 p-2 rounded border border-gray-200">
          <div v-for="(value, key) in errorDetail.context" :key="key" class="text-xs text-gray-600">
            <span class="font-medium">{{ key }}:</span> {{ value }}
          </div>
        </div>
      </div>

      <!-- 技术信息 (可折叠) -->
      <div v-if="errorDetail.technicalMessage && showTechnicalDetails">
        <div 
          @click="toggleTechnicalDetails" 
          class="flex items-center text-xs text-gray-500 cursor-pointer hover:text-gray-700 mb-1"
        >
          <span class="font-medium">技术详情</span>
          <svg 
            class="h-4 w-4 ml-1 transition-transform" 
            :class="{'transform rotate-180': showTechnicalDetails}"
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
        <div v-if="showTechnicalDetails" class="bg-gray-50 p-2 rounded border border-gray-200">
          <pre class="text-xs font-mono text-gray-600 whitespace-pre-wrap">{{ errorDetail.technicalMessage }}</pre>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="actions && actions.length > 0" class="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2">
      <button 
        v-for="action in actions" 
        :key="action.key"
        @click="handleAction(action)"
        :class="[
          'px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-offset-2',
          action.primary 
            ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
            : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500'
        ]"
      >
        {{ action.label }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  // 错误详情对象
  errorDetail: {
    type: Object,
    default: null
  },
  // 普通错误消息 (当没有结构化错误时使用)
  errorMessage: {
    type: String,
    default: '发生未知错误'
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true
  },
  // 操作按钮
  actions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['close', 'action']);

// 是否显示技术详情
const showTechnicalDetails = ref(false);

// 计算是否有上下文信息
const hasContext = computed(() => {
  return props.errorDetail && 
         props.errorDetail.context && 
         Object.keys(props.errorDetail.context).length > 0;
});

// 切换技术详情显示状态
const toggleTechnicalDetails = () => {
  showTechnicalDetails.value = !showTechnicalDetails.value;
};

// 处理操作按钮点击
const handleAction = (action) => {
  emit('action', action);
};
</script>

<style scoped>
.error-detail-panel {
  max-width: 100%;
  width: 100%;
}

@media (min-width: 640px) {
  .error-detail-panel {
    max-width: 32rem;
  }
}
</style> 