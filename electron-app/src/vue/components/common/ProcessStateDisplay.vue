<template>
  <div 
    v-if="processState && processState.isActive"
    class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4"
  >
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center space-x-2">
        <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
        <span class="text-sm font-medium text-blue-800">
          {{ processState.workflowType || '处理中' }}
        </span>
        <span v-if="processState.traceId" class="text-xs text-blue-600 font-mono bg-blue-100 px-2 py-1 rounded">
          ID: {{ processState.traceId.slice(-8) }}
        </span>
      </div>
      <div class="text-xs text-blue-600">
        {{ getStatusText(processState.status) }}
      </div>
    </div>
    
    <div class="mb-2">
      <div class="flex items-center justify-between">
        <span class="text-sm text-blue-700">{{ processState.currentStageName }}</span>
        <span class="text-sm text-blue-600">{{ processState.overallPercentage }}%</span>
      </div>
      <div class="w-full bg-blue-200 rounded-full h-2 mt-1">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${processState.overallPercentage}%` }"
        ></div>
      </div>
    </div>
    
    <div class="text-sm text-blue-700">
      {{ processState.currentMessage }}
    </div>
    
    <!-- 错误详情 -->
    <div v-if="processState.errorDetail" class="mt-3 p-3 bg-red-100 border border-red-200 rounded">
      <div class="text-red-800 font-medium text-sm">{{ processState.errorDetail.userMessage }}</div>
      <div v-if="processState.errorDetail.errorCode" class="text-red-600 text-xs mt-1">
        错误代码: {{ processState.errorDetail.errorCode }}
      </div>
      <div v-if="processState.errorDetail.technicalMessage && showTechnicalDetails" class="text-red-500 text-xs mt-1">
        技术详情: {{ processState.errorDetail.technicalMessage }}
      </div>
      <button 
        v-if="processState.errorDetail.technicalMessage"
        @click="showTechnicalDetails = !showTechnicalDetails"
        class="text-red-600 text-xs mt-1 hover:underline"
      >
        {{ showTechnicalDetails ? '隐藏' : '显示' }}技术详情
      </button>
    </div>
  </div>
  
  <!-- 静态状态显示（非活动状态） -->
  <div 
    v-else-if="processState && !processState.isActive && processState.status"
    class="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <span class="text-sm font-medium text-gray-700">
          {{ processState.workflowType || '最近处理' }}
        </span>
        <span v-if="processState.traceId" class="text-xs text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded">
          ID: {{ processState.traceId.slice(-8) }}
        </span>
      </div>
      <div class="flex items-center space-x-1">
        <span v-if="processState.status === 'SUCCESS'" class="text-green-600">✅</span>
                    <span v-else-if="processState.status === 'FAILURE' || processState.status === 'ERROR'" class="text-red-600">❌</span>
        <span v-else-if="processState.status === 'PARTIAL_SUCCESS'" class="text-yellow-600">⚠️</span>
        <span class="text-xs text-gray-600">{{ getStatusText(processState.status) }}</span>
      </div>
    </div>
    <div v-if="processState.currentMessage" class="text-sm text-gray-600 mt-1">
      {{ processState.currentMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  processState: {
    type: Object,
    default: null
  }
});

const showTechnicalDetails = ref(false);

const getStatusText = (status) => {
  const statusMap = {
    'SUCCESS': '成功',
            'FAILURE': '失败',
        'ERROR': '失败',
    'PARTIAL_SUCCESS': '部分成功',
    'IN_PROGRESS': '进行中',
    'UNSPECIFIED': '未知状态'
  };
  return statusMap[status] || status;
};
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style> 