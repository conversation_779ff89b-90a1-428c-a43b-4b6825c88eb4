<template>
  <div v-if="isVisible" class="enhanced-progress-indicator bg-white rounded-lg shadow-sm border border-gray-200 p-4">
    <!-- 标题栏 -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center">
        <div class="flex-shrink-0 mr-3">
          <div v-if="!isError" class="relative">
            <svg 
              class="animate-spin h-6 w-6"
              :class="spinnerColorClass"
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24">
              <circle 
                class="opacity-25" 
                cx="12" 
                cy="12" 
                r="10" 
                stroke="currentColor" 
                stroke-width="4">
              </circle>
              <path 
                class="opacity-75" 
                fill="currentColor" 
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
          </div>
          <div v-else class="text-red-500">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        
        <div>
          <h3 class="text-sm font-medium" :class="titleColorClass">
            {{ currentStageName || title }}
          </h3>
          <p v-if="subtitle" class="text-xs text-gray-500 mt-1">{{ subtitle }}</p>
        </div>
      </div>
      
      <div class="text-right">
        <div class="text-sm font-semibold" :class="percentageColorClass">
          {{ formattedPercentage }}
        </div>
        <div v-if="showTime" class="text-xs text-gray-400 mt-1">
          {{ elapsedTime }}
        </div>
      </div>
    </div>

    <!-- 主进度条 -->
    <div class="mb-4">
      <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
        <div 
          class="h-3 rounded-full transition-all duration-500 ease-out relative"
          :class="progressBarColorClass"
          :style="{ width: progressWidth }">
          <!-- 进度条动画效果 -->
          <div v-if="!isError && percentage < 100" 
               class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse">
          </div>
        </div>
      </div>
    </div>

    <!-- 当前消息 -->
    <div class="mb-3">
      <p class="text-sm" :class="messageColorClass">
        {{ currentMessage || message }}
      </p>
    </div>

    <!-- 错误信息 -->
    <div v-if="isError && errorMessage" class="mb-3 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-4 w-4 text-red-400 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="ml-2 text-sm text-red-700">
          <p class="font-medium">错误详情:</p>
          <p class="mt-1">{{ errorMessage }}</p>
        </div>
      </div>
    </div>

    <!-- 阶段历史 -->
    <div v-if="showStageHistory && stageHistory.length > 1" class="border-t border-gray-100 pt-3">
      <div class="text-xs text-gray-500 mb-2">处理阶段:</div>
      <div class="space-y-1">
        <div 
          v-for="(stage, index) in stageHistory" 
          :key="index"
          class="flex items-center justify-between text-xs"
          :class="{
            'text-green-600': stage.completed,
            'text-blue-600': stage.active,
            'text-gray-400': !stage.active && !stage.completed
          }">
          <span class="flex items-center">
            <span v-if="stage.completed" class="mr-1">✓</span>
            <span v-else-if="stage.active" class="mr-1">⏳</span>
            <span v-else class="mr-1">⭕</span>
            {{ stage.name }}
          </span>
          <span v-if="stage.duration && stage.completed" class="text-gray-400">
            {{ stage.duration }}ms
          </span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="showActions && actions.length > 0" class="border-t border-gray-100 pt-3 mt-3">
      <div class="flex space-x-2">
        <button
          v-for="action in actions"
          :key="action.key"
          @click="handleAction(action)"
          :disabled="action.disabled"
          class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors"
          :class="[
            action.primary 
              ? 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500' 
              : 'text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500',
            action.disabled ? 'opacity-50 cursor-not-allowed' : ''
          ]">
          <span v-if="action.icon" class="mr-1">{{ action.icon }}</span>
          {{ action.label }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  // 基础显示控制
  isVisible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '处理中...'
  },
  subtitle: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: '请稍候'
  },
  
  // 进度相关
  percentage: {
    type: Number,
    default: 0
  },
  stageName: {
    type: String,
    default: ''
  },
  
  // 错误状态
  isError: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  
  // 主题和样式
  theme: {
    type: String,
    default: 'blue',
    validator: (value) => ['blue', 'green', 'indigo', 'purple', 'red', 'yellow', 'orange', 'pink', 'gray'].includes(value)
  },
  
  // 功能开关
  showTime: {
    type: Boolean,
    default: true
  },
  showStageHistory: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: false
  },
  
  // 操作按钮
  actions: {
    type: Array,
    default: () => []
  },
  
  // 进度更新监听
  progressUpdateListener: {
    type: Function,
    default: null
  }
});

const emit = defineEmits(['action', 'stage-complete']);

// 响应式数据
const startTime = ref(Date.now());
const currentTime = ref(Date.now());
const currentStageName = ref(props.stageName);
const currentMessage = ref(props.message);
const stageHistory = ref([]);

// 定时器
let timeInterval = null;

// 计算属性
const formattedPercentage = computed(() => {
  if (props.isError) return '错误';
  if (props.percentage >= 100) return '完成';
  return `${Math.round(props.percentage)}%`;
});

const progressWidth = computed(() => {
  if (props.isError) return '100%';
  return `${Math.min(100, Math.max(0, props.percentage))}%`;
});

const elapsedTime = computed(() => {
  const elapsed = currentTime.value - startTime.value;
  const seconds = Math.floor(elapsed / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}分${seconds % 60}秒`;
  }
  return `${seconds}秒`;
});

// 样式计算
const spinnerColorClass = computed(() => {
  const colorMap = {
    blue: 'text-blue-500',
    green: 'text-green-500',
    indigo: 'text-indigo-500',
    purple: 'text-purple-500',
    red: 'text-red-500',
    yellow: 'text-yellow-500',
    orange: 'text-orange-500',
    pink: 'text-pink-500',
    gray: 'text-gray-500'
  };
  return colorMap[props.theme] || colorMap.blue;
});

const progressBarColorClass = computed(() => {
  if (props.isError) return 'bg-red-500';
  if (props.percentage >= 100) return 'bg-green-500';
  
  const colorMap = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    indigo: 'bg-indigo-600',
    purple: 'text-purple-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600',
    orange: 'bg-orange-600',
    pink: 'bg-pink-600',
    gray: 'bg-gray-600'
  };
  return colorMap[props.theme] || colorMap.blue;
});

const titleColorClass = computed(() => {
  if (props.isError) return 'text-red-700';
  if (props.percentage >= 100) return 'text-green-700';
  return 'text-gray-700';
});

const messageColorClass = computed(() => {
  if (props.isError) return 'text-red-600';
  if (props.percentage >= 100) return 'text-green-600';
  return 'text-gray-600';
});

const percentageColorClass = computed(() => {
  if (props.isError) return 'text-red-600';
  if (props.percentage >= 100) return 'text-green-600';
  return 'text-blue-600';
});

// 方法
const handleAction = (action) => {
  emit('action', action);
};

const updateStageHistory = (stageName, isComplete = false) => {
  const existing = stageHistory.value.find(s => s.name === stageName);
  if (existing) {
    if (isComplete) {
      existing.completed = true;
      existing.active = false;
      existing.duration = Date.now() - existing.startTime;
    } else {
      existing.active = true;
    }
  } else {
    stageHistory.value.push({
      name: stageName,
      active: !isComplete,
      completed: isComplete,
      startTime: Date.now(),
      duration: null
    });
  }
  
  // 更新其他阶段状态
  stageHistory.value.forEach(stage => {
    if (stage.name !== stageName) {
      stage.active = false;
    }
  });
};

// 监听器
watch(() => props.stageName, (newStageName) => {
  if (newStageName && newStageName !== currentStageName.value) {
    // 完成上一个阶段
    if (currentStageName.value) {
      updateStageHistory(currentStageName.value, true);
      emit('stage-complete', currentStageName.value);
    }
    
    // 开始新阶段
    currentStageName.value = newStageName;
    updateStageHistory(newStageName, false);
  }
});

watch(() => props.message, (newMessage) => {
  currentMessage.value = newMessage;
});

watch(() => props.percentage, (newPercentage) => {
  if (newPercentage >= 100 && currentStageName.value) {
    updateStageHistory(currentStageName.value, true);
  }
});

// 生命周期
onMounted(() => {
  // 启动时间计时器
  timeInterval = setInterval(() => {
    currentTime.value = Date.now();
  }, 1000);
  
  // 设置进度更新监听器
  if (props.progressUpdateListener) {
    window.electronAPI?.onProgressUpdate?.(props.progressUpdateListener);
  }
  
  // 初始化第一个阶段
  if (props.stageName) {
    updateStageHistory(props.stageName, false);
  }
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
.enhanced-progress-indicator {
  @apply transition-all duration-300 ease-in-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 