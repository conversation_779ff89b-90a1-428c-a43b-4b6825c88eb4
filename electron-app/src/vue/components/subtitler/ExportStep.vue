<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 6: 导出字幕</h3>
      <p class="text-sm text-gray-500 mt-1">选择格式和样式导出最终字幕</p>
    </div>

    <!-- 当前语言设置显示 -->
    <div v-if="hasEditableSegments" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <span class="text-lg mr-2">🌍</span>
          <span class="text-sm font-medium text-blue-800">当前语言设置</span>
        </div>
        <div class="text-sm text-blue-600">
          {{ languageSettings }}
        </div>
      </div>
    </div>

    <!-- 前置条件警告 -->
    <StatusMessage
      v-if="!hasEditableSegments"
      type="warning"
      title="需要先完成前面的步骤"
      message="请先完成字幕编辑或翻译处理，准备好字幕内容后再进行导出"
      :actions="[
        { key: 'goto-edit', label: '前往字幕编辑', icon: '📝', primary: true }
      ]"
      @action="handlePrerequisiteAction"
    />

    <!-- 导出设置 -->
    <div v-if="hasEditableSegments && !isLoading" class="current-actions mb-6">
      <!-- 内容选择 (可多选) -->
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-4">📋 内容选择 (可多选)</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <label
            v-for="content in contentOptions"
            :key="content.value"
            :class="[
              'flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200',
              selectedContents.includes(content.value)
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 bg-white hover:bg-gray-50'
            ]"
          >
            <input
              type="checkbox"
              :value="content.value"
              v-model="selectedContents"
              class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-3"
            />
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-800">{{ content.label }}</div>
              <div class="text-xs text-gray-600">{{ content.description }}</div>
            </div>
          </label>
        </div>
      </div>

      <!-- 格式选择 (可多选) -->
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-4">📄 格式选择 (可多选)</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          <label
            v-for="format in formatOptions"
            :key="format.value"
            :class="[
              'flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200',
              selectedFormats.includes(format.value)
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 bg-white hover:bg-gray-50'
            ]"
          >
            <input
              type="checkbox"
              :value="format.value"
              v-model="selectedFormats"
              class="h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500 mr-2"
            />
            <div class="text-center">
              <div class="text-sm font-medium text-gray-800">{{ format.label }}</div>
              <div class="text-xs text-gray-600">{{ format.extension }}</div>
            </div>
          </label>
        </div>
      </div>

      <!-- 导出预览 -->
      <div v-if="selectedContents.length > 0 && selectedFormats.length > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-blue-800 mb-3">📊 导出预览 ({{ totalFiles }}个文件)</h4>
        <div class="bg-white rounded-md p-3 border border-blue-200">
          <div class="text-xs text-blue-600 mb-2">📁 将生成文件:</div>
          <div class="space-y-1 max-h-32 overflow-y-auto">
            <div
              v-for="file in previewFiles"
              :key="file"
              class="text-sm text-gray-700 font-mono"
            >
              {{ file }}
            </div>
          </div>
          <div class="mt-3 pt-3 border-t border-blue-200 text-xs text-blue-600">
            📊 预计总大小: {{ estimatedSize }}
          </div>
        </div>
      </div>

      <!-- 双语样式设置 (仅在选择双语内容时显示) -->
      <div v-if="showBilingualSettings" class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-4">🎨 双语样式设置</h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">分隔方式:</label>
            <select
              v-model="bilingualSettings.separator"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="newline">换行</option>
              <option value="space">空格</option>
              <option value="pipe">|</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">原文字体:</label>
            <select
              v-model="bilingualSettings.originalFont"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="16px">16px</option>
              <option value="18px">18px</option>
              <option value="20px">20px</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">译文字体:</label>
            <select
              v-model="bilingualSettings.translationFont"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="14px">14px</option>
              <option value="16px">16px</option>
              <option value="18px">18px</option>
            </select>
          </div>
        </div>
        <div class="mt-3">
          <label class="flex items-center space-x-2 text-sm text-gray-700">
            <input
              type="checkbox"
              v-model="bilingualSettings.addLanguageLabels"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span>添加语言标识 (🇨🇳/🇺🇸)</span>
          </label>
        </div>
      </div>

      <!-- 导出设置 -->
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-4">⚙️ 导出设置</h4>
        <div class="space-y-3">
          <!-- 默认地址设置 -->
          <label class="flex items-center space-x-2 text-sm text-gray-700">
            <input
              type="checkbox"
              v-model="useDefaultPath"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span>使用默认地址（不弹出确认对话框）</span>
          </label>

          <!-- 默认路径显示 -->
          <div v-if="useDefaultPath" class="ml-6 text-xs text-gray-600">
            <span>默认路径: ~/Downloads/subtitles/</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button
          @click="handleBatchExport"
          :disabled="isLoading || !hasEditableSegments || selectedContents.length === 0 || selectedFormats.length === 0"
          class="flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          📥 批量导出
        </button>

        <button
          @click="showPreviewDialog"
          :disabled="isLoading || !hasEditableSegments || selectedContents.length === 0 || selectedFormats.length === 0"
          class="px-6 py-3 text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          👁️ 预览
        </button>
      </div>
    </div>

    <!-- 进度显示 -->
    <WorkflowProgress
      v-if="isLoading"
      :operation-name="'导出字幕'"
      :operation-status="currentOperationStatus"
      :progress-percentage="exportProgressPercent"
      :current-segment="currentSegment"
      :total-segments="totalSegments"
      :theme="'blue'"
    />

    <!-- 错误详情面板 -->
    <ErrorDetailPanel
      v-if="hasError"
      :error="currentError"
      :title="'导出过程中出现错误'"
      @retry="handleExport"
    />

    <!-- 导出完成结果 -->
    <div v-if="exportResults.length > 0 && !isLoading" class="mb-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-green-800">✅ 批量导出成功！</h4>
          <button
            @click="toggleExportDetails"
            class="text-xs text-green-600 hover:text-green-800 focus:outline-none"
          >
            {{ showExportDetails ? '隐藏详情' : '查看详情' }} {{ showExportDetails ? '▲' : '▼' }}
          </button>
        </div>

        <p class="text-sm text-green-700 mb-3">已生成 {{ exportResults.length }} 个文件</p>

        <!-- 导出详情 -->
        <div v-if="showExportDetails" class="space-y-3">
          <div class="bg-white rounded-md p-3 border border-green-200">
            <div class="text-xs text-green-600 mb-2">📁 已生成文件:</div>
            <div class="space-y-1 max-h-32 overflow-y-auto">
              <div
                v-for="result in exportResults"
                :key="result.path"
                class="flex items-center justify-between text-sm"
              >
                <span class="text-gray-700 font-mono">{{ result.filename }}</span>
                <span class="text-gray-500">({{ result.size }})</span>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-between text-xs text-green-600">
            <span>📂 保存位置: {{ exportResults[0]?.directory }}</span>
            <span>📊 总大小: {{ totalExportSize }}</span>
          </div>

          <div class="flex space-x-2">
            <button
              @click="openExportFolder"
              class="inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              📂 打开文件夹
            </button>
            <button
              @click="copyExportPaths"
              class="inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              📋 复制路径
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 可折叠的导出日志 -->
    <CollapsibleLog
      v-if="subtitlerStore.progressUpdates.length > 0"
      title="导出日志"
      :logs="formattedLogs"
      :initial-expanded="false"
      :show-stats="true"
      @clear="clearLogs"
      @copy="handleLogCopy"
    />

  </div>

  <!-- 新任务按钮 - 最下方正中间 -->
  <div
    v-if="showNextStepAction"
    class="mt-8 flex justify-center"
  >
    <button
      @click="finishWorkflow"
      class="px-8 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors text-base shadow-lg"
    >
      🔄 新任务
    </button>
  </div>

  <!-- 预览对话框 -->
  <div
    v-if="showPreviewModal"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click="closePreviewDialog"
  >
    <div
      class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden"
      @click.stop
    >
      <!-- 对话框头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">👁️ 字幕预览</h3>
        <button
          @click="closePreviewDialog"
          class="text-gray-400 hover:text-gray-600 focus:outline-none"
        >
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- 预览选择 -->
      <div class="p-6 border-b border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 内容类型选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">预览内容类型:</label>
            <select
              v-model="previewContent"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option
                v-for="content in selectedContents"
                :key="content"
                :value="content"
              >
                {{ getContentLabel(content) }}
              </option>
            </select>
          </div>

          <!-- 格式选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">预览格式:</label>
            <select
              v-model="previewFormat"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option
                v-for="format in selectedFormats"
                :key="format"
                :value="format"
              >
                {{ formatOptions.find(f => f.value === format)?.label || format }}
              </option>
            </select>
          </div>
        </div>

        <div class="mt-4 flex justify-center">
          <button
            @click="generatePreview"
            class="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors"
          >
            🔄 生成预览
          </button>
        </div>
      </div>

      <!-- 预览内容 -->
      <div class="p-6 overflow-y-auto max-h-96">
        <div v-if="previewText" class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-gray-700">
              预览: {{ getContentLabel(previewContent) }} - {{ formatOptions.find(f => f.value === previewFormat)?.label }}
            </h4>
            <span class="text-xs text-gray-500">显示前10条，总共{{ subtitlerStore.editableSegments?.length || 0 }}条</span>
          </div>
          <pre class="text-sm text-gray-800 whitespace-pre-wrap font-mono bg-white border border-gray-200 rounded p-3 overflow-x-auto">{{ previewText }}</pre>
        </div>
        <div v-else class="text-center text-gray-500 py-8">
          <p>请选择内容类型和格式，然后点击"生成预览"</p>
        </div>
      </div>

      <!-- 对话框底部 -->
      <div class="flex justify-end space-x-3 p-6 border-t border-gray-200">
        <button
          @click="closePreviewDialog"
          class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-medium transition-colors"
        >
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted, watch } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import CollapsibleLog from '@/vue/components/common/CollapsibleLog.vue';
import WorkflowProgress from '@/vue/components/subtitler/WorkflowProgress.vue';
import ErrorDetailPanel from '@/vue/components/common/ErrorDetailPanel.vue';
import { OPERATION_STATUS } from '@/vue/constants/operationStatus';

const subtitlerStore = useSubtitlerStore();

// 响应式数据
const selectedContents = ref(['source_only']); // 默认选择仅原文
const selectedFormats = ref(['srt']); // 默认选择SRT格式
const showExportDetails = ref(false);
const exportProgress = ref(0);
const exportProgressText = ref('');
const exportStatus = ref([]);
const exportResults = ref([]);

// 新增的响应式数据
const useDefaultPath = ref(false); // 是否使用默认路径
const showPreviewModal = ref(false); // 是否显示预览对话框
const previewContent = ref('source_only'); // 预览内容类型
const previewFormat = ref('srt'); // 预览格式
const previewText = ref(''); // 预览文本内容

// 双语样式设置
const bilingualSettings = reactive({
  separator: 'newline',
  originalFont: '16px',
  translationFont: '14px',
  addLanguageLabels: false
});

// 内容选择选项 (根据翻译状态动态调整)
const contentOptions = computed(() => {
  const hasTranslation = subtitlerStore.translatedSubtitles?.length > 0;
  const languages = getActualLanguages();

  const options = [
    {
      value: 'source_only',
      label: `仅原文 (${languages.sourceName})`,
      description: '只包含原始语言内容'
    }
  ];

  if (hasTranslation) {
    options.push(
      {
        value: 'target_only',
        label: `仅译文 (${languages.targetName})`,
        description: '只包含翻译语言内容'
      },
      {
        value: 'source_first',
        label: `原文在上 (${languages.sourceName}在上，${languages.targetName}在下)`,
        description: '原文在上，译文在下'
      },
      {
        value: 'target_first',
        label: `译文在上 (${languages.targetName}在上，${languages.sourceName}在下)`,
        description: '译文在上，原文在下'
      }
    );
  }

  return options;
});

// 格式选择选项
const formatOptions = computed(() => [
  {
    value: 'srt',
    label: 'SRT',
    extension: '.srt',
    description: '最常用的字幕格式'
  },
  {
    value: 'vtt',
    label: 'VTT',
    extension: '.vtt',
    description: 'Web视频字幕格式'
  },
  {
    value: 'ass',
    label: 'ASS',
    extension: '.ass',
    description: '高级字幕格式'
  },
  {
    value: 'txt',
    label: 'TXT',
    extension: '.txt',
    description: '纯文本格式'
  }
]);


// 计算属性
const isLoading = computed(() => subtitlerStore.isLoading);
const hasEditableSegments = computed(() => subtitlerStore.hasEditableSegments);

// 新增计算属性
const currentOperationStatus = computed(() => subtitlerStore.currentOperationStatus || OPERATION_STATUS.IDLE);
const hasError = computed(() => subtitlerStore.lastError && (currentOperationStatus.value === OPERATION_STATUS.ERROR || currentOperationStatus.value === OPERATION_STATUS.CANCELED));
const currentError = computed(() => subtitlerStore.lastError || {});
const currentSegment = computed(() => subtitlerStore.currentSegmentIndex || 0);
const totalSegments = computed(() => subtitlerStore.totalSegments || (subtitlerStore.editableSegments ? subtitlerStore.editableSegments.length : 0));
const exportProgressPercent = computed(() => subtitlerStore.exportProgress || 0);

// 语言代码到名称的映射
const getLanguageName = (code) => {
  const languages = {
    'zh': '中文 (简体)',
    'zh-TW': '中文 (繁体)',
    'en': 'English',
    'ja': '日本語',
    'ko': '한국어',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'ru': 'Русский',
    'ar': 'العربية'
  };
  return languages[code] || code;
};

// 动态检测实际的源语言和目标语言
const getActualLanguages = () => {
  // 检查翻译设置
  const translationSettings = subtitlerStore.translationSettings;

  if (translationSettings && translationSettings.targetLanguage) {
    // 如果有翻译设置，使用翻译设置中的语言
    const sourceLanguage = translationSettings.sourceLanguage || 'en'; // 默认原文为英文
    const targetLanguage = translationSettings.targetLanguage;

    return {
      source: sourceLanguage,
      target: targetLanguage,
      sourceName: getLanguageName(sourceLanguage),
      targetName: getLanguageName(targetLanguage)
    };
  }

  // 如果没有翻译设置，尝试从字幕内容推断
  if (subtitlerStore.editableSegments && subtitlerStore.editableSegments.length > 0) {
    const firstSegment = subtitlerStore.editableSegments[0];

    // 检查是否有翻译内容
    if (firstSegment.translatedText || firstSegment.translation) {
      // 有翻译内容，尝试检测语言
      const originalText = firstSegment.text || '';
      const translatedText = firstSegment.translatedText || firstSegment.translation || '';

      // 简单的语言检测（基于字符特征）
      const detectLanguage = (text) => {
        if (/[\u4e00-\u9fff]/.test(text)) return 'zh'; // 中文
        if (/[a-zA-Z]/.test(text)) return 'en'; // 英文
        if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'ja'; // 日文
        if (/[\uac00-\ud7af]/.test(text)) return 'ko'; // 韩文
        return 'en'; // 默认英文
      };

      const sourceLanguage = detectLanguage(originalText);
      const targetLanguage = detectLanguage(translatedText);

      return {
        source: sourceLanguage,
        target: targetLanguage,
        sourceName: getLanguageName(sourceLanguage),
        targetName: getLanguageName(targetLanguage)
      };
    }
  }

  // 默认情况
  return {
    source: 'en',
    target: 'zh',
    sourceName: 'English',
    targetName: '中文 (简体)'
  };
};

const languageSettings = computed(() => {
  const hasTranslation = subtitlerStore.translatedSubtitles?.length > 0;
  const languages = getActualLanguages();

  if (hasTranslation) {
    return `原文: ${languages.sourceName} | 译文: ${languages.targetName}`;
  } else {
    return `仅${languages.sourceName}`;
  }
});

const totalFiles = computed(() => {
  return selectedContents.value.length * selectedFormats.value.length;
});

const previewFiles = computed(() => {
  const files = [];
  const baseFilename = subtitlerStore.getUploadedFileName?.replace(/\.[^/.]+$/, '') || 'video';

  for (const content of selectedContents.value) {
    for (const format of selectedFormats.value) {
      const contentLabel = getContentLabel(content);
      const extension = formatOptions.value.find(f => f.value === format)?.extension || '.srt';
      files.push(`${baseFilename}_${contentLabel}${extension}`);
    }
  }

  return files;
});

const estimatedSize = computed(() => {
  // 估算文件大小
  const avgSegmentLength = 50; // 平均每个字幕段50字符
  const segmentCount = subtitlerStore.editableSegments?.length || 0;
  const totalChars = segmentCount * avgSegmentLength * selectedContents.value.length;
  const sizeInBytes = totalChars * 2; // UTF-8编码大约2字节/字符

  if (sizeInBytes < 1024) return `${sizeInBytes}B`;
  if (sizeInBytes < 1024 * 1024) return `${(sizeInBytes / 1024).toFixed(1)}KB`;
  return `${(sizeInBytes / 1024 / 1024).toFixed(1)}MB`;
});

const showBilingualSettings = computed(() => {
  return selectedContents.value.some(content =>
    content === 'source_first' || content === 'target_first'
  );
});

const totalExportSize = computed(() => {
  if (exportResults.value.length === 0) return '0B';

  const totalBytes = exportResults.value.reduce((sum, result) => {
    const sizeMatch = result.size.match(/(\d+(?:\.\d+)?)\s*([KMGT]?B)/i);
    if (sizeMatch) {
      const value = parseFloat(sizeMatch[1]);
      const unit = sizeMatch[2].toUpperCase();
      const multipliers = { 'B': 1, 'KB': 1024, 'MB': 1024*1024, 'GB': 1024*1024*1024 };
      return sum + (value * (multipliers[unit] || 1));
    }
    return sum;
  }, 0);

  if (totalBytes < 1024) return `${totalBytes}B`;
  if (totalBytes < 1024 * 1024) return `${(totalBytes / 1024).toFixed(1)}KB`;
  return `${(totalBytes / 1024 / 1024).toFixed(1)}MB`;
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => ({
    timestamp: new Date().toISOString(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
  }));
});

const showNextStepAction = computed(() => {
  return exportResults.value.length > 0 && !isLoading.value;
});

// 方法
const formatTime = (timeMs) => {
  if (typeof timeMs === 'number') {
    // 处理时间戳（毫秒）
    const totalSeconds = Math.floor(timeMs / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  } else if (timeMs instanceof Date) {
    // 处理Date对象
    return timeMs.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } else {
    return '00:00';
  }
};

const getContentLabel = (contentValue) => {
  const labelMap = {
    'source_only': '仅原文',
    'target_only': '仅译文',
    'source_first': '原文在上',
    'target_first': '译文在上'
  };
  return labelMap[contentValue] || contentValue;
};

const getStatusClass = (status) => {
  const classMap = {
    'completed': 'text-green-600',
    'processing': 'text-blue-600',
    'waiting': 'text-gray-600',
    'error': 'text-red-600'
  };
  return classMap[status] || 'text-gray-600';
};

const getStatusIcon = (status) => {
  const iconMap = {
    'completed': '✅',
    'processing': '🔄',
    'waiting': '⏳',
    'error': '❌'
  };
  return iconMap[status] || '';
};

const handlePrerequisiteAction = (action) => {
  if (action.key === 'goto-edit') {
    subtitlerStore.setCurrentStep(3); // 前往字幕编辑
  }
};

const handleBatchExport = async () => {
  if (selectedContents.value.length === 0) {
    alert('请至少选择一个内容类型');
    return;
  }

  if (selectedFormats.value.length === 0) {
    alert('请至少选择一个导出格式');
    return;
  }

  // 重置导出状态
  exportProgress.value = 0;
  exportProgressText.value = '准备导出...';
  exportStatus.value = [];
  exportResults.value = [];

  // 生成导出任务列表
  const exportTasks = [];
  const baseFilename = subtitlerStore.getUploadedFileName?.replace(/\.[^/.]+$/, '') || 'video';

  for (const content of selectedContents.value) {
    for (const format of selectedFormats.value) {
      const contentLabel = getContentLabel(content);
      const extension = formatOptions.value.find(f => f.value === format)?.extension || '.srt';
      const filename = `${baseFilename}_${contentLabel}${extension}`;

      exportTasks.push({
        content,
        format,
        filename,
        status: 'waiting'
      });
    }
  }

  exportStatus.value = exportTasks.map(task => ({
    file: task.filename,
    status: 'waiting'
  }));

  try {
    // 模拟批量导出过程
    for (let i = 0; i < exportTasks.length; i++) {
      const task = exportTasks[i];

      // 更新当前任务状态
      exportStatus.value[i].status = 'processing';
      exportProgressText.value = `正在生成 ${task.filename}...`;
      exportProgress.value = (i / exportTasks.length) * 100;

      // 模拟导出延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 设置导出参数
      subtitlerStore.setExportFormat(task.format);
      subtitlerStore.setExportFilename(task.filename.replace(/\.[^/.]+$/, '')); // 移除扩展名

      // 根据内容类型设置导出源
      const contentSourceMap = {
        'source_only': 'editable_segments',
        'target_only': 'translation_result',
        'source_first': 'editable_segments',
        'target_first': 'editable_segments'
      };
      subtitlerStore.setExportContentSource(contentSourceMap[task.content] || 'editable_segments');

      // 根据内容类型设置布局 (使用后端期望的中文布局名称)
      const layoutMap = {
        'source_only': '仅原文',
        'target_only': '仅译文',
        'source_first': '原文在上',
        'target_first': '译文在上'
      };
      subtitlerStore.setExportLayout(layoutMap[task.content] || '仅原文');

      try {
        // 设置是否使用默认路径
        subtitlerStore.setExportAutoSaveToDefault(useDefaultPath.value);

        // 调用实际的导出方法
        await subtitlerStore.exportSubtitles({
          filename: task.filename.replace(/\.[^/.]+$/, '') // 移除扩展名
        });

        // 检查是否导出成功
        if (subtitlerStore.lastExportPath) {
          exportStatus.value[i].status = 'completed';
          exportResults.value.push({
            filename: task.filename,
            path: subtitlerStore.lastExportPath,
            size: '未知',
            directory: subtitlerStore.lastExportPath.substring(0, subtitlerStore.lastExportPath.lastIndexOf('/'))
          });
        } else {
          exportStatus.value[i].status = 'error';
        }
      } catch (error) {
        console.error(`导出 ${task.filename} 失败:`, error);
        exportStatus.value[i].status = 'error';
      }
    }

    exportProgress.value = 100;
    exportProgressText.value = '导出完成！';

  } catch (error) {
    console.error('批量导出失败:', error);
    exportProgressText.value = '导出失败';
  }
};

// 显示预览对话框
const showPreviewDialog = () => {
  // 初始化预览选项
  previewContent.value = selectedContents.value[0] || 'source_only';
  previewFormat.value = selectedFormats.value[0] || 'srt';
  previewText.value = '';
  showPreviewModal.value = true;
};

// 关闭预览对话框
const closePreviewDialog = () => {
  showPreviewModal.value = false;
  previewText.value = '';
};

// 生成预览内容
const generatePreview = () => {
  if (!subtitlerStore.editableSegments || subtitlerStore.editableSegments.length === 0) {
    previewText.value = '没有可预览的字幕内容';
    return;
  }

  const segments = subtitlerStore.editableSegments.slice(0, 10); // 只预览前10条
  let content = '';

  // 根据格式生成不同的预览内容
  switch (previewFormat.value) {
    case 'srt':
      content = generateSRTPreview(segments);
      break;
    case 'vtt':
      content = generateVTTPreview(segments);
      break;
    case 'ass':
      content = generateASSPreview(segments);
      break;
    case 'txt':
      content = generateTXTPreview(segments);
      break;
    default:
      content = generateSRTPreview(segments);
  }

  previewText.value = content;
};

// 生成SRT格式预览
const generateSRTPreview = (segments) => {
  return segments.map((segment, index) => {
    const startTime = formatTimeForSRT(segment.startTimeMs);
    const endTime = formatTimeForSRT(segment.endTimeMs);
    const text = getSegmentTextByContent(segment);
    return `${index + 1}\n${startTime} --> ${endTime}\n${text}\n`;
  }).join('\n');
};

// 生成VTT格式预览
const generateVTTPreview = (segments) => {
  let content = 'WEBVTT\n\n';
  content += segments.map((segment, index) => {
    const startTime = formatTimeForVTT(segment.startTimeMs);
    const endTime = formatTimeForVTT(segment.endTimeMs);
    const text = getSegmentTextByContent(segment);
    return `${startTime} --> ${endTime}\n${text}\n`;
  }).join('\n');
  return content;
};

// 生成ASS格式预览
const generateASSPreview = (segments) => {
  let content = '[Script Info]\nTitle: 字幕预览\n\n[V4+ Styles]\nFormat: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\nStyle: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1\n\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n';

  content += segments.map((segment) => {
    const startTime = formatTimeForASS(segment.startTimeMs);
    const endTime = formatTimeForASS(segment.endTimeMs);
    const text = getSegmentTextByContent(segment);
    return `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,${text}`;
  }).join('\n');

  return content;
};

// 生成TXT格式预览
const generateTXTPreview = (segments) => {
  return segments.map((segment) => {
    const text = getSegmentTextByContent(segment);
    return text;
  }).join('\n');
};

// 根据内容类型获取字幕文本
const getSegmentTextByContent = (segment) => {
  // 获取原文和译文
  const originalText = segment.text || '';
  let translatedText = '';

  // 尝试从多个可能的字段获取翻译文本
  if (segment.translatedText) {
    translatedText = segment.translatedText;
  } else if (segment.translation) {
    translatedText = segment.translation;
  } else if (subtitlerStore.translatedSubtitles && subtitlerStore.translatedSubtitles.length > 0) {
    // 从翻译结果中查找对应的翻译
    const translatedSegment = subtitlerStore.translatedSubtitles.find(ts =>
      ts.startTimeMs === segment.startTimeMs && ts.endTimeMs === segment.endTimeMs
    );
    if (translatedSegment) {
      translatedText = translatedSegment.text || translatedSegment.translatedText || '';
    }
  }

  // 根据内容类型返回相应的文本
  switch (previewContent.value) {
    case 'source_only':
      return originalText;
    case 'target_only':
      return translatedText || originalText; // 如果没有翻译，显示原文
    case 'source_first':
      if (translatedText) {
        return `${originalText}\n${translatedText}`;
      } else {
        return originalText;
      }
    case 'target_first':
      if (translatedText) {
        return `${translatedText}\n${originalText}`;
      } else {
        return originalText;
      }
    default:
      return originalText;
  }
};

// SRT时间格式化
const formatTimeForSRT = (timeMs) => {
  const totalSeconds = Math.floor(timeMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const milliseconds = timeMs % 1000;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

// VTT时间格式化
const formatTimeForVTT = (timeMs) => {
  const totalSeconds = Math.floor(timeMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const milliseconds = timeMs % 1000;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
};

// ASS时间格式化
const formatTimeForASS = (timeMs) => {
  const totalSeconds = Math.floor(timeMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const centiseconds = Math.floor((timeMs % 1000) / 10);

  return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${centiseconds.toString().padStart(2, '0')}`;
};

const toggleExportDetails = () => {
  showExportDetails.value = !showExportDetails.value;
};

const openExportFolder = () => {
  if (exportResults.value.length > 0) {
    const directory = exportResults.value[0].directory;
    // 调用系统方法打开文件夹
    window.electronAPI?.openFolder(directory);
  }
};

const copyExportPaths = async () => {
  const paths = exportResults.value.map(result => result.path).join('\n');
  try {
    await navigator.clipboard.writeText(paths);
    console.log('路径已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
  }
};

const finishWorkflow = () => {
  // 完成工作流程，重置到初始状态
  subtitlerStore.resetWorkflow();
  subtitlerStore.setCurrentStep(1);
};

const clearLogs = () => {
  subtitlerStore.progressUpdates = [];
};

const handleLogCopy = (logText) => {
  console.log('日志已复制:', logText);
};
</script>

<style scoped>
/* Scoped styles for ExportStep.vue */
.list-disc li::marker {
  color: #60a5fa; /* blue-400 for markers */
}
.bg-gray-750 {
    background-color: #374151; /* A bit darker than gray-700 for contrast */
}
</style>