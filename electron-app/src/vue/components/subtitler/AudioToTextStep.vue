<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 3: 音频转文字</h3>
      <p class="text-sm text-gray-500 mt-1">使用语音识别将音频转换为文本</p>
    </div>

    <!-- 当前处理状态 -->
    <ProcessStateDisplay :process-state="subtitlerStore.currentProcessState" />
    
    <!-- 前置条件警告 -->
    <StatusMessage
      v-if="!hasAudioSource"
      type="warning"
      title="需要音频文件"
      message="请先完成前面的步骤获取音频文件"
      :actions="[
        { key: 'goto-video-audio', label: '前往音频提取', icon: '🎵', primary: true }
      ]"
      @action="handlePrerequisiteAction"
    />

    <!-- 工作流进度 -->
    <WorkflowProgress
      v-if="hasAudioSource"
      title="语音识别进度"
      :current-workflow="currentWorkflow"
      :overall-progress="overallProgress"
      :current-stage="currentStage"
      :stages="workflowStages"
      :error-detail="errorDetail"
      :error-title="errorTitle"
      :error-actions="errorActions"
      :actions="workflowActions"
      @action="handleWorkflowAction"
      @error-action="handleErrorAction"
      class="mb-6"
    />

    <!-- 音频源选择 -->
    <div v-if="hasAudioSource && !isLoading && !currentWorkflow" class="audio-source-selection mb-6">
      <div class="bg-blue-50 rounded-lg border border-blue-200 p-4">
        <h4 class="text-sm font-medium text-blue-800 mb-2">音频源选择</h4>

        <div class="flex items-center mb-4">
          <input
            id="usePreviousAudio"
            v-model="usePreviousAudio"
            type="checkbox"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label for="usePreviousAudio" class="ml-2 block text-sm text-gray-700">
            使用上一步提取的音频 ({{ previousAudioFileName }})
          </label>
        </div>

        <div v-if="!usePreviousAudio" class="mb-4">
          <label for="audioFile" class="block text-sm font-medium text-gray-700 mb-1">
            或选择其他音频文件:
          </label>
          <input
            type="file"
            id="audioFile"
            @change="handleAudioFileChange"
            accept="audio/*"
            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>
      </div>
    </div>

    <!-- 转录设置 -->
    <div v-if="hasAudioSource && !isLoading && !currentWorkflow" class="transcription-settings mb-6">
      <div class="bg-gray-50 rounded-lg border border-gray-200 p-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">转录设置</h4>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 语言选择 -->
          <div>
            <label for="language" class="block text-sm font-medium text-gray-700 mb-1">
              音频语言
          </label>
          <select
              id="language"
              v-model="transcriptionSettings.language"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="zh">中文</option>
              <option value="en">英语</option>
            <option value="auto">自动检测</option>
          </select>
        </div>

          <!-- 模型选择 -->
          <div>
            <label for="model" class="block text-sm font-medium text-gray-700 mb-1">
              识别模型
          </label>
            <select
              id="model"
              v-model="transcriptionSettings.model"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
            >
              <option value="whisper-1">Whisper (标准)</option>
              <option value="whisper-large">Whisper Large (高精度)</option>
            </select>
          </div>
          
          <!-- 高级设置 -->
          <div class="md:col-span-2">
            <div class="flex items-center mb-2">
              <button 
                @click="showAdvancedSettings = !showAdvancedSettings" 
                class="text-sm text-blue-600 hover:text-blue-800 focus:outline-none flex items-center"
              >
                <svg 
                  class="h-4 w-4 mr-1 transition-transform" 
                  :class="{'transform rotate-90': showAdvancedSettings}"
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
                高级设置
              </button>
            </div>
            
            <div v-if="showAdvancedSettings" class="pl-4 border-l-2 border-blue-100 space-y-3 mt-2">
              <!-- 分段设置 -->
              <div>
                <label for="segmentLength" class="block text-sm font-medium text-gray-700 mb-1">
                  分段长度 (秒)
                </label>
            <input
                  id="segmentLength"
                  v-model.number="transcriptionSettings.segmentLength"
                  type="number"
                  min="1"
                  max="30"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
              </div>
              
              <!-- 其他高级设置可以根据需要添加 -->
            </div>
          </div>
        </div>
        </div>
      </div>

    <!-- 转录操作 -->
    <div v-if="hasAudioSource && !isLoading && !currentWorkflow" class="transcription-actions mb-6">
      <div class="flex space-x-3">
        <button
          @click="handleProcessAudioToText"
          :disabled="!hasValidAudioSource || audioToTextResult"
          class="flex-1 px-6 py-3 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          🎙️ {{ audioToTextResult ? '已完成转录' : '开始语音转文字' }}
        </button>

        <button
          v-if="audioToTextResult"
          @click="goToNextStep"
          class="px-6 py-3 text-indigo-700 bg-indigo-100 rounded-lg hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium transition-colors text-base"
        >
          ➡️ 下一步
        </button>
      </div>
    </div>

    <!-- 结果显示 -->
    <div v-if="audioToTextResult && !isLoading" class="result-section bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
      <div class="flex items-start">
        <div class="flex-shrink-0 mt-0.5">
          <svg class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <div class="ml-3">
          <h4 class="text-sm font-medium text-green-800">转录完成</h4>
          <div class="mt-2 text-sm text-green-700">
            <p>音频已成功转录为文本，共识别 {{ recognizedSegmentsCount }} 个片段</p>
          </div>
          <div class="mt-3">
            <button
              @click="goToNextStep"
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              继续下一步
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志显示 -->
    <div v-if="formattedLogs.length > 0" class="log-section mt-6">
      <div class="flex items-center justify-between mb-2">
        <h4 class="text-sm font-medium text-gray-700">处理日志</h4>
          <button
          @click="toggleLogs" 
          class="text-xs text-gray-500 hover:text-gray-700 focus:outline-none"
          >
          {{ showLogs ? '隐藏日志' : '显示日志' }}
          </button>
        </div>
      <div v-if="showLogs" class="bg-gray-50 border border-gray-200 rounded-lg p-3 max-h-60 overflow-y-auto">
        <div 
          v-for="(log, index) in formattedLogs" 
          :key="index"
          :class="[
            'text-xs font-mono py-1 border-b border-gray-100 last:border-0',
            log.level === 'error' ? 'text-red-600' :
            log.level === 'warning' ? 'text-yellow-600' :
            log.level === 'success' ? 'text-green-600' : 'text-gray-600'
          ]"
        >
          <span class="text-gray-400 mr-2">{{ log.timestamp.split('T')[1].split('.')[0] }}</span>
          <span>{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import WorkflowProgress from '@/vue/components/subtitler/WorkflowProgress.vue';
import ProcessStateDisplay from '@/vue/components/common/ProcessStateDisplay.vue';

const subtitlerStore = useSubtitlerStore();

// 状态变量
const isLoading = computed(() => subtitlerStore.isLoading);
const uploadedFile = computed(() => subtitlerStore.uploadedFile);
const videoToAudioResult = computed(() => subtitlerStore.videoToAudioResult);
const audioToTextResult = computed(() => subtitlerStore.audioToTextResult);
const audioToTextError = computed(() => subtitlerStore.audioToTextError);
const audioToTextProgress = computed(() => subtitlerStore.audioToTextProgress);
const showAdvancedSettings = ref(false);
const showLogs = ref(false);

// 工作流状态
const currentWorkflow = ref(null);
const overallProgress = ref(0);
const currentStage = ref(null);
const workflowStages = ref([]);
const errorDetail = ref(null);
const errorTitle = ref('语音识别过程中发生错误');
const errorActions = ref([
  { key: 'retry', label: '重试', primary: true },
  { key: 'skip', label: '跳过' }
]);

// 转录设置
const transcriptionSettings = ref({
  language: 'zh',
  model: 'whisper-1',
  segmentLength: 5
});

// 工作流操作按钮
const workflowActions = computed(() => {
  if (!currentWorkflow.value) return [];
  
  const actions = [];
  
  if (currentWorkflow.value.status === 'in_progress') {
    actions.push({ key: 'cancel', label: '取消', primary: false });
  } else if (currentWorkflow.value.status === 'completed') {
    actions.push({ key: 'next', label: '下一步', primary: true });
  } else if (currentWorkflow.value.status === 'failed') {
    actions.push({ key: 'retry', label: '重试', primary: true });
    actions.push({ key: 'skip', label: '跳过', primary: false });
  }
  
  return actions;
});

// 计算属性
const hasAudioSource = computed(() => {
  return videoToAudioResult.value || (uploadedFile.value && uploadedFile.value.type && uploadedFile.value.type.startsWith('audio/'));
});

const hasValidAudioSource = computed(() => {
  return usePreviousAudio.value ? !!videoToAudioResult.value : !!uploadedFile.value;
});

const audioSourcePath = computed(() => {
  if (usePreviousAudio.value && videoToAudioResult.value) {
    return videoToAudioResult.value;
  }
  return uploadedFile.value?.path;
});

const previousAudioFileName = computed(() => {
  if (!videoToAudioResult.value) return '';
  const fileName = videoToAudioResult.value.split('/').pop() || videoToAudioResult.value;
  return fileName;
});

const usePreviousAudio = computed({
  get: () => subtitlerStore.usePreviousAudioForTranscription,
  set: (value) => {
    subtitlerStore.setUsePreviousAudioForTranscription(value);
  }
});

const recognizedSegmentsCount = computed(() => {
  if (!subtitlerStore.editableSegments) return 0;
  return subtitlerStore.editableSegments.length;
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => {
    // 如果update是新格式的对象
    if (typeof update === 'object' && update.stageName) {
      return {
        timestamp: update.timestamp || Date.now(),
        level: update.isError ? 'error' :
               update.status === 'SUCCESS' ? 'success' :
               (update.status === 'FAILURE' || update.status === 'ERROR') ? 'error' :
               update.status === 'PARTIAL_SUCCESS' ? 'warning' :
               update.message?.includes('✅') ? 'success' :
               update.message?.includes('❌') ? 'error' :
               update.message?.includes('⚠️') ? 'warning' : 'info',
        message: `[${update.stageName}] ${update.message}${update.traceId ? ` (${update.traceId.slice(-8)})` : ''}`,
        traceId: update.traceId,
        status: update.status,
        errorDetail: update.errorDetail
      };
    }
    // 兼容旧格式的字符串
    return {
      timestamp: Date.now(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
    };
  });
});

// 方法
const handleAudioFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    subtitlerStore.setUploadedFile(file);
    subtitlerStore.setUsePreviousAudioForTranscription(false);
  }
};

const handleProcessAudioToText = async () => {
  if (!hasValidAudioSource.value) return;
  
  try {
    // 重置错误状态
    errorDetail.value = null;
    
    // 设置工作流状态
    currentWorkflow.value = {
      id: 'audioToText',
      name: '音频转文字',
      status: 'in_progress'
    };
    
    // 设置初始阶段
    workflowStages.value = [
      { 
        id: 'init', 
        name: '初始化', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '准备处理音频文件'
      },
      { 
        id: 'transcribe', 
        name: '语音识别', 
        status: 'in_progress', 
        isActive: true,
        progress: 0,
        message: '正在进行语音识别'
      },
      { 
        id: 'segment', 
        name: '分段处理', 
        status: 'pending', 
        isActive: false,
        progress: 0,
        message: '等待处理文本分段'
      }
    ];
    
    // 设置当前阶段
    currentStage.value = workflowStages.value[1];
    
    // 监听进度更新
    const progressListener = (progress) => {
      if (progress.percentage !== undefined) {
        overallProgress.value = progress.percentage;
        
        // 更新当前阶段进度
        if (currentStage.value) {
          currentStage.value.progress = progress.percentage;
  }

        // 更新阶段状态
        if (progress.stage) {
          const stage = workflowStages.value.find(s => s.id === progress.stage);
          if (stage) {
            // 将之前的活动阶段设为非活动
            workflowStages.value.forEach(s => {
              if (s.id !== progress.stage) {
                s.isActive = false;
                if (s.status === 'in_progress') {
                  s.status = 'completed';
  }
              }
            });
            
            // 更新当前阶段
            stage.isActive = true;
            stage.status = 'in_progress';
            stage.message = progress.message || stage.message;
            currentStage.value = stage;
  }
        }
        
        // 更新阶段消息
        if (progress.message && currentStage.value) {
          currentStage.value.message = progress.message;
        }
      }
    };
    
    // 调用处理方法
    const result = await subtitlerStore.processAudioToText({
      audioPath: audioSourcePath.value,
      settings: transcriptionSettings.value,
      onProgress: progressListener
    });
    
    // 处理成功
    currentWorkflow.value.status = 'completed';
    overallProgress.value = 100;
    
    // 更新所有阶段为完成
    workflowStages.value.forEach(stage => {
      stage.status = 'completed';
      stage.progress = 100;
      stage.isActive = false;
    });

    return result;
  } catch (error) {
    // 处理错误
    currentWorkflow.value.status = 'failed';
    
    // 更新当前阶段为失败
    if (currentStage.value) {
      currentStage.value.status = 'failed';
      currentStage.value.message = error.message || '处理失败';
    }
    
    // 设置错误详情
    errorDetail.value = {
      userMessage: error.message || '语音识别处理失败',
      errorCode: error.code || 'PROCESSING_ERROR',
      technicalMessage: error.stack || error.toString(),
      context: {
        audioSource: usePreviousAudio.value ? '上一步提取的音频' : '用户上传的音频',
        fileName: usePreviousAudio.value ? previousAudioFileName.value : uploadedFile.value?.name,
        settings: JSON.stringify(transcriptionSettings.value)
      }
    };
    
    console.error('[AudioToTextStep] Error:', error);
  }
};

const handleWorkflowAction = (action) => {
  if (action.key === 'retry') {
    handleProcessAudioToText();
  } else if (action.key === 'next' || action.key === 'skip') {
    goToNextStep();
  } else if (action.key === 'cancel') {
    // 取消处理
    subtitlerStore.cancelCurrentOperation();
    currentWorkflow.value.status = 'cancelled';
    
    // 更新当前阶段为取消
    if (currentStage.value) {
      currentStage.value.status = 'cancelled';
      currentStage.value.message = '操作已取消';
    }
  }
};

const handleErrorAction = (action) => {
  handleWorkflowAction(action);
};

const handlePrerequisiteAction = (action) => {
  if (action.key === 'goto-video-audio') {
    subtitlerStore.setCurrentStep(2);
  }
};

const toggleLogs = () => {
  showLogs.value = !showLogs.value;
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(4);
};

// 生命周期钩子
onMounted(() => {
  // 如果已有处理结果，更新工作流状态
  if (audioToTextResult.value) {
    currentWorkflow.value = {
      id: 'audioToText',
      name: '音频转文字',
      status: 'completed'
    };
    
    overallProgress.value = 100;
    
    workflowStages.value = [
      { 
        id: 'init', 
        name: '初始化', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '准备处理音频文件'
      },
      { 
        id: 'transcribe', 
        name: '语音识别', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '语音识别完成'
      },
      { 
        id: 'segment', 
        name: '分段处理', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '文本分段处理完成'
      }
    ];
  }
  
  // 如果有错误，更新错误详情
  if (audioToTextError.value) {
    errorDetail.value = {
      userMessage: audioToTextError.value,
      errorCode: 'PROCESSING_ERROR',
      technicalMessage: audioToTextError.value,
      context: {
        audioSource: usePreviousAudio.value ? '上一步提取的音频' : '用户上传的音频',
        fileName: usePreviousAudio.value ? previousAudioFileName.value : uploadedFile.value?.name
      }
    };
    
    if (currentWorkflow.value) {
      currentWorkflow.value.status = 'failed';
    }
  }
  
  // 默认使用上一步提取的音频
  if (videoToAudioResult.value) {
    subtitlerStore.setUsePreviousAudioForTranscription(true);
  }
});
</script>

<style scoped>
/* 可以添加特定样式 */
</style>