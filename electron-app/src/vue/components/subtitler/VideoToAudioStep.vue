<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 2: 视频转音频</h3>
      <p class="text-sm text-gray-500 mt-1">从视频文件中提取音频用于后续处理</p>
    </div>

    <!-- 前置条件警告 -->
    <StatusMessage
      v-if="!uploadedFile"
      type="warning"
      title="需要先上传文件"
      message="请先在步骤1中上传视频或音频文件"
      :actions="[
        { key: 'goto-upload', label: '前往上传文件', icon: '📁', primary: true }
      ]"
      @action="handlePrerequisiteAction"
    />

    <!-- 工作流进度 -->
    <WorkflowProgress
      v-if="uploadedFile"
      title="音频提取进度"
      :current-workflow="currentWorkflow"
      :overall-progress="overallProgress"
      :current-stage="currentStage"
      :stages="workflowStages"
      :error-detail="errorDetail"
      :error-title="errorTitle"
      :error-actions="errorActions"
      :actions="workflowActions"
      @action="handleWorkflowAction"
      @error-action="handleErrorAction"
      class="mb-6"
    />

    <!-- 音频提取操作 -->
    <div v-if="uploadedFile && !isLoading && !currentWorkflow" class="current-actions mb-6">
      <div class="flex space-x-3">
        <button
          @click="handleProcessVideoToAudio"
          :disabled="isLoading || videoToAudioResult"
          class="flex-1 px-6 py-3 text-white bg-purple-600 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          🎵 {{ videoToAudioResult ? '音频已提取' : '开始提取音频' }}
        </button>

        <button
          v-if="videoToAudioResult"
          @click="goToNextStep"
          class="px-6 py-3 text-purple-700 bg-purple-100 rounded-lg hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 font-medium transition-colors text-base"
        >
          ➡️ 下一步
        </button>
      </div>
    </div>

    <!-- 结果显示 -->
    <div v-if="videoToAudioResult && !isLoading" class="result-section bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
      <div class="flex items-start">
        <div class="flex-shrink-0 mt-0.5">
          <svg class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <div class="ml-3">
          <h4 class="text-sm font-medium text-green-800">音频提取成功</h4>
          <div class="mt-2 text-sm text-green-700">
            <p>{{ extractionSummary }}</p>
            <p class="mt-1">输出文件: <span class="font-medium">{{ audioFileName }}</span></p>
          </div>
          <div class="mt-3">
            <button
              @click="goToNextStep"
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              继续下一步
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志显示 -->
    <div v-if="formattedLogs.length > 0" class="log-section mt-6">
      <div class="flex items-center justify-between mb-2">
        <h4 class="text-sm font-medium text-gray-700">处理日志</h4>
        <button 
          @click="toggleLogs" 
          class="text-xs text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          {{ showLogs ? '隐藏日志' : '显示日志' }}
        </button>
      </div>
      <div v-if="showLogs" class="bg-gray-50 border border-gray-200 rounded-lg p-3 max-h-60 overflow-y-auto">
        <div 
          v-for="(log, index) in formattedLogs" 
          :key="index"
          :class="[
            'text-xs font-mono py-1 border-b border-gray-100 last:border-0',
            log.level === 'error' ? 'text-red-600' :
            log.level === 'warning' ? 'text-yellow-600' :
            log.level === 'success' ? 'text-green-600' : 'text-gray-600'
          ]"
        >
          <span class="text-gray-400 mr-2">{{ log.timestamp.split('T')[1].split('.')[0] }}</span>
          <span>{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import WorkflowProgress from '@/vue/components/subtitler/WorkflowProgress.vue';

const subtitlerStore = useSubtitlerStore();

// 状态变量
const isLoading = computed(() => subtitlerStore.isLoading);
const uploadedFile = computed(() => subtitlerStore.uploadedFile);
const videoToAudioResult = computed(() => subtitlerStore.videoToAudioResult);
const videoToAudioError = computed(() => subtitlerStore.videoToAudioError);
const videoToAudioProgress = computed(() => subtitlerStore.videoToAudioProgress);
const showLogs = ref(false);

// 工作流状态
const currentWorkflow = ref(null);
const overallProgress = ref(0);
const currentStage = ref(null);
const workflowStages = ref([]);
const errorDetail = ref(null);
const errorTitle = ref('音频提取过程中发生错误');
const errorActions = ref([
  { key: 'retry', label: '重试', primary: true },
  { key: 'skip', label: '跳过' }
]);

// 工作流操作按钮
const workflowActions = computed(() => {
  if (!currentWorkflow.value) return [];
  
  const actions = [];
  
  if (currentWorkflow.value.status === 'in_progress') {
    actions.push({ key: 'cancel', label: '取消', primary: false });
  } else if (currentWorkflow.value.status === 'completed') {
    actions.push({ key: 'next', label: '下一步', primary: true });
  } else if (currentWorkflow.value.status === 'failed') {
    actions.push({ key: 'retry', label: '重试', primary: true });
    actions.push({ key: 'skip', label: '跳过', primary: false });
  }
  
  return actions;
});

// 计算属性
const extractionSummary = computed(() => {
  if (!videoToAudioResult.value) return '';
  return '音频文件已成功从视频中提取，可以进入下一步进行语音转文字处理';
});

const audioFileName = computed(() => {
  if (!videoToAudioResult.value) return '';
  // 从路径中提取文件名
  const fileName = videoToAudioResult.value.split('/').pop() || videoToAudioResult.value;
  return fileName;
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => ({
    timestamp: new Date().toISOString(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
  }));
});

// 方法
const handleProcessVideoToAudio = async () => {
  if (!uploadedFile.value) return;
  
  try {
    // 重置错误状态
    errorDetail.value = null;
    
    // 设置工作流状态
    currentWorkflow.value = {
      id: 'videoToAudio',
      name: '视频转音频',
      status: 'in_progress'
    };
    
    // 设置初始阶段
    workflowStages.value = [
      { 
        id: 'init', 
        name: '初始化', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '准备处理视频文件'
      },
      { 
        id: 'extract', 
        name: '提取音频', 
        status: 'in_progress', 
        isActive: true,
        progress: 0,
        message: '正在从视频中提取音频轨道'
      },
      { 
        id: 'save', 
        name: '保存文件', 
        status: 'pending', 
        isActive: false,
        progress: 0,
        message: '等待保存音频文件'
      }
    ];
    
    // 设置当前阶段
    currentStage.value = workflowStages.value[1];
    
    // 监听进度更新
    const progressListener = (progress) => {
      if (progress.percentage !== undefined) {
        overallProgress.value = progress.percentage;
        
        // 更新当前阶段进度
        if (currentStage.value) {
          currentStage.value.progress = progress.percentage;
        }
        
        // 更新阶段状态
        if (progress.stage) {
          const stage = workflowStages.value.find(s => s.id === progress.stage);
          if (stage) {
            // 将之前的活动阶段设为非活动
            workflowStages.value.forEach(s => {
              if (s.id !== progress.stage) {
                s.isActive = false;
                if (s.status === 'in_progress') {
                  s.status = 'completed';
                }
              }
            });
            
            // 更新当前阶段
            stage.isActive = true;
            stage.status = 'in_progress';
            stage.message = progress.message || stage.message;
            currentStage.value = stage;
          }
        }
        
        // 更新阶段消息
        if (progress.message && currentStage.value) {
          currentStage.value.message = progress.message;
        }
      }
    };
    
    // 调用处理方法
    const result = await subtitlerStore.processVideoToAudio({
      onProgress: progressListener
    });
    
    // 处理成功
    currentWorkflow.value.status = 'completed';
    overallProgress.value = 100;
    
    // 更新所有阶段为完成
    workflowStages.value.forEach(stage => {
      stage.status = 'completed';
      stage.progress = 100;
      stage.isActive = false;
    });
    
    return result;
  } catch (error) {
    // 处理错误
    currentWorkflow.value.status = 'failed';
    
    // 更新当前阶段为失败
    if (currentStage.value) {
      currentStage.value.status = 'failed';
      currentStage.value.message = error.message || '处理失败';
    }
    
    // 设置错误详情
    errorDetail.value = {
      userMessage: error.message || '视频转音频处理失败',
      errorCode: error.code || 'PROCESSING_ERROR',
      technicalMessage: error.stack || error.toString(),
      context: {
        fileName: uploadedFile.value.name,
        fileSize: `${(uploadedFile.value.size / 1024 / 1024).toFixed(2)} MB`,
        fileType: uploadedFile.value.type
      }
    };
    
    console.error('[VideoToAudioStep] Error:', error);
  }
};

const handleWorkflowAction = (action) => {
  if (action.key === 'retry') {
    handleProcessVideoToAudio();
  } else if (action.key === 'next' || action.key === 'skip') {
    goToNextStep();
  } else if (action.key === 'cancel') {
    // 取消处理
    subtitlerStore.cancelCurrentOperation();
    currentWorkflow.value.status = 'cancelled';
    
    // 更新当前阶段为取消
    if (currentStage.value) {
      currentStage.value.status = 'cancelled';
      currentStage.value.message = '操作已取消';
    }
  }
};

const handleErrorAction = (action) => {
  handleWorkflowAction(action);
};

const handlePrerequisiteAction = (action) => {
  if (action.key === 'goto-upload') {
    subtitlerStore.setCurrentStep(1);
  }
};

const toggleLogs = () => {
  showLogs.value = !showLogs.value;
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(3);
};

// 生命周期钩子
onMounted(() => {
  // 如果已有处理结果，更新工作流状态
  if (videoToAudioResult.value) {
    currentWorkflow.value = {
      id: 'videoToAudio',
      name: '视频转音频',
      status: 'completed'
    };
    
    overallProgress.value = 100;
    
    workflowStages.value = [
      { 
        id: 'init', 
        name: '初始化', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '准备处理视频文件'
      },
      { 
        id: 'extract', 
        name: '提取音频', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '音频轨道提取完成'
      },
      { 
        id: 'save', 
        name: '保存文件', 
        status: 'completed', 
        isActive: false,
        progress: 100,
        message: '音频文件已保存'
      }
    ];
  }
  
  // 如果有错误，更新错误详情
  if (videoToAudioError.value) {
    errorDetail.value = {
      userMessage: videoToAudioError.value,
      errorCode: 'PROCESSING_ERROR',
      technicalMessage: videoToAudioError.value,
      context: {
        fileName: uploadedFile.value?.name,
        fileSize: uploadedFile.value ? `${(uploadedFile.value.size / 1024 / 1024).toFixed(2)} MB` : 'Unknown',
        fileType: uploadedFile.value?.type
      }
    };
    
    if (currentWorkflow.value) {
      currentWorkflow.value.status = 'failed';
    }
  }
});
</script>

<style scoped>
/* 可以添加特定样式 */
</style>