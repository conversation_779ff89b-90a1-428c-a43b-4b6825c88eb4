<template>
  <div class="workflow-progress">
    <!-- 主进度面板 -->
    <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
      <!-- 标题栏 -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="h-5 w-5 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 class="text-sm font-medium text-blue-800">{{ title || '工作流进度' }}</h3>
          </div>
          <div class="flex items-center">
            <OperationStatusBadge 
              v-if="currentWorkflow" 
              :status="currentWorkflow.status" 
              size="small"
            />
            <button 
              v-if="showClose" 
              @click="$emit('close')" 
              class="ml-2 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 进度内容 -->
      <div class="p-4">
        <!-- 整体进度 -->
        <div class="mb-4">
          <div class="flex items-center justify-between text-xs mb-1">
            <span class="font-medium text-gray-700">总体进度</span>
            <span class="text-gray-600">{{ Math.round(overallProgress) }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="h-2 rounded-full transition-all duration-300"
              :class="progressBarColorClass"
              :style="{ width: `${Math.min(100, Math.max(0, overallProgress))}%` }"
            />
          </div>
        </div>

        <!-- 当前阶段信息 -->
        <div v-if="currentStage" class="mb-4">
          <div class="flex items-center justify-between mb-1">
            <div class="flex items-center">
              <span class="text-xs font-medium text-gray-700">当前阶段</span>
              <OperationStatusBadge 
                v-if="currentStage.status !== undefined" 
                :status="currentStage.status" 
                size="small"
                class="ml-2"
              />
            </div>
            <span class="text-xs text-gray-500">{{ currentStage.progress }}%</span>
          </div>
          <div class="text-sm text-gray-800 mb-1">{{ currentStage.name || '处理中' }}</div>
          <div v-if="currentStage.message" class="text-xs text-gray-600">{{ currentStage.message }}</div>
        </div>

        <!-- 错误信息 -->
        <ErrorDetailPanel 
          v-if="hasError && errorDetail" 
          :errorDetail="errorDetail"
          :title="errorTitle"
          :showClose="false"
          :actions="errorActions"
          @action="handleErrorAction"
          class="mb-4"
        />

        <!-- 阶段列表 -->
        <div v-if="showStages && stages.length > 0" class="mt-4">
          <h4 class="text-xs font-medium text-gray-700 mb-2">处理阶段</h4>
          <div class="space-y-2">
            <div 
              v-for="(stage, index) in stages" 
              :key="index"
              class="flex items-center justify-between p-2 rounded-md"
              :class="{
                'bg-blue-50 border border-blue-100': stage.isActive,
                'bg-gray-50 border border-gray-100': !stage.isActive
              }"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0 mr-2">
                  <!-- 完成图标 -->
                  <svg v-if="stage.status === 'completed'" class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <!-- 进行中图标 -->
                  <svg v-else-if="stage.isActive" class="h-4 w-4 text-blue-500 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <!-- 错误图标 -->
                  <svg v-else-if="stage.status === 'failed'" class="h-4 w-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <!-- 等待图标 -->
                  <svg v-else class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div class="text-xs font-medium" :class="{'text-blue-700': stage.isActive, 'text-gray-700': !stage.isActive}">
                    {{ stage.name }}
                  </div>
                  <div v-if="stage.message" class="text-xs text-gray-500">{{ stage.message }}</div>
                </div>
              </div>
              <OperationStatusBadge 
                v-if="stage.status" 
                :status="stage.status" 
                size="small"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区 -->
      <div v-if="actions && actions.length > 0" class="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2">
        <button 
          v-for="action in actions" 
          :key="action.key"
          @click="handleAction(action)"
          :disabled="action.disabled"
          :class="[
            'px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 focus:ring-offset-2',
            action.primary 
              ? 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500 disabled:bg-gray-100 disabled:text-gray-400'
          ]"
        >
          {{ action.label }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import OperationStatusBadge from '@/vue/components/common/OperationStatusBadge.vue';
import ErrorDetailPanel from '@/vue/components/common/ErrorDetailPanel.vue';

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: false
  },
  // 当前工作流信息
  currentWorkflow: {
    type: Object,
    default: null
  },
  // 总体进度
  overallProgress: {
    type: Number,
    default: 0
  },
  // 当前阶段
  currentStage: {
    type: Object,
    default: null
  },
  // 所有阶段
  stages: {
    type: Array,
    default: () => []
  },
  // 是否显示阶段列表
  showStages: {
    type: Boolean,
    default: true
  },
  // 错误信息
  errorDetail: {
    type: Object,
    default: null
  },
  // 错误标题
  errorTitle: {
    type: String,
    default: '处理过程中发生错误'
  },
  // 错误操作
  errorActions: {
    type: Array,
    default: () => []
  },
  // 底部操作按钮
  actions: {
    type: Array,
    default: () => []
  },
  // 主题
  theme: {
    type: String,
    default: 'blue',
    validator: (value) => ['blue', 'green', 'indigo', 'purple', 'red', 'yellow', 'orange', 'pink', 'gray'].includes(value)
  }
});

const emit = defineEmits(['close', 'action', 'error-action']);

// 是否有错误
const hasError = computed(() => {
  return props.errorDetail || 
         (props.currentWorkflow && props.currentWorkflow.status === 'failed') ||
         (props.currentStage && props.currentStage.status === 'failed');
});

// 进度条颜色
const progressBarColorClass = computed(() => {
  if (hasError.value) {
    return 'bg-red-500';
  }
  
  const colorMap = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    indigo: 'bg-indigo-600',
    purple: 'bg-purple-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600',
    orange: 'bg-orange-600',
    pink: 'bg-pink-600',
    gray: 'bg-gray-600'
  };
  
  return colorMap[props.theme] || colorMap.blue;
});

// 处理操作按钮点击
const handleAction = (action) => {
  emit('action', action);
};

// 处理错误操作按钮点击
const handleErrorAction = (action) => {
  emit('error-action', action);
};
</script>

<style scoped>
.workflow-progress {
  width: 100%;
}
</style> 