<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 8: 翻译处理</h3>
      <p class="text-sm text-gray-500 mt-1">将字幕翻译成目标语言</p>
    </div>

    <!-- 当前处理状态 -->
    <ProcessStateDisplay :process-state="subtitlerStore.currentProcessState" />
    
    <!-- 前置结果摘要 -->
    <div v-if="hasEditableSegments" class="previous-results mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <svg class="h-4 w-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span class="text-sm font-medium text-green-800">翻译选择已完成</span>
        </div>
        <span class="text-xs text-green-600">目标语言: {{ getLanguageName(translationSettings.targetLanguage) }}</span>
      </div>
    </div>

    <!-- 前置条件警告 -->
    <div v-if="!hasEditableSegments" class="prerequisite-warning mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div class="flex items-start">
        <svg class="h-5 w-5 text-yellow-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <div class="flex-1">
          <h4 class="text-sm font-medium text-yellow-800">需要先完成翻译选择</h4>
          <p class="text-sm text-yellow-700 mt-1">请先在步骤7中完成翻译选择，设置翻译参数后再开始翻译</p>
          <button
            @click="handlePrerequisiteAction({ step: 7 })"
            class="mt-2 text-sm text-yellow-800 underline hover:text-yellow-900 transition-colors">
            前往翻译选择
          </button>
        </div>
      </div>
    </div>

    <!-- 当前操作区域 -->
    <div v-if="hasEditableSegments && !hasTranslatedSubtitles" class="current-actions mb-6">
      <!-- 翻译设置显示 -->
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">翻译设置</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
          <div class="bg-white p-2 rounded border">
            <span class="text-gray-600 font-medium">目标语言:</span>
            <div class="text-pink-700 font-semibold">{{ getLanguageName(translationSettings.targetLanguage) }}</div>
          </div>
          <div class="bg-white p-2 rounded border">
            <span class="text-gray-600 font-medium">翻译质量:</span>
            <div class="text-pink-700 font-semibold">{{ getQualityName(translationSettings.quality) }}</div>
          </div>
          <div class="bg-white p-2 rounded border">
            <span class="text-gray-600 font-medium">翻译风格:</span>
            <div class="text-pink-700 font-semibold">{{ getStyleName(translationSettings.style) }}</div>
          </div>
          <div class="bg-white p-2 rounded border">
            <span class="text-gray-600 font-medium">字幕数量:</span>
            <div class="text-pink-700 font-semibold">{{ editableSegmentsCount }} 个</div>
          </div>
        </div>
      </div>

      <!-- 原文预览 -->
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">原文字幕预览</h4>
        <div class="bg-white border border-gray-200 rounded-md p-3 max-h-32 overflow-y-auto">
          <div class="space-y-1">
            <div v-for="(subtitle, index) in subtitlePreview" :key="index" class="text-sm">
              <span class="text-gray-500 font-mono text-xs">{{ formatTime(subtitle.startTimeMs) }} → {{ formatTime(subtitle.endTimeMs) }}</span>
              <span class="text-gray-700 ml-2">{{ subtitle.text }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 缓存控制选项 -->
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">翻译选项</h4>
        <label class="flex items-center space-x-2 text-sm text-gray-700">
          <input
            type="checkbox"
            v-model="skipCache"
            class="rounded border-gray-300 text-pink-600 shadow-sm focus:border-pink-300 focus:ring focus:ring-pink-200 focus:ring-opacity-50"
          />
          <span>本次不使用缓存</span>
          <span class="text-xs text-gray-500 ml-2">
            {{ skipCache ? '⚠️ 强制重新翻译' : '💾 使用缓存加速' }}
          </span>
        </label>
      </div>

      <div class="flex space-x-3">
        <button
          @click="startTranslation"
          :disabled="isLoading"
          class="flex-1 px-4 py-3 text-white bg-pink-600 rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors">
          <svg class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
          </svg>
          {{ isLoading ? '正在翻译...' : `开始翻译成${getLanguageName(translationSettings.targetLanguage)}` }}
        </button>

        <button
          v-if="isLoading"
          @click="cancelTranslation"
          class="px-3 py-3 text-red-700 bg-red-100 rounded-lg hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 font-medium transition-colors">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 翻译结果预览 -->
    <div v-if="hasTranslatedSubtitles" class="translation-results mb-6">
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">翻译结果预览</h4>
        <div class="bg-white border border-gray-200 rounded-md p-3 max-h-48 overflow-y-auto">
          <div class="space-y-3">
            <div v-for="(subtitle, index) in translatedPreview" :key="index" class="border-b border-gray-200 pb-2 last:border-b-0">
              <div class="text-xs text-gray-500 font-mono mb-1">
                {{ formatTime(subtitle.startTimeMs) }} → {{ formatTime(subtitle.endTimeMs) }}
              </div>
              <div class="text-sm text-gray-600 mb-1">
                <span class="font-medium text-gray-500">原文:</span> {{ subtitle.originalText }}
              </div>
              <div class="text-sm text-pink-800">
                <span class="font-medium text-pink-600">译文:</span> {{ subtitle.translatedText }}
              </div>
            </div>
          </div>
        </div>

        <!-- 翻译统计信息 -->
        <div class="mt-3 mb-4">
          <span class="text-sm text-gray-600">共翻译 {{ translatedSubtitlesCount }} 个字幕片段</span>
        </div>

        <!-- 操作按钮组 - 分两行布局 -->
        <div class="space-y-3">
          <!-- 第一行：返回按钮 -->
          <div class="flex justify-start">
            <button
              @click="goToPreviousStep"
              class="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors text-base">
              ← 返回选择
            </button>
          </div>

          <!-- 第二行：重新翻译和进入导出 -->
          <div class="flex justify-between space-x-3">
            <button
              @click="retranslate"
              :disabled="isLoading"
              class="flex-1 px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base">
              🔄 重新翻译
            </button>

            <button
              @click="goToNextStep"
              :disabled="isLoading"
              class="flex-1 px-6 py-3 text-white bg-pink-600 rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base">
              ➡️ 进入导出
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作流进度显示 -->
    <WorkflowProgress
      v-if="isLoading"
      :operation-name="'翻译字幕'"
      :operation-status="currentOperationStatus"
      :progress-percentage="translationProgressPercent"
      :current-segment="currentSegment"
      :total-segments="totalSegments"
      :theme="'pink'"
    />

    <!-- 错误详情面板 -->
    <ErrorDetailPanel
      v-if="hasError"
      :error="currentError"
      :title="'翻译过程中出现错误'"
      @retry="startTranslation"
    />

    <!-- 可折叠的翻译日志 -->
    <CollapsibleLog
      v-if="subtitlerStore.progressUpdates.length > 0"
      title="翻译日志"
      :logs="formattedLogs"
      :initial-expanded="false"
      :show-stats="true"
      @clear="clearLogs"
      @copy="handleLogCopy"
    />

  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import CollapsibleLog from '@/vue/components/common/CollapsibleLog.vue';
import WorkflowProgress from '@/vue/components/subtitler/WorkflowProgress.vue';
import ErrorDetailPanel from '@/vue/components/common/ErrorDetailPanel.vue';
import ProcessStateDisplay from '@/vue/components/common/ProcessStateDisplay.vue';
import { OPERATION_STATUS } from '@/vue/constants/operationStatus';

const subtitlerStore = useSubtitlerStore();

// 响应式状态
const skipCache = ref(false);

// Computed properties
const isLoading = computed(() => subtitlerStore.isLoading);
const hasEditableSegments = computed(() => subtitlerStore.editableSegments && subtitlerStore.editableSegments.length > 0);
const editableSegmentsCount = computed(() => subtitlerStore.editableSegments ? subtitlerStore.editableSegments.length : 0);
const hasTranslatedSubtitles = computed(() => subtitlerStore.translatedSubtitles && subtitlerStore.translatedSubtitles.length > 0);
const translatedSubtitlesCount = computed(() => subtitlerStore.translatedSubtitles ? subtitlerStore.translatedSubtitles.length : 0);
const translationProgress = computed(() => subtitlerStore.translationProgress);
const translationProgressPercent = computed(() => subtitlerStore.translationProgressPercent);
const progressUpdates = computed(() => subtitlerStore.progressUpdates);
const translationSettings = computed(() => subtitlerStore.translationSettings || {});

// 新增计算属性
const currentOperationStatus = computed(() => subtitlerStore.currentOperationStatus || OPERATION_STATUS.IDLE);
const hasError = computed(() => subtitlerStore.lastError && (currentOperationStatus.value === OPERATION_STATUS.ERROR || currentOperationStatus.value === OPERATION_STATUS.CANCELED));
const currentError = computed(() => subtitlerStore.lastError || {});
const currentSegment = computed(() => subtitlerStore.currentSegmentIndex || 0);
const totalSegments = computed(() => subtitlerStore.totalSegments || editableSegmentsCount.value);

const subtitlePreview = computed(() => {
  if (!hasEditableSegments.value) return [];
  return subtitlerStore.editableSegments.slice(0, 3); // 显示前3个
});

const translatedPreview = computed(() => {
  if (!hasTranslatedSubtitles.value) return [];
  return subtitlerStore.translatedSubtitles.slice(0, 3); // 显示前3个
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => {
    // 如果update是新格式的对象
    if (typeof update === 'object' && update.stageName) {
      return {
        timestamp: update.timestamp || Date.now(),
        level: update.isError ? 'error' :
               update.status === 'SUCCESS' ? 'success' :
               (update.status === 'FAILURE' || update.status === 'ERROR') ? 'error' :
               update.status === 'PARTIAL_SUCCESS' ? 'warning' :
               update.message?.includes('✅') ? 'success' :
               update.message?.includes('❌') ? 'error' :
               update.message?.includes('⚠️') ? 'warning' : 'info',
        message: `[${update.stageName}] ${update.message}${update.traceId ? ` (${update.traceId.slice(-8)})` : ''}`,
        traceId: update.traceId,
        status: update.status,
        errorDetail: update.errorDetail
      };
    }
    // 兼容旧格式的字符串
    return {
      timestamp: Date.now(),
      level: update.includes('✅') ? 'success' :
             update.includes('❌') ? 'error' :
             update.includes('⚠️') ? 'warning' : 'info',
      message: update
    };
  });
});

// Methods
const formatTime = (timeMs) => {
  const totalSeconds = Math.floor(timeMs / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const getLanguageName = (code) => {
  const languages = {
    'zh': '中文 (简体)',
    'zh-TW': '中文 (繁体)',
    'en': 'English',
    'ja': '日本語',
    'ko': '한国语',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'ru': 'Русский',
    'ar': 'العربية'
  };
  return languages[code] || code;
};

const getQualityName = (quality) => {
  const qualities = {
    'fast': '快速翻译',
    'balanced': '平衡模式',
    'quality': '高质量'
  };
  return qualities[quality] || quality;
};

const getStyleName = (style) => {
  const styles = {
    'formal': '正式',
    'casual': '随意',
    'literary': '文学',
    'technical': '技术'
  };
  return styles[style] || style;
};

const startTranslation = async () => {
  try {
    await subtitlerStore.translateSubtitles({
      skipCache: skipCache.value
    });
  } catch (error) {
    console.error('翻译字幕时出错:', error);
  }
};

const retranslate = async () => {
  try {
    await subtitlerStore.translateSubtitles({
      forceRetranslate: true,
      skipCache: skipCache.value
    });
  } catch (error) {
    console.error('重新翻译字幕时出错:', error);
  }
};

const cancelTranslation = () => {
  subtitlerStore.cancelCurrentOperation();
};

const goToPreviousStep = () => {
  subtitlerStore.setCurrentStep(7); // 返回翻译选择步骤
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(9); // 进入导出步骤
};

const clearLogs = () => {
  subtitlerStore.progressUpdates = [];
};

const handleLogCopy = (logText) => {
  console.log('日志已复制:', logText);
};

const handlePrerequisiteAction = (action) => {
  if (action.step) {
    subtitlerStore.setCurrentStep(action.step);
  }
};
</script>

<style scoped>
/* 组件特定样式 */
</style>
