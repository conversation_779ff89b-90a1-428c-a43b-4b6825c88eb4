import { defineStore } from 'pinia';
import { ref, computed, watch, nextTick } from 'vue';
import { extractSubtitleTranslationResponse } from '@/js/renderer-modules/subtitler/extractors';
import { segmentsToSrt } from '@/js/renderer-modules/subtitler/utils';
import { OPERATION_STATUS } from '@/vue/constants/operationStatus';

// 导入模块化组件
import { 
  subtitlerCoreState, 
  subtitlerCoreGetters, 
  subtitlerCoreActions 
} from './modules/subtitlerCore.js';

import { 
  subtitlerWorkflowState, 
  subtitlerWorkflowActions 
} from './modules/subtitlerWorkflow.js';

import { 
  subtitlerTranslationState, 
  subtitlerTranslationGetters, 
  subtitlerTranslationActions 
} from './modules/subtitlerTranslation.js';

import { 
  subtitlerExportState, 
  subtitlerExportGetters, 
  subtitlerExportActions 
} from './modules/subtitlerExport.js';

import { 
  subtitlerOneClickState, 
  subtitlerOneClickActions 
} from './modules/subtitlerOneClick.js';

import { 
  subtitlerUtilsActions 
} from './modules/subtitlerUtils.js';

// Helper function to get subtitler client (available after renderer.js initialization)
const getSubtitlerClient = () => {
  if (typeof window !== 'undefined' && window.subtitlerClient) {
    console.log('[SubtitlerStore] subtitlerClient found and available');
    return window.subtitlerClient;
  }

  // Enhanced debugging information
  console.warn('[SubtitlerStore] subtitlerClient not available. Debugging info:');
  console.warn('- window object exists:', typeof window !== 'undefined');
  console.warn('- window.subtitlerClient exists:', typeof window !== 'undefined' && !!window.subtitlerClient);
  console.warn('- window.electronAPI exists:', typeof window !== 'undefined' && !!window.electronAPI);

  if (typeof window !== 'undefined') {
    console.warn('- Available window properties:', Object.keys(window).filter(key => key.includes('subtitler') || key.includes('electron') || key.includes('grpc')));
  }

  return null;
};

export const useSubtitlerStore = defineStore('subtitler', {
  state: () => ({
    // 合并所有模块的状态
    ...subtitlerCoreState,
    ...subtitlerWorkflowState,
    ...subtitlerTranslationState,
    ...subtitlerExportState,
    ...subtitlerOneClickState,
  }),

  getters: {
    // 合并所有模块的 getters
    ...subtitlerCoreGetters,
    ...subtitlerTranslationGetters,
    ...subtitlerExportGetters,

    // 添加自定义计算属性
    srtPreviewFromEditableSegments: (state) => {
      if (state.editableSegments && state.editableSegments.length > 0) {
        return segmentsToSrt(state.editableSegments);
      }
      return '';
    },

    srtPreviewFromOriginalSegments: (state) => {
      if (state.audioToTextResult && state.audioToTextResult.segments && state.audioToTextResult.segments.length > 0) {
        return segmentsToSrt(state.audioToTextResult.segments);
      }
      return '';
    },

    getSegmentById: (state) => (id) => {
      if (!state.editableSegments) return null;
      return state.editableSegments.find(segment => segment.id === id) || null;
    },

    isReadyForNextStep: (state) => {
      // Enhanced logic for step progression
      switch (state.currentStep) {
        case 1: return state.uploadedFile !== null;
        case 2: return state.videoToAudioResult !== null;
        case 3: return state.audioToTextResult !== null;
        case 4: return state.generatedSubtitles !== null && state.generatedSubtitles.length > 0;
        case 5: return state.optimizedSubtitles !== null || state.generatedSubtitles !== null; // Can skip optimization
        case 6: return state.editableSegments !== null && state.editableSegments.length > 0;
        case 7: return true; // Translation choice is always ready
        case 8: return state.translatedSubtitles !== null; // Only if translation was chosen
        case 9: return true; // Export step is always ready if we got this far
        default: return false;
      }
    },

    getUploadedFileName: (state) => {
      return state.uploadedFile ? state.uploadedFile.name : '';
    },

    getUploadedFileSize: (state) => {
      return state.uploadedFile ? state.uploadedFile.size : '';
    },

    getUploadedFileType: (state) => {
      return state.uploadedFile ? state.uploadedFile.type : '';
    },

    // Getter to check if there are editable segments
    hasEditableSegments: (state) => {
      return state.editableSegments && state.editableSegments.length > 0;
    },

    // Getter for the currently selected segment object
    selectedSegment: (state) => {
      if (!state.selectedSegmentId || !state.editableSegments) return null;
      return state.editableSegments.find(segment => segment.id === state.selectedSegmentId) || null;
    }
  },

  actions: {
    // 合并所有模块的 actions
    ...subtitlerCoreActions,
    ...subtitlerWorkflowActions,
    ...subtitlerTranslationActions,
    ...subtitlerExportActions,
    ...subtitlerOneClickActions,
    ...subtitlerUtilsActions,

    // UI 相关操作
    setActiveResultTab(tabName) {
      this.activeResultTab = tabName;
    },

    setUploadedFile(fileObject) {
      if (fileObject) {
        this.uploadedFile = {
          name: fileObject.name,
          path: fileObject.path, // Electron File object has 'path'
          type: fileObject.type,
          size: fileObject.size,
        };
        this.currentStep = 1; // Or advance to next step if applicable
        this.progressUpdates = []; // Reset progress for new file
        console.log('[SubtitlerStore] File uploaded:', this.uploadedFile);
      } else {
        this.uploadedFile = null;
        console.log('[SubtitlerStore] File cleared');
      }
    },

    setCurrentStep(stepNumber) {
      if (stepNumber >= 1 && stepNumber <= 9) {
        this.currentStep = stepNumber;
        console.log(`[SubtitlerStore] Current step set to: ${stepNumber}`);
      }
    },

    // 段落编辑相关操作
    setSelectedSegmentId(id) {
      this.selectedSegmentId = id;
    },

    updateSegmentText({ id, newText }) {
      const segment = this.editableSegments.find(seg => seg.id === id);
      if (segment) {
        segment.text = newText;
        console.log(`Updated segment ${id} text`);
      }
    },

    updateSegmentTime({ id, newStartTimeMs, newEndTimeMs }) {
      const segment = this.editableSegments.find(seg => seg.id === id);
      if (segment) {
        if (newStartTimeMs !== undefined) segment.startTimeMs = newStartTimeMs;
        if (newEndTimeMs !== undefined) segment.endTimeMs = newEndTimeMs;
        console.log(`Updated segment ${id} timing`);
      }
    },

    addSegment({ afterId = null, newSegmentData = { startTimeMs: 0, endTimeMs: 0, text: 'New Segment' } }) {
      if (!this.editableSegments) this.editableSegments = [];

      const newSegment = {
        id: `segment-${Date.now()}`,
        ...newSegmentData
      };
      
      if (afterId) {
        const index = this.editableSegments.findIndex(seg => seg.id === afterId);
        if (index !== -1) {
          this.editableSegments.splice(index + 1, 0, newSegment);
        } else {
          this.editableSegments.push(newSegment);
        }
      } else {
        this.editableSegments.push(newSegment);
      }
      
      console.log(`Added new segment: ${newSegment.id}`);
    },

    deleteSegment(id) {
      if (this.editableSegments) {
        const index = this.editableSegments.findIndex(seg => seg.id === id);
        if (index !== -1) {
          this.editableSegments.splice(index, 1);
          console.log(`Deleted segment: ${id}`);
        }
      }
    },

    async saveEditedSubtitles() {
      if (!this.editableSegments || this.editableSegments.length === 0) {
        console.warn('No segments to save');
        return;
      }

      try {
      this.isLoading = true;
        console.log('Saving edited subtitles...');
        
        // 这里可以调用后端保存接口或自动保存功能
        // 目前只是标记为已保存
        this.editedSubtitlesSaved = true;
        
        console.log('Edited subtitles saved successfully');
        this.progressUpdates.push('✅ 字幕编辑已保存');
      } catch (error) {
        console.error('Failed to save edited subtitles:', error);
        this.progressUpdates.push(`❌ 保存失败: ${error.message}`);
      } finally {
        this.isLoading = false;
      }
    },
 
    // 高级工作流方法（从原文件中提取）
    async _handleWorkflowStream(method, params, options = {}) {
      const {
        stageName = method,
        onStart = () => {},
        onProgress = () => {},
        onSuccess = () => {},
        onError = () => {},
        onCancel = () => {}
      } = options;

      // 设置基础状态
      this.isLoading = true;
      
      // 调用开始回调
      onStart();

      return new Promise((resolve, reject) => {
        // 创建进度监听器
        const progressListener = (progressData) => {
          // 检查是否是当前操作的进度更新
          if (progressData.traceId && this.currentProcessState.traceId && progressData.traceId !== this.currentProcessState.traceId) {
            return;
          }

          try {
            // 处理进度更新
            onProgress(progressData);

            // 检查是否取消
            if (progressData.status === 'CANCELLED' || progressData.status === 'CANCELED') {
              onCancel();
              resolve({ cancelled: true });
              return;
            }

            // 检查是否出错
            if (progressData.isError || progressData.status === 'ERROR') {
              const error = new Error(progressData.errorMessage || '操作失败');
              error.detail = progressData.errorDetail;
              onError(error);
              reject(error);
              return;
            }

            // 检查是否成功完成
            if (progressData.status === 'SUCCESS' && progressData.dataPayload) {
              onSuccess(progressData.dataPayload);
              resolve(progressData.dataPayload);
              return;
            }

      } catch (error) {
            console.error(`[_handleWorkflowStream] Error processing progress for ${method}:`, error);
            onError(error);
            reject(error);
          }
        };

        // 添加进度监听器
        const removeProgressListener = window.electronAPI.on('progress-update', progressListener);

        // 调用相应的方法
        const client = getSubtitlerClient();
        if (!client) {
          const error = new Error('SubtitlerClient 不可用');
          onError(error);
          reject(error);
          return;
        }

        // 根据方法名调用对应的客户端方法
        let clientMethod;
        switch (method) {
          case 'videoToAudio':
            clientMethod = client.videoToAudio.bind(client);
            break;
          case 'audioToText':
            clientMethod = client.audioToText.bind(client);
            break;
          case 'generateSubtitles':
            clientMethod = client.generateSubtitles.bind(client);
            break;
          case 'optimizeSubtitles':
            clientMethod = client.optimizeSubtitles.bind(client);
            break;
          case 'translateSubtitles':
            clientMethod = client.translateSubtitles.bind(client);
            break;
          default:
            const error = new Error(`未知的方法: ${method}`);
            onError(error);
            reject(error);
            return;
        }

        // 调用客户端方法（流式方法）
        try {
          const stream = clientMethod(params);
          
          // 监听流事件
          stream.on('data', (response) => {
            if (response.getIsError && response.getIsError()) {
              const error = new Error(response.getErrorMessage ? response.getErrorMessage() : '操作失败');
              onError(error);
              reject(error);
              return;
            }
            
            // 处理进度更新
            const progressData = {
              stageName: response.getStageName ? response.getStageName() : stageName,
              percentage: response.getPercentage ? response.getPercentage() : 0,
              message: response.getMessage ? response.getMessage() : '',
              isError: response.getIsError ? response.getIsError() : false,
              errorMessage: response.getErrorMessage ? response.getErrorMessage() : '',
              status: response.getIsError && response.getIsError() ? 'ERROR' : 'IN_PROGRESS',
              dataPayload: response
            };
            
            onProgress(progressData);
          });
            
            stream.on('end', () => {
            onSuccess({});
            resolve({});
            });
            
            stream.on('error', (error) => {
            console.error(`[_handleWorkflowStream] ${method} failed:`, error);
            onError(error);
              reject(error);
          });
          
          } catch (error) {
          console.error(`[_handleWorkflowStream] ${method} setup failed:`, error);
          onError(error);
          reject(error);
          } finally {
          // 清理会在 Promise 完成时自动调用
          setTimeout(() => {
            if (removeProgressListener) {
              removeProgressListener();
            }
        this.isLoading = false;
          }, 100);
        }
      });
    },

    // 状态映射方法
    _mapStatusEnumToString(statusEnum) {
      if (statusEnum === null || statusEnum === undefined) {
        return 'UNKNOWN';
      }
      
      if (typeof statusEnum === 'string') {
        // 如果已经是字符串，确保是大写的有效状态
        const upperStatus = statusEnum.toUpperCase();
        const validStatuses = ['SUCCESS', 'ERROR', 'PENDING', 'CANCELLED', 'UNKNOWN'];
        return validStatuses.includes(upperStatus) ? upperStatus : 'UNKNOWN';
      }
      
      if (typeof statusEnum === 'number') {
        const statusMap = {
          0: 'UNKNOWN',
          1: 'SUCCESS', 
          2: 'ERROR',    // Changed from 'FAILURE' to 'ERROR'
          3: 'PENDING',
          4: 'CANCELLED'
        };
        return statusMap[statusEnum] || 'UNKNOWN';
      }
      
      return 'UNKNOWN';
    },

    _extractErrorDetail(errorDetailProto) {
      if (!errorDetailProto) return null;
      
      try {
        return {
          errorCode: errorDetailProto.error_code || 'UNKNOWN_ERROR',
          technicalMessage: errorDetailProto.technical_message || '',
          userMessage: errorDetailProto.user_message || '发生了未知错误',
          context: errorDetailProto.context || {}
        };
      } catch (error) {
        console.error('Error extracting error detail:', error);
        return {
          errorCode: 'EXTRACTION_ERROR',
          technicalMessage: 'Failed to extract error details',
          userMessage: '无法获取错误详情',
          context: {}
        };
      }
    },

    // 从 segments 更新可编辑段落
    _updateEditableSegmentsFromSegments(segments) {
      if (!segments || segments.length === 0) {
        console.warn('[_updateEditableSegmentsFromSegments] No segments provided');
        return;
      }

      console.log('[_updateEditableSegmentsFromSegments] Updating with', segments.length, 'segments');

      this.editableSegments = segments.map((segment, index) => ({
        id: segment.id || `segment-${index + 1}`,
        startTimeMs: segment.startTimeMs || segment.start_time_ms || 0,
        endTimeMs: segment.endTimeMs || segment.end_time_ms || 1000,
        text: segment.text || '',
        originalText: segment.originalText || segment.text || '',
        translatedText: segment.translatedText || '',
        status: segment.status || 'SUCCESS',
        errorDetail: segment.errorDetail || null
      }));

      console.log('[_updateEditableSegmentsFromSegments] Updated editableSegments with', this.editableSegments.length, 'segments');
    }
  }
});