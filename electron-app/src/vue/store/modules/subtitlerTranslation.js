/**
 * 字幕翻译管理模块
 * 包含翻译相关的状态、getters和actions
 */
import { OPERATION_STATUS } from '@/vue/constants/operationStatus';

export const subtitlerTranslationState = {
  // 翻译相关状态
  translationSettings: {
    targetLanguage: 'en',
    quality: 'balanced',
    style: 'formal',
    customPrompt: ''
  },
  translatedSubtitles: null, // 翻译后的字幕片段数组
  translationProgress: '', // 翻译进度消息
  translationProgressPercent: 0, // 翻译进度百分比
  translationError: null,
  lastError: null,
  currentOperationStatus: OPERATION_STATUS.IDLE,
  currentSegmentIndex: 0,
  totalSegments: 0,
};

export const subtitlerTranslationGetters = {
  // 获取语言的显示名称
  getLanguageDisplayName: () => (languageCode) => {
    const languageMap = {
      'zh': '中文(简体)',
      'zh-CN': '中文(简体)',
      'zh-TW': '中文(繁体)',
      'en': '英语',
      'ja': '日语',
      'ko': '韩语',
      'fr': '法语',
      'de': '德语',
      'es': '西班牙语',
      'it': '意大利语',
      'pt': '葡萄牙语',
      'ru': '俄语',
      'ar': '阿拉伯语',
      'th': '泰语',
      'vi': '越南语'
    };
    return languageMap[languageCode] || languageCode;
  }
};

export const subtitlerTranslationActions = {
  setTranslationSettings(settings) {
    this.translationSettings = { ...this.translationSettings, ...settings };
  },

  async translateSubtitles(options = {}) {
    // 确定字幕内容
    let subtitleContent = options.subtitleContent;
    
    // 如果没有指定字幕内容，但有生成的字幕，则使用它
    if (!subtitleContent && this.generatedSubtitles) {
      if (typeof this.generatedSubtitles === 'string') {
        subtitleContent = this.generatedSubtitles;
      } else if (this.generatedSubtitles && this.generatedSubtitles.srt_content) {
        subtitleContent = this.generatedSubtitles.srt_content;
      }
    }
    
    // 如果仍然没有字幕内容，但有可编辑段落，则生成字幕内容
    if (!subtitleContent && this.editableSegments && this.editableSegments.length > 0) {
      try {
        // 需要从工具模块导入 segmentsToSrt
        const { segmentsToSrt } = await import('@/js/renderer-modules/subtitler/utils');
        subtitleContent = segmentsToSrt(this.editableSegments);
      } catch (error) {
        console.error('Error converting segments to SRT:', error);
      }
    }
    
    // 如果没有有效的字幕内容，则报错
    if (!subtitleContent) {
      const error = new Error('No subtitle content available for translation.');
      this.translationError = error.message;
      this.lastError = {
        code: 'NO_CONTENT',
        message: '没有可用于翻译的字幕内容',
        details: '请确保您已经完成了字幕编辑步骤，并且有可编辑的字幕段落。'
      };
      this.currentOperationStatus = OPERATION_STATUS.ERROR;
      console.error(this.translationError);
      return;
    }
    
    // 确定目标语言
    const targetLanguage = options.targetLanguage || this.translationSettings.targetLanguage || 'en';

    this.isLoading = true;
    this.translationError = null;
    this.lastError = null;
    this.translationProgress = `正在准备翻译为 ${targetLanguage}...`;
    this.currentOperationStatus = OPERATION_STATUS.PENDING;
    this.currentSegmentIndex = 0;
    this.totalSegments = this.editableSegments ? this.editableSegments.length : 0;
    
    try {
      // 准备参数
      const params = {
        subtitle_content: subtitleContent,
        target_language: targetLanguage,
        skip_cache: options.skipCache === true, // 默认为 false
        force_retranslate: options.forceRetranslate === true, // 默认为 false
        trace_id: options.traceId || this._generateTraceId() // 生成唯一的追踪ID
      };
      
      // 调用通用的工作流处理方法
      const result = await this._handleWorkflowStream('translateSubtitles', params, {
        stageName: 'TranslateSubtitles',
        onStart: () => {
          this.translationProgress = `正在开始翻译为 ${targetLanguage}...`;
          this.currentOperationStatus = OPERATION_STATUS.RUNNING;
          this.progressUpdates.push(`✅ 开始翻译字幕为 ${targetLanguage}`);
        },
        onProgress: (progress) => {
          this.translationProgress = progress.message || `${progress.percentage}%`;
          this.translationProgressPercent = progress.percentage || 0;
          
          // 更新当前处理的段落索引
          if (progress.dataPayload && progress.dataPayload.current_segment !== undefined) {
            this.currentSegmentIndex = progress.dataPayload.current_segment;
          }
          
          // 更新操作状态
          if (progress.status) {
            this.currentOperationStatus = this._mapProgressStatusToOperationStatus(progress.status);
          }
          
          // 如果有分段数据，直接更新翻译结果
          if (progress.dataPayload && progress.dataPayload.segment_results && progress.dataPayload.segment_results.length > 0) {
            this._updateSegmentsWithTranslation(progress.dataPayload.segment_results);
          }
        },
        onSuccess: (result) => {
          this.translationProgress = '翻译已完成';
          this.currentOperationStatus = OPERATION_STATUS.COMPLETED;
          this.progressUpdates.push(`✅ 字幕翻译已完成`);
          
          // 如果有分段数据，直接更新翻译结果
          if (result && result.segment_results && result.segment_results.length > 0) {
            this._updateSegmentsWithTranslation(result.segment_results);
          }
        },
        onError: (error) => {
          this.translationError = error.message || '翻译失败';
          this.translationProgress = '错误: ' + this.translationError;
          this.currentOperationStatus = OPERATION_STATUS.ERROR;
          
          // 设置结构化错误信息
          this.lastError = {
            code: error.code || 'TRANSLATION_FAILED',
            message: error.message || '翻译过程中出现错误',
            details: error.details || '请检查网络连接和翻译设置，然后重试。'
          };
          
          this.progressUpdates.push(`❌ 翻译失败: ${error.message || '未知错误'}`);
        },
        onCancel: () => {
          this.translationProgress = '翻译已取消';
          this.currentOperationStatus = OPERATION_STATUS.CANCELED;
          this.progressUpdates.push(`⚠️ 翻译已被用户取消`);
        }
      });
      
      return result;
    } catch (error) {
      this.translationError = error.message || '翻译失败';
      this.currentOperationStatus = OPERATION_STATUS.ERROR;
      
      // 设置结构化错误信息
      this.lastError = {
        code: error.code || 'TRANSLATION_FAILED',
        message: error.message || '翻译过程中出现错误',
        details: error.details || '请检查网络连接和翻译设置，然后重试。'
      };
      
      console.error('[translateSubtitles] Error:', error);
      this.progressUpdates.push(`❌ 翻译失败: ${error.message || '未知错误'}`);
      throw error;
    } finally {
      this.isLoading = false;
    }
  },
  
  // 辅助方法：更新段落的翻译
  _updateSegmentsWithTranslation(translatedSegments) {
    if (!translatedSegments || translatedSegments.length === 0) return;
    
    // 如果还没有可编辑段落，则初始化
    if (!this.editableSegments) {
      this.editableSegments = [];
      
      // 如果没有可编辑段落，直接从翻译结果创建
      translatedSegments.forEach((segment, index) => {
        this.editableSegments.push({
          id: segment.segment_id || `s${index + 1}`,
          text: segment.original_text || '',
          translatedText: segment.translated_text || '',
          status: segment.status || 'SUCCESS',
          errorDetail: segment.error_detail || null
        });
      });
      
      return;
    }
    
    // 更新现有段落的翻译
    translatedSegments.forEach(segment => {
      // 尝试通过 segment_id 匹配
      let existingIndex = -1;
      
      if (segment.segment_id) {
        existingIndex = this.editableSegments.findIndex(s => s.id === segment.segment_id);
      }
      
      // 如果找不到，尝试通过文本内容匹配
      if (existingIndex < 0 && segment.original_text) {
        existingIndex = this.editableSegments.findIndex(s => s.text === segment.original_text);
      }
      
      if (existingIndex >= 0) {
        // 更新现有段落
        this.editableSegments[existingIndex].translatedText = segment.translated_text || '';
        
        // 如果有状态信息，也更新状态
        if (segment.status) {
          this.editableSegments[existingIndex].translationStatus = segment.status;
        }
        
        // 如果有错误信息，也更新错误信息
        if (segment.error_detail) {
          this.editableSegments[existingIndex].translationErrorDetail = segment.error_detail;
        }
      } else if (segment.translated_text) {
        // 如果找不到匹配的段落，但有翻译结果，则添加新段落
        this.editableSegments.push({
          id: segment.segment_id || `t${this.editableSegments.length + 1}`,
          text: segment.original_text || '',
          translatedText: segment.translated_text,
          translationStatus: segment.status || 'SUCCESS',
          translationErrorDetail: segment.error_detail || null
        });
      }
    });
  },

  // 更新editableSegments以包含翻译数据
  _updateEditableSegmentsWithTranslation(translatedSegments) {
    if (!this.editableSegments || !translatedSegments) {
      console.warn('Cannot update editable segments: missing data');
      return;
    }

    console.log('Updating editable segments with translation data...');
    console.log('Original editableSegments count:', this.editableSegments.length);
    console.log('Translated segments count:', translatedSegments.length);

    // 更新editableSegments，为每个segment添加翻译数据
    this.editableSegments = this.editableSegments.map((segment, index) => {
      // 查找对应的翻译片段
      let translatedSegment = null;

      // 首先尝试通过时间戳匹配
      translatedSegment = translatedSegments.find(ts =>
        ts.startTimeMs === segment.startTimeMs && ts.endTimeMs === segment.endTimeMs
      );

      // 如果时间戳匹配失败，尝试通过索引匹配
      if (!translatedSegment && translatedSegments[index]) {
        translatedSegment = translatedSegments[index];
      }

      // 如果找到了翻译片段，添加翻译数据
      if (translatedSegment) {
        const updatedSegment = {
          ...segment,
          translatedText: translatedSegment.translatedText || translatedSegment.text,
          translation: translatedSegment.translatedText || translatedSegment.text,
          originalText: segment.text
        };

        console.log(`Updated segment ${index}:`, {
          original: segment.text,
          translated: updatedSegment.translatedText
        });

        return updatedSegment;
      } else {
        console.warn(`No translation found for segment ${index}:`, segment.text);
        return segment;
      }
    });

    console.log('Updated editableSegments with translation data');
    this.progressUpdates.push('🔄 已将翻译数据合并到可编辑字幕中');
  },
}; 