/**
 * 字幕核心状态管理模块
 * 包含基础状态定义和核心 getters
 */
import { OPERATION_STATUS } from '@/vue/constants/operationStatus';

export const subtitlerCoreState = {
  // 基础文件和步骤状态
  uploadedFile: null, // { name, path, type, size }
  currentStep: 1,
  isLoading: false,
  progressUpdates: [], // 存储结构化的进度对象
  
  // 当前处理状态
  currentProcessState: {
    traceId: null,
    workflowType: null,
    currentStageName: null,
    overallPercentage: 0,
    currentMessage: '',
    status: null,
    errorDetail: null,
    isActive: false,
    finalDataPayload: null
  },
  
  // 控制标志
  usePreviousAudioForTranscription: true, // 控制音频转文字是否使用视频转音频结果
  useTranscriptionForEditing: true,    // 控制编辑字幕是否使用音频转文字结果

  // 工作流步骤结果
  videoToAudioResult: null,
  videoToAudioProgress: '', // 可以是字符串（消息）或数字（百分比）
  videoToAudioError: null,
  
  audioToTextResult: null, // 存储转录结果（对象或字符串）
  audioToTextProgress: '', // 存储转录进度（字符串或数字）
  audioToTextError: null,  // 存储转录错误（字符串）
  
  generatedSubtitles: null, // 生成的字幕片段数组
  generateSubtitlesProgress: '', // 字幕生成进度消息

  optimizedSubtitles: null, // 优化后的字幕片段数组
  optimizeSubtitlesProgress: '', // 优化进度消息

  editableSegments: null, // 可编辑字幕片段数组：{ id, startTimeMs, endTimeMs, text }
  selectedSegmentId: null, // 当前选中的片段ID
  editedSubtitlesSaved: false, // 跟踪编辑的字幕是否已保存

  // 翻译相关
  translationSettings: {
    targetLanguage: 'en',
    quality: 'balanced',
    style: 'formal',
    customPrompt: ''
  },
  translatedSubtitles: null, // 翻译后的字幕片段数组
  translationProgress: '', // 翻译进度消息
  translationProgressPercent: 0, // 翻译进度百分比
  
  // 导出相关
  exportFilename: '', // 用户定义的文件名（不含扩展名）
  exportFormat: 'srt', // 用户选择的导出格式
  exportLayout: '原文在上', // 使用后端期望的中文布局名称
  exportLayouts: ['原文在上'], // 多选布局
  exportContentSource: 'editable_segments', // 内容来源
  exportContentSources: ['editable_segments'], // 多选内容来源
  exportAutoSaveToDefault: false, // 是否跳过保存对话框
  lastExportPath: null, // 最后成功导出的路径
  exportResults: [], // 存储多个导出结果

  // 一键操作相关
  oneClickOperationInProgress: false, // 跟踪一键操作是否在进行中
  oneClickOperationError: null, // 存储一键操作的错误
  oneClickWorkflowType: 'vid_to_srt_trans', // 默认工作流类型
  oneClickTargetLanguage: 'zh-CN', // 默认目标语言
  oneClickExportFormat: 'srt', // 一键操作的默认导出格式
  oneClickExportLayout: '原文在上', // 使用后端期望的中文布局名称
  oneClickExportLayouts: ['原文在上'], // 多选布局支持

  // 结果查看器
  activeResultTab: 'transcript', // 默认活跃标签页
};

export const subtitlerCoreGetters = {
  isReadyForNextStep: (state) => {
    // 增强的步骤进度逻辑
    switch (state.currentStep) {
      case 1: return state.uploadedFile !== null;
      case 2: return state.videoToAudioResult !== null;
      case 3: return state.audioToTextResult !== null;
      case 4: return state.generatedSubtitles !== null && state.generatedSubtitles.length > 0;
      case 5: return state.optimizedSubtitles !== null || state.generatedSubtitles !== null; // 可以跳过优化
      case 6: return state.editableSegments !== null && state.editableSegments.length > 0;
      case 7: return true; // 翻译选择总是就绪
      case 8: return state.translatedSubtitles !== null; // 只有在选择翻译时才需要
      case 9: return true; // 导出步骤总是就绪，如果能到这一步
      default: return false;
    }
  },
  
  getUploadedFileName: (state) => {
    return state.uploadedFile ? state.uploadedFile.name : '';
  },
  
  getUploadedFileSize: (state) => {
    return state.uploadedFile ? state.uploadedFile.size : '';
  },
  
  getUploadedFileType: (state) => {
    return state.uploadedFile ? state.uploadedFile.type : '';
  },
  
  // 检查是否有可编辑片段的getter
  hasEditableSegments: (state) => {
    return state.editableSegments && state.editableSegments.length > 0;
  },
  
  // 当前选中片段对象的getter
  selectedSegment: (state) => {
    if (!state.selectedSegmentId || !state.editableSegments) return null;
    return state.editableSegments.find(segment => segment.id === state.selectedSegmentId) || null;
  },
  
  getSegmentById: (state) => (id) => {
    if (!state.editableSegments) return null;
    return state.editableSegments.find(segment => segment.id === id) || null;
  }
};

export const subtitlerCoreActions = {
  // 基础操作
  setActiveResultTab(tabName) {
    this.activeResultTab = tabName;
  },
  
  setUploadedFile(fileObject) {
    if (fileObject) {
      this.uploadedFile = {
        name: fileObject.name,
        path: fileObject.path, // Electron文件对象有'path'属性
        type: fileObject.type,
        size: fileObject.size,
      };
      this.currentStep = 1; // 或推进到下一步（如果适用）
      this.progressUpdates = []; // 为新文件重置进度
      this.videoToAudioResult = null;
      this.audioToTextResult = null;
      this.audioToTextProgress = '';
      this.audioToTextError = null;
      this.editableSegments = null;
      this.selectedSegmentId = null;
      this.editedSubtitlesSaved = false; // 新文件时重置
    } else {
      this.uploadedFile = null;
    }
  },
  
  setCurrentStep(stepNumber) {
    if (typeof stepNumber === 'number' && stepNumber > 0) {
      // 可以添加逻辑以验证步骤是否可达
      this.currentStep = stepNumber;
    } else {
      console.warn(`Invalid step number: ${stepNumber}`);
    }
  },
  
  resetWorkflow() {
    this.uploadedFile = null;
    this.currentStep = 1;
    this.isLoading = false;
    this.progressUpdates = [];
    this.videoToAudioResult = null;
    this.audioToTextResult = null;
    this.audioToTextProgress = '';
    this.audioToTextError = null;

    // 重置新工作流状态
    this.generatedSubtitles = null;
    this.generateSubtitlesProgress = '';
    this.optimizedSubtitles = null;
    this.optimizeSubtitlesProgress = '';
    this.editableSegments = null;
    this.selectedSegmentId = null;
    this.editedSubtitlesSaved = false;
    this.translationSettings = {
      targetLanguage: 'en',
      quality: 'balanced',
      style: 'formal',
      customPrompt: ''
    };
    this.translatedSubtitles = null;
    this.translationProgress = '';
    this.translationProgressPercent = 0;

    // 重置控制标志
    this.usePreviousAudioForTranscription = true;
    this.useTranscriptionForEditing = true;

    // 重置导出选项
    this.exportFilename = '';
    this.exportFormat = 'srt';
    this.exportLayout = '原文在上'; // 使用后端期望的中文布局名称
    this.exportContentSource = 'editable_segments';
    this.exportAutoSaveToDefault = false;
    this.lastExportPath = null;
    console.log('Subtitler workflow reset.');
  },
  
  // 控制标志设置
  setUsePreviousAudioForTranscription(value) {
    this.usePreviousAudioForTranscription = !!value;
    // 如果不使用前一步的音频，可能重置audioToTextResult，或让组件处理
    if (!value) {
      this.audioToTextResult = null; // 如果不使用前一步音频且没有其他音频加载，清除转录
      this.editableSegments = null; // 也清除片段
    }
  },

  setUseTranscriptionForEditing(value) {
    this.useTranscriptionForEditing = !!value;
    if (value && this.audioToTextResult) {
      this.initializeEditableSegments(); // 如果切换为true且结果存在，重新初始化
    } else if (!value) {
      this.editableSegments = null; // 如果不使用转录，清除片段
    }
  },
  
  // 片段编辑操作
  setSelectedSegmentId(id) {
    this.selectedSegmentId = id;
  },

  updateSegmentText({ id, newText }) {
    if (!this.editableSegments) return;
    const segment = this.editableSegments.find(s => s.id === id);
    if (segment) {
      segment.text = newText;
    }
    this.editedSubtitlesSaved = false; // 文本更新，尚未保存
  },

  updateSegmentTime({ id, newStartTimeMs, newEndTimeMs }) {
    if (!this.editableSegments) return;
    const segment = this.editableSegments.find(s => s.id === id);
    if (segment) {
      // 基本验证，可以扩展
      if (typeof newStartTimeMs === 'number' && newStartTimeMs >= 0) {
        segment.startTimeMs = newStartTimeMs;
      }
      if (typeof newEndTimeMs === 'number' && newEndTimeMs >= 0 && newEndTimeMs >= segment.startTimeMs) {
        segment.endTimeMs = newEndTimeMs;
      }
    }
    this.editedSubtitlesSaved = false; // 时间更新，尚未保存
  },

  addSegment({ afterId = null, newSegmentData = { startTimeMs: 0, endTimeMs: 0, text: 'New Segment' } }) {
    if (!this.editableSegments) this.editableSegments = [];

    const newId = `segment-${Date.now()}-${this.editableSegments.length}`;
    const segmentToAdd = { ...newSegmentData, id: newId };

    if (afterId === null) { // 添加到末尾
      this.editableSegments.push(segmentToAdd);
    } else {
      const index = this.editableSegments.findIndex(s => s.id === afterId);
      if (index !== -1) {
        this.editableSegments.splice(index + 1, 0, segmentToAdd);
      } else { // 如果找不到afterId，添加到末尾
        this.editableSegments.push(segmentToAdd);
        console.warn(`addSegment: afterId ${afterId} not found. Segment added to end.`);
      }
    }
    this.selectedSegmentId = newId; // 可选择性地选择新片段
    this.editedSubtitlesSaved = false; // 片段已添加，尚未保存
  },

  deleteSegment(id) {
    if (!this.editableSegments) return;
    this.editableSegments = this.editableSegments.filter(s => s.id !== id);
    if (this.selectedSegmentId === id) {
      this.selectedSegmentId = null; // 如果删除了选中的片段，清除选择
    }
    this.editedSubtitlesSaved = false; // 片段已删除，可能需要保存
  },
  
  setEditableSegments(segments) {
    this.editableSegments = segments;
  },
}; 