/**
 * 字幕工作流管理模块
 * 包含核心工作流操作：videoToAudio, audioToText, generateSubtitles, optimizeSubtitles
 */
import { segmentsToSrt } from '@/js/renderer-modules/subtitler/utils';

// 导出工作流状态
export const subtitlerWorkflowState = {
  // Video to Audio step
  videoToAudioResult: null,
  videoToAudioProgress: '', // Can be String (for messages) or Number (for percentage)
  videoToAudioError: null,
  
  // Audio to Text step
  audioToTextResult: null, // Stores the transcription result (Object or String)
  audioToTextProgress: '', // Stores transcription progress (String or Number)
  audioToTextError: null,  // Stores transcription errors (String)
  
  // Generate Subtitles step (Step 4)
  generatedSubtitles: null, // Array of generated subtitle segments
  generateSubtitlesProgress: '', // Progress message for subtitle generation

  // Optimize Subtitles step (Step 5)
  optimizedSubtitles: null, // Array of optimized subtitle segments
  optimizeSubtitlesProgress: '', // Progress message for optimization
};

// Helper function to get subtitler client
const getSubtitlerClient = () => {
  if (typeof window !== 'undefined' && window.subtitlerClient) {
    console.log('[SubtitlerStore] subtitlerClient found and available');
    return window.subtitlerClient;
  }

  // Enhanced debugging information
  console.warn('[SubtitlerStore] subtitlerClient not available. Debugging info:');
  console.warn('- window object exists:', typeof window !== 'undefined');
  console.warn('- window.subtitlerClient exists:', typeof window !== 'undefined' && !!window.subtitlerClient);
  console.warn('- window.electronAPI exists:', typeof window !== 'undefined' && !!window.electronAPI);

  if (typeof window !== 'undefined') {
    console.warn('- Available window properties:', Object.keys(window).filter(key => key.includes('subtitler') || key.includes('electron') || key.includes('grpc')));
  }

  return null;
};

/**
 * 生成唯一的追踪ID
 * @returns {string} 追踪ID
 */
function generateTraceId() {
  return 'trace-' + Date.now() + '-' + Math.random().toString(36).substring(2, 15);
}

export const subtitlerWorkflowActions = {
  async processVideoToAudio() {
    if (!this.uploadedFile || !this.uploadedFile.path) {
      this.videoToAudioError = 'No file uploaded or file path is missing.';
      console.error(this.videoToAudioError);
      return;
    }

    this.isLoading = true;
    this.videoToAudioError = null;
    this.videoToAudioProgress = 'Starting video to audio conversion...';
    this.progressUpdates = [this.videoToAudioProgress]; // Reset and add first message

    try {
      // 调用通用的工作流处理方法
      const result = await this._handleWorkflowStream('videoToAudio', 
        { video_path: this.uploadedFile.path },
        {
          stageName: 'VideoToAudio',
          onStart: () => {
            this.videoToAudioProgress = 'Starting video to audio conversion...';
          },
          onProgress: (progress) => {
            this.videoToAudioProgress = progress.message || `${progress.percentage}%`;
          },
          onSuccess: (result) => {
            this.videoToAudioProgress = 'Video to audio conversion completed.';
          },
          onError: (error) => {
            this.videoToAudioError = error.message || 'Video to audio conversion failed.';
            this.videoToAudioProgress = 'Error: ' + this.videoToAudioError;
          }
        }
      );
      
      return result;
    } catch (error) {
      this.videoToAudioError = error.message || 'Video to audio conversion failed.';
      console.error('[processVideoToAudio] Error:', error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  },

  async processAudioToText(options = {}) {
    // 确定音频路径
    let audioPath = options.audioPath;
    
    // 如果没有指定音频路径，但设置了使用上一步的音频结果，则使用它
    if (!audioPath && this.usePreviousAudioForTranscription && this.videoToAudioResult) {
      if (typeof this.videoToAudioResult === 'string') {
      audioPath = this.videoToAudioResult;
      } else if (this.videoToAudioResult && this.videoToAudioResult.audio_path) {
        audioPath = this.videoToAudioResult.audio_path;
      }
    }
    
    // 如果仍然没有音频路径，但上传了音频文件，则使用它
    if (!audioPath && this.uploadedFile && this.uploadedFile.path && 
        (this.uploadedFile.type.startsWith('audio/') || this.uploadedFile.name.match(/\.(mp3|wav|ogg|m4a|aac|flac)$/i))) {
      audioPath = this.uploadedFile.path;
    }

    // 如果没有有效的音频路径，则报错
    if (!audioPath) {
      this.audioToTextError = 'No audio path available for transcription.';
      console.error(this.audioToTextError);
      return;
    }

    this.isLoading = true;
    this.audioToTextError = null;
    this.audioToTextProgress = 'Starting audio transcription...';
    
    try {
      // 准备参数
      const params = {
        audio_path: audioPath,
        request_word_timestamps: options.requestWordTimestamps !== false, // 默认为 true
        skip_cache: options.skipCache === true // 默认为 false
      };
      
      // 调用通用的工作流处理方法
      const result = await this._handleWorkflowStream('audioToText', params, {
        stageName: 'AudioToText',
        onStart: () => {
          this.audioToTextProgress = 'Starting audio transcription...';
        },
        onProgress: (progress) => {
          this.audioToTextProgress = progress.message || `${progress.percentage}%`;
          
          // 如果有分段数据，直接更新可编辑段落
          if (progress.dataPayload && progress.dataPayload.segments && progress.dataPayload.segments.length > 0) {
            this._updateEditableSegmentsFromSegments(progress.dataPayload.segments);
          }
        },
        onSuccess: (result) => {
          this.audioToTextProgress = 'Audio transcription completed.';
          
          // 如果有分段数据，直接更新可编辑段落
          if (result && result.segments && result.segments.length > 0) {
            this._updateEditableSegmentsFromSegments(result.segments);
          }
        },
        onError: (error) => {
          this.audioToTextError = error.message || 'Audio transcription failed.';
          this.audioToTextProgress = 'Error: ' + this.audioToTextError;
        }
      });
      
      return result;
    } catch (error) {
      this.audioToTextError = error.message || 'Audio transcription failed.';
      console.error('[processAudioToText] Error:', error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  },

  // 辅助方法：从分段数据更新可编辑段落
  _updateEditableSegmentsFromSegments(segments) {
    if (!segments || segments.length === 0) return;
    
    // 如果还没有可编辑段落，则初始化
    if (!this.editableSegments) {
      this.editableSegments = [];
    }
    
    // 将新段落添加到可编辑段落中
    segments.forEach((segment, index) => {
      const existingIndex = this.editableSegments.findIndex(s => 
        (s.startTimeMs === segment.start_time_ms && s.endTimeMs === segment.end_time_ms) ||
        (s.id === segment.segment_id)
      );
      
      if (existingIndex >= 0) {
        // 更新现有段落
        this.editableSegments[existingIndex].text = segment.text;
        // 如果有状态信息，也更新状态
        if (segment.status) {
          this.editableSegments[existingIndex].status = segment.status;
        }
        // 如果有错误信息，也更新错误信息
        if (segment.error_detail) {
          this.editableSegments[existingIndex].errorDetail = segment.error_detail;
        }
          } else {
        // 添加新段落
        this.editableSegments.push({
          id: segment.segment_id || `s${index + 1}`,
          startTimeMs: segment.start_time_ms,
          endTimeMs: segment.end_time_ms,
          text: segment.text,
          status: segment.status || 'SUCCESS',
          errorDetail: segment.error_detail || null
        });
      }
    });
  },

  initializeEditableSegments() {
    if (this.useTranscriptionForEditing) {
      if (this.audioToTextResult && Array.isArray(this.audioToTextResult.segments)) {
        this.editableSegments = this.audioToTextResult.segments.map((segment, index) => ({
          id: `segment-${Date.now()}-${index}`, // Simple unique ID
          startTimeMs: segment.startTimeMs, // Assuming this structure from ASR
          endTimeMs: segment.endTimeMs,   // Assuming this structure from ASR
          text: segment.text,
        }));
        this.selectedSegmentId = null; // Reset selected segment
        console.log('Editable segments initialized from transcription:', this.editableSegments);
      } else if (typeof this.audioToTextResult === 'string') {
        // Handle plain text result - create a single segment
        this.editableSegments = [{
          id: `segment-${Date.now()}-0`,
          startTimeMs: 0, // Default start time
          endTimeMs: 0,   // Default end time, user might need to adjust
          text: this.audioToTextResult,
        }];
        this.selectedSegmentId = null;
        console.log('Editable segments initialized from plain text transcription:', this.editableSegments);
      } else {
        console.warn('Cannot initialize editable segments: audioToTextResult is not in expected format or is null, despite useTranscriptionForEditing being true.');
        this.editableSegments = []; // Initialize as empty array to prevent errors
      }
    } else {
      // If not using transcription for editing, clear segments or allow manual input (handled by component)
      this.editableSegments = []; // Or null, depending on desired behavior for manual input
      console.log('Not using transcription for editing. Editable segments cleared.');
    }
    this.editedSubtitlesSaved = false; // Segments initialized or cleared, not saved yet
  },

  async generateSubtitles(skipCache = false) {
    if (!this.audioToTextResult || !this.audioToTextResult.segments) {
      throw new Error('No transcription result available for subtitle generation');
    }

    this.isLoading = true;
    this.generateSubtitlesProgress = '开始生成字幕...';
    this.progressUpdates.push('📝 开始生成字幕片段...');

    try {
      // Call backend to generate subtitles using text_to_srt workflow
      this.generateSubtitlesProgress = '调用后端生成字幕...';

      const requestParams = {
        req_text_content: this.audioToTextResult.transcript,
        workflow_type: 'text_to_srt',
        request_word_timestamps: false,
        cache_control: {
          skip_cache: skipCache
        }
      };

      if (skipCache) {
        this.progressUpdates.push('⚠️ 跳过缓存，强制重新生成字幕...');
      }

      // 设置字幕生成进度监听器
      const generateProgressListener = (progress) => {
        // 添加详细的时间戳和完整日志
        console.log(`[${new Date().toISOString()}] Received GenerateSubtitles progress-update:`, JSON.stringify(progress));
        
        // 修复：添加对中文阶段名的支持，如"字幕断句"和"字幕优化"
        if (progress && progress.stageName && (
            progress.stageName.includes('generateSubtitles') || 
            progress.stageName.includes('GenerateSubtitles') ||
            progress.stageName.includes('字幕生成') ||
            progress.stageName.includes('字幕断句') ||
            progress.stageName.includes('字幕优化')
        )) {
          this.generateSubtitlesProgress = `${progress.message} (${progress.percentage}%)`;
          
          // 始终记录进度更新，不论百分比
          this.progressUpdates.push(`📝 生成进度：${progress.percentage}% - ${progress.message}`);
          
          console.log('[GenerateSubtitles] Progress update:', progress);
        }
      };

      // 添加进度监听器 - electronAPI.on 返回一个清理函数
      const removeGenerateProgressListener = window.electronAPI.on('progress-update', generateProgressListener);

      let result;
      try {
        result = await window.electronAPI.invoke('subtitler-full-workflow', requestParams);
      } finally {
        // 调用清理函数移除监听器
        if (removeGenerateProgressListener) {
          removeGenerateProgressListener();
        }
      }

      if (result && result.generate_subtitles_response && result.generate_subtitles_response.segments) {
        // Use the segments from the backend response
        this.generatedSubtitles = result.generate_subtitles_response.segments.map((segment, index) => ({
          id: segment.id || `generated-${index}`,
          text: segment.text,
          startTimeMs: segment.startTimeMs || segment.start_time_ms,
          endTimeMs: segment.endTimeMs || segment.end_time_ms,
          start_time_ms: segment.start_time_ms || segment.startTimeMs,
          end_time_ms: segment.end_time_ms || segment.endTimeMs
        }));

        this.generateSubtitlesProgress = '字幕生成完成！';
        this.progressUpdates.push(`✅ 成功从转录文本生成 ${this.generatedSubtitles.length} 个字幕片段`);
      } else {
        // Fallback: use simple sentence splitting if backend doesn't support text_to_srt
        this.generateSubtitlesProgress = '使用备用句子生成方法...';
        this.generatedSubtitles = this._generateSentencesFromSegments(this.audioToTextResult.segments);
        this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`);
      }
    } catch (error) {
      console.error('Error generating subtitles:', error);
      this.generateSubtitlesProgress = '后端失败，使用备用方法...';

      // Fallback to local sentence generation
      try {
        this.generatedSubtitles = this._generateSentencesFromSegments(this.audioToTextResult.segments);
        this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`);
      } catch (fallbackError) {
        this.generateSubtitlesProgress = '字幕生成失败';
        this.progressUpdates.push(`❌ 错误：${fallbackError.message}`);
        throw fallbackError;
      }
    } finally {
      this.isLoading = false;
    }
  },

  // Helper method to generate sentences from word-level segments
  _generateSentencesFromSegments(segments) {
    if (!segments || segments.length === 0) {
      return [];
    }

    console.log(`[generateSentences] Processing ${segments.length} segments`);

    const sentences = [];
    let currentSentence = {
      words: [],
      startTime: null,
      endTime: null
    };

    // Sentence ending punctuation
    const sentenceEnders = /[.!?。！？]/;

    // More aggressive sentence splitting - split every 8-12 words or on punctuation
    const maxWordsPerSentence = 12;
    const minWordsPerSentence = 5;

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      const text = segment.text.trim();

      if (!text) continue;

      // Initialize start time for new sentence
      if (currentSentence.words.length === 0) {
        currentSentence.startTime = segment.startTimeMs || segment.start_time_ms;
      }

      currentSentence.words.push(text);
      currentSentence.endTime = segment.endTimeMs || segment.end_time_ms;

      // Check if this segment ends a sentence
      const endsWithPunctuation = sentenceEnders.test(text);
      const isLongEnough = currentSentence.words.length >= minWordsPerSentence;
      const isTooLong = currentSentence.words.length >= maxWordsPerSentence;
      const hasTimeGap = i < segments.length - 1 &&
        ((segments[i + 1].startTimeMs || segments[i + 1].start_time_ms) - currentSentence.endTime) > 1500; // 1.5 second gap
      const isLastSegment = i === segments.length - 1;

      // Decision logic for ending sentence
      let shouldEndSentence = false;
      let reason = '';

      if (endsWithPunctuation) {
        shouldEndSentence = true;
        reason = 'punctuation';
      } else if (isTooLong) {
        shouldEndSentence = true;
        reason = 'too long';
      } else if (isLongEnough && hasTimeGap) {
        shouldEndSentence = true;
        reason = 'time gap';
      } else if (isLastSegment && currentSentence.words.length > 0) {
        shouldEndSentence = true;
        reason = 'last segment';
      }

      if (shouldEndSentence) {
        // Create sentence
        const sentenceText = currentSentence.words.join(' ').trim();
        if (sentenceText) {
          console.log(`[generateSentences] Creating sentence ${sentences.length + 1}: "${sentenceText.substring(0, 50)}..." (${currentSentence.words.length} words, reason: ${reason})`);

          sentences.push({
            id: `sentence-${sentences.length}`,
            text: sentenceText,
            startTimeMs: currentSentence.startTime,
            endTimeMs: currentSentence.endTime,
            start_time_ms: currentSentence.startTime,
            end_time_ms: currentSentence.endTime
          });
        }

        // Reset for next sentence (only if not the last segment)
        if (!isLastSegment) {
          currentSentence = {
            words: [],
            startTime: null,
            endTime: null
          };
        }
      }
    }

    console.log(`[generateSentences] Generated ${sentences.length} sentences from ${segments.length} segments`);
    return sentences;
  },

  async saveEditedSubtitles() {
    if (!this.editableSegments || this.editableSegments.length === 0) {
      console.warn('No edited segments to save.');
      // Optionally, inform the user via UI store
      this.editedSubtitlesSaved = false; // Explicitly false if nothing to save or already saved
      return;
    }
    this.isLoading = true;
    this.editedSubtitlesSaved = false; // Set to false initially for the save attempt
    try {
      // Create a clean, serializable copy of the segments
      const cleanSegments = this.editableSegments.map(segment => ({
        id: segment.id,
        text: segment.text,
        startTimeMs: segment.startTimeMs,
        endTimeMs: segment.endTimeMs,
        start_time_ms: segment.start_time_ms,
        end_time_ms: segment.end_time_ms
      }));

      console.log('Saving edited subtitles:', cleanSegments.length, 'segments');
      this.progressUpdates.push(`💾 正在保存 ${cleanSegments.length} 个编辑后的字幕片段...`);
      // IPC call to main process to handle saving
      // The main process might save to a file, send to backend, etc.
      const result = await window.electronAPI.invoke('subtitler:save-edited-segments', cleanSegments);
      console.log('Save edited subtitles result:', result);
      this.progressUpdates.push(`✅ 编辑后的字幕保存成功`);
      this.editedSubtitlesSaved = true; // Mark as saved on success
      // Handle success - e.g., show notification, move to next step
      // this.setCurrentStep(5); // Example: move to an "Export" or "Done" step
    } catch (error) {
      console.error('Error saving edited subtitles:', error);
      this.progressUpdates.push(`❌ 保存编辑字幕失败：${error.message}`);
      this.editedSubtitlesSaved = false; // Mark as not saved on error
      // Handle error - e.g., show error message to user
    } finally {
      this.isLoading = false;
    }
  },
}; 