/**
 * 字幕工具模块
 * 包含各种辅助方法和工具函数
 */

/**
 * 生成唯一的追踪ID
 * @returns {string} 追踪ID
 */
export function generateTraceId() {
  return 'trace-' + Date.now() + '-' + Math.random().toString(36).substring(2, 15);
}

export const subtitlerUtilsActions = {
  // Helper methods for SRT conversion
  _segmentsToSrt(segments) {
    if (!segments || segments.length === 0) {
      console.warn('[_segmentsToSrt] No segments provided');
      return '';
    }

    console.log('[_segmentsToSrt] Converting', segments.length, 'segments to SRT');

    return segments.map((segment, index) => {
      const startTimeMs = segment.startTimeMs || segment.start_time_ms || 0;
      const endTimeMs = segment.endTimeMs || segment.end_time_ms || 1000;

      const startTime = this._msToSrtTime(startTimeMs);
      const endTime = this._msToSrtTime(endTimeMs);
      const text = segment.text || '';

      if (!text.trim()) {
        console.warn('[_segmentsToSrt] Empty text for segment', index);
      }

      return `${index + 1}\n${startTime} --> ${endTime}\n${text}\n`;
    }).join('\n');
  },

  _msToSrtTime(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    const milliseconds = ms % 1000;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
  },

  _parseSrtToSegments(srtContent, originalSegments) {
    const lines = srtContent.trim().split('\n');
    const segments = [];
    let currentSegment = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (!line) {
        if (currentSegment) {
          segments.push(currentSegment);
          currentSegment = null;
        }
        continue;
      }

      // Check if line is a number (subtitle index)
      if (/^\d+$/.test(line)) {
        currentSegment = { index: parseInt(line) };
      }
      // Check if line is a timestamp
      else if (line.includes('-->')) {
        const [startStr, endStr] = line.split('-->').map(s => s.trim());
        const startMs = this._srtTimeToMs(startStr);
        const endMs = this._srtTimeToMs(endStr);

        if (currentSegment) {
          currentSegment.startTimeMs = startMs;
          currentSegment.endTimeMs = endMs;
          currentSegment.start_time_ms = startMs;
          currentSegment.end_time_ms = endMs;
        }
      }
      // Text content
      else if (currentSegment && !currentSegment.text) {
        currentSegment.text = line;
        currentSegment.translatedText = line;

        // Find corresponding original segment
        const originalIndex = currentSegment.index - 1;
        if (originalSegments && originalSegments[originalIndex]) {
          currentSegment.originalText = originalSegments[originalIndex].text;
          currentSegment.id = originalSegments[originalIndex].id || `translated-${currentSegment.index}`;
        } else {
          currentSegment.originalText = line; // Fallback
          currentSegment.id = `translated-${currentSegment.index}`;
        }
      }
    }

    // Add last segment if exists
    if (currentSegment) {
      segments.push(currentSegment);
    }

    return segments;
  },

  _srtTimeToMs(timeStr) {
    const [time, ms] = timeStr.split(',');
    const [hours, minutes, seconds] = time.split(':').map(Number);

    return (hours * 3600 + minutes * 60 + seconds) * 1000 + parseInt(ms || 0);
  },

  // 将英文阶段名称转换为中文
  getStageNameInChinese(stageName) {
    const stageMap = {
      'audio_extraction': '音频提取',
      'audio_processing': '音频处理',
      'transcription': '语音识别',
      'text_processing': '文本处理',
      'subtitle_generation': '字幕生成',
      'subtitle_optimization': '字幕优化',
      'translation': '翻译处理',
      'export': '导出处理',
      'file_processing': '文件处理',
      'initialization': '初始化',
      'completion': '完成处理',
      'VideoToAudio': '视频转音频',
      'AudioToText': '音频转文字',
      'GenerateSubtitles': '生成字幕',
      'OptimizeSubtitles': '优化字幕',
      'Translation': '翻译处理',
      'Export': '导出处理'
    };
    return stageMap[stageName] || stageName;
  },

  // 取消当前操作
  async cancelCurrentOperation() {
    if (!this.currentProcessState.isActive) {
      console.log('没有活跃的操作需要取消');
      return;
    }

    try {
      console.log(`正在取消操作，trace_id: ${this.currentProcessState.traceId}`);
      
      // 更新状态为取消中
      this.currentProcessState.status = 'CANCELING';
      this.currentProcessState.currentMessage = '正在取消操作...';
      
      // 添加取消日志
      this.progressUpdates.push({
        stageName: this.currentProcessState.currentStageName || '取消操作',
        percentage: this.currentProcessState.overallPercentage,
        message: '用户取消了操作',
        isError: false,
        errorMessage: '',
        traceId: this.currentProcessState.traceId,
        status: 'CANCELED',
        errorDetail: null,
        timestamp: Date.now(),
        dataPayload: null
      });

      // 调用后端取消接口（如果有的话）
      if (this.currentProcessState.traceId) {
        try {
          await window.electronAPI.invoke('cancel-operation', {
            trace_id: this.currentProcessState.traceId
          });
        } catch (error) {
          console.warn('后端取消操作失败:', error.message);
        }
      }

      // 更新最终状态
      this.currentProcessState.isActive = false;
      this.currentProcessState.status = 'CANCELED';
      this.currentProcessState.currentMessage = '操作已取消';
      
      // 重置加载状态
      this.isLoading = false;
      
      // 更新操作状态（如果有的话）
      if (this.currentOperationStatus) {
        this.currentOperationStatus = 'CANCELED'; // OPERATION_STATUS.CANCELED 的值
      }
      
      console.log('操作已成功取消');
    } catch (error) {
      console.error('取消操作时发生错误:', error);
      
      // 即使取消失败，也要重置状态
      this.currentProcessState.isActive = false;
      this.currentProcessState.status = 'ERROR';
      this.currentProcessState.errorDetail = {
        errorCode: 'CANCEL_ERROR',
        technicalMessage: error.message,
        userMessage: '取消操作时发生错误',
        context: {}
      };
      
      this.isLoading = false;
      if (this.currentOperationStatus) {
        this.currentOperationStatus = 'ERROR'; // OPERATION_STATUS.ERROR 的值
      }
    }
  },

  // 将进度状态映射为操作状态
  _mapProgressStatusToOperationStatus(progressStatus) {
    if (typeof progressStatus === 'string') {
      const statusMap = {
        'UNSPECIFIED': 'IDLE',
        'IN_PROGRESS': 'RUNNING',
        'ERROR': 'ERROR',
        'ERROR': 'ERROR',
        'SUCCESS': 'COMPLETED',
        'PARTIAL_SUCCESS': 'COMPLETED',
        'CANCELLED': 'CANCELED',
        'PENDING': 'PENDING'
      };
      return statusMap[progressStatus] || 'IDLE';
    } else if (typeof progressStatus === 'number') {
      const statusMap = {
        0: 'IDLE',      // UNSPECIFIED
        1: 'RUNNING',   // IN_PROGRESS
        2: 'ERROR',     // ERROR
        3: 'COMPLETED', // SUCCESS
        4: 'COMPLETED', // PARTIAL_SUCCESS
        5: 'CANCELED',  // CANCELLED
        6: 'PENDING'    // PENDING
      };
      return statusMap[progressStatus] || 'IDLE';
    }
    return 'IDLE';
  },

  // 根据进度数据中的中间结果更新可编辑段落
  _updateEditableSegmentsFromProgressData(progressData) {
    if (!progressData || !progressData.dataPayload) return;
    
    const { dataPayload } = progressData;
    
    // 处理转录结果更新
    if (dataPayload.segments && Array.isArray(dataPayload.segments)) {
      // 如果还没有初始化editableSegments，则创建它们
      if (!this.editableSegments || this.editableSegments.length === 0) {
        this._updateEditableSegmentsFromSegments(dataPayload.segments);
      } else {
        // 增量更新现有segments的状态和错误信息
        dataPayload.segments.forEach((segment, index) => {
          if (this.editableSegments[index]) {
            this.editableSegments[index].status = segment.status || 'SUCCESS';
            this.editableSegments[index].errorDetail = segment.error_detail || null;
            
            // 如果文本发生变化，也要更新
            if (segment.text && this.editableSegments[index].text !== segment.text) {
              this.editableSegments[index].text = segment.text;
            }
          }
        });
      }
    }
    
    // 处理翻译结果更新
    if (dataPayload.segment_results && Array.isArray(dataPayload.segment_results)) {
      // 确保editableSegments已初始化
      if (this.editableSegments && this.editableSegments.length > 0) {
        dataPayload.segment_results.forEach((segmentResult) => {
          const segmentId = segmentResult.segment_id;
          // 找到对应的segment（通过id或索引）
          let targetSegment = null;
          if (segmentId) {
            // 如果segment_id是数字字符串，转换为数字索引
            if (/^\d+$/.test(segmentId)) {
              const index = parseInt(segmentId) - 1; // 假设segment_id从1开始
              if (index >= 0 && index < this.editableSegments.length) {
                targetSegment = this.editableSegments[index];
              }
            } else {
              // 通过id字段查找
              targetSegment = this.editableSegments.find(seg => seg.id === segmentId);
            }
          }
          
          if (targetSegment) {
            // 更新翻译文本
            if (segmentResult.translated_text) {
              targetSegment.translatedText = segmentResult.translated_text;
            }
            
            // 更新状态
            targetSegment.status = segmentResult.status || 'SUCCESS';
            targetSegment.errorDetail = segmentResult.error_detail || null;
          }
        });
      }
    }
    
    // 处理字幕生成结果更新
    if (dataPayload.srt_content && dataPayload.segments) {
      // 可以根据需要更新相关状态
      console.log('[Store] 收到字幕生成结果，段落数:', dataPayload.segments.length);
    }
  },

  // 生成 trace_id
  _generateTraceId() {
    return generateTraceId();
  },
}; 