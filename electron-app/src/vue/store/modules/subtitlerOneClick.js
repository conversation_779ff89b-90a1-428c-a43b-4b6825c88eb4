/**
 * 字幕一键操作管理模块
 * 包含一键操作相关的状态、getters和actions
 */

export const subtitlerOneClickState = {
  // 一键操作相关
  oneClickOperationInProgress: false, // 跟踪一键操作是否在进行中
  oneClickOperationError: null, // 存储一键操作的错误
  oneClickWorkflowType: 'vid_to_srt_trans', // 默认工作流类型
  oneClickTargetLanguage: 'zh-CN', // 默认目标语言
  oneClickExportFormat: 'srt', // 一键操作的默认导出格式
  oneClickExportLayout: '原文在上', // 使用后端期望的中文布局名称
  oneClickExportLayouts: ['原文在上'], // 多选布局支持
};

export const subtitlerOneClickActions = {
  async performOneClickOperation() {
    if (this.oneClickOperationInProgress) return;
    
    try {
    this.oneClickOperationInProgress = true;
    this.oneClickOperationError = null;
      
      if (!this.uploadedFile) {
        throw new Error('请先上传文件');
      }
      
      // 生成 trace_id
      const traceId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
      
      // 准备请求参数
      const params = {
        file_path: this.uploadedFile.path,
        workflow_type: this.oneClickWorkflowType,
        target_language: this.oneClickTargetLanguage,
        request_word_timestamps: true,
        export_format: this.oneClickExportFormat,
        export_layout: this.oneClickExportLayout,
        export_layouts: this.oneClickExportLayouts,
        auto_save_to_default: false
      };
      
      // 调用一键操作工作流
      const result = await this._handleOneClickWorkflow(params);
      
      // 处理结果
      if (result && result.success) {
        // 根据工作流类型处理结果
        if (this.oneClickWorkflowType === 'vid_to_audio') {
          this.videoToAudioResult = result.video_to_audio_response;
        } else if (this.oneClickWorkflowType === 'vid_to_srt' || this.oneClickWorkflowType === 'audio_to_srt') {
          // 处理字幕结果
          if (result.audio_to_text_response) {
            this.audioToTextResult = result.audio_to_text_response;
            
            // 初始化可编辑段落
            if (this.audioToTextResult.segments && this.audioToTextResult.segments.length > 0) {
      this.initializeEditableSegments();
            }
          }
          
          // 如果有导出结果，更新导出状态
          if (result.export_results) {
            this.exportResults = result.export_results;
            if (result.export_results.length > 0) {
              this.lastExportPath = result.export_results[0].file_path;
            }
          }
        } else if (this.oneClickWorkflowType === 'vid_to_srt_trans' || this.oneClickWorkflowType === 'audio_to_srt_trans') {
          // 处理翻译结果
          if (result.audio_to_text_response) {
            this.audioToTextResult = result.audio_to_text_response;
          }
          
          if (result.translate_subtitles_response) {
            this.translatedSubtitles = result.translate_subtitles_response;
          }
          
          // 初始化可编辑段落
          if (this.audioToTextResult && this.audioToTextResult.segments && this.audioToTextResult.segments.length > 0) {
            this.initializeEditableSegments();
            
            // 如果有翻译结果，更新段落翻译
            if (this.translatedSubtitles && this.translatedSubtitles.segment_results) {
              this._updateEditableSegmentsWithTranslation(this.translatedSubtitles.segment_results);
            }
          }
          
          // 如果有导出结果，更新导出状态
          if (result.export_results) {
            this.exportResults = result.export_results;
            if (result.export_results.length > 0) {
              this.lastExportPath = result.export_results[0].file_path;
            }
          }
        }
      }
      
      return result;
    } catch (error) {
      console.error('One-click operation failed:', error);
      this.oneClickOperationError = error.message || '操作失败';
      throw error;
    } finally {
      this.oneClickOperationInProgress = false;
    }
  },

  // 处理一键操作工作流
  async _handleOneClickWorkflow(params) {
    const traceId = params.trace_id || this._generateTraceId();
    
    // 更新当前处理状态
    this.currentProcessState = {
      traceId,
      workflowType: params.workflow_type,
      currentStageName: '准备一键操作',
      overallPercentage: 0,
      currentMessage: '正在准备处理流程...',
      status: 'IN_PROGRESS',
      errorDetail: null,
      isActive: true,
      finalDataPayload: null
    };

    return new Promise((resolve, reject) => {
      // 监听进度更新
      const progressListener = (progressData) => {
        // 确保是当前操作的进度更新
        if (progressData.traceId && progressData.traceId !== traceId) {
          return;
        }

        // 更新当前处理状态
        this.currentProcessState = {
          ...this.currentProcessState,
          currentStageName: progressData.stageName || this.currentProcessState.currentStageName,
          overallPercentage: progressData.percentage || 0,
          currentMessage: progressData.message || '',
          status: progressData.status || 'IN_PROGRESS',
          errorDetail: progressData.errorDetail || null,
          isActive: progressData.status !== 'SUCCESS' && progressData.status !== 'ERROR',
          finalDataPayload: progressData.dataPayload || this.currentProcessState.finalDataPayload
        };

        // 添加到进度更新列表
        this.progressUpdates.push({
          stageName: progressData.stageName,
          percentage: progressData.percentage,
          message: progressData.message,
          isError: progressData.isError,
          errorMessage: progressData.errorMessage,
          traceId: progressData.traceId,
          status: progressData.status,
          errorDetail: progressData.errorDetail,
          timestamp: Date.now(),
          dataPayload: progressData.dataPayload
        });

        // 处理中间结果更新
        if (progressData.dataPayload) {
          this._updateEditableSegmentsFromProgressData(progressData);
        }

        // 检查是否出错
        if (progressData.isError || progressData.status === 'ERROR') {
          const error = new Error(progressData.errorMessage || '一键操作失败');
          error.detail = progressData.errorDetail;
          reject(error);
        }
      };

      // 添加进度监听器
      const removeProgressListener = window.electronAPI.on('progress-update', progressListener);

      // 调用后端接口
      window.electronAPI.invoke('subtitler-full-workflow', params)
        .then((result) => {
          // 更新最终状态
          this.currentProcessState.isActive = false;
          this.currentProcessState.status = 'SUCCESS';
          this.currentProcessState.finalDataPayload = result;
          
          resolve(result);
        })
        .catch((error) => {
          // 更新错误状态
          this.currentProcessState.isActive = false;
          this.currentProcessState.status = 'ERROR';
          this.currentProcessState.errorDetail = {
            errorCode: 'WORKFLOW_ERROR',
            technicalMessage: error.message,
            userMessage: '一键操作失败',
            context: { workflowType: params.workflow_type }
          };
          
          reject(error);
        })
        .finally(() => {
          // 移除监听器
          if (removeProgressListener) {
            removeProgressListener();
          }
        });
    });
  },

  // Set workflow type for one-click operation
  setOneClickWorkflowType(workflowType) {
    this.oneClickWorkflowType = workflowType;
  },

  // Set target language for one-click operation
  setOneClickTargetLanguage(language) {
    this.oneClickTargetLanguage = language;
  },

  // Set export format for one-click operation
  setOneClickExportFormat(format) {
    this.oneClickExportFormat = format;
  },

  // Set export layout for one-click operation
  setOneClickExportLayout(layout) {
    this.oneClickExportLayout = layout;
  },

  // Set multiple export layouts for one-click operation
  setOneClickExportLayouts(layouts) {
    if (Array.isArray(layouts) && layouts.length > 0) {
      this.oneClickExportLayouts = layouts;
      // 同时更新单选布局（用于兼容性）
      this.oneClickExportLayout = layouts[0];
    } else {
      // 如果传入空数组或无效值，使用默认值
      this.oneClickExportLayouts = ['原文在上'];
      this.oneClickExportLayout = '原文在上';
    }
  },
}; 