/**
 * 字幕导出管理模块
 * 包含导出相关的状态、getters和actions
 */

export const subtitlerExportState = {
  // 导出相关
  exportFilename: '', // 用户定义的文件名（不含扩展名）
  exportFormat: 'srt', // 用户选择的导出格式
  exportLayout: '原文在上', // 使用后端期望的中文布局名称
  exportLayouts: ['原文在上'], // 多选布局
  exportContentSource: 'editable_segments', // 内容来源
  exportContentSources: ['editable_segments'], // 多选内容来源
  exportAutoSaveToDefault: false, // 是否跳过保存对话框
  lastExportPath: null, // 最后成功导出的路径
  exportResults: [], // 存储多个导出结果
};

export const subtitlerExportGetters = {
  // 获取布局后缀用于文件命名
  _getLayoutSuffix: () => (layout) => {
    const layoutSuffixMap = {
      '原文在上': '原文在上',
      '译文在上': '译文在上',
      '仅原文': '仅原文',
      '仅译文': '仅译文',
      'original_top': '原文在上',
      'translation_top': '译文在上',
      'original_only': '仅原文',
      'translation_only': '仅译文'
    };
    return layoutSuffixMap[layout] || layout;
  }
};

export const subtitlerExportActions = {
  setExportFilename(filename) {
    this.exportFilename = filename;
  },
  
  setExportFormat(format) {
    this.exportFormat = format;
  },
  
  setExportLayout(layout) {
    this.exportLayout = layout;
  },
  
  setExportLayouts(layouts) {
    this.exportLayouts = layouts || [];
  },
  
  setExportContentSource(source) {
    this.exportContentSource = source;
  },
  
  setExportContentSources(sources) {
    this.exportContentSources = sources || [];
  },
  
  setExportAutoSaveToDefault(autoSave) {
    this.exportAutoSaveToDefault = autoSave;
  },

  async exportSubtitles(options = {}) {
    // 确定要导出的内容基于exportContentSource
    let segmentsToExport;
    let rawContentToExport = null; // 用于文本或原始段落数据

    switch (this.exportContentSource) {
      case 'transcript_text':
        if (this.audioToTextResult && typeof this.audioToTextResult.transcript === 'string') {
          rawContentToExport = this.audioToTextResult.transcript;
          // 对于TXT格式，我们可以传递原始文本。对于其他格式，我们可能需要创建一个虚拟段落。
          // 或者，TXT的主进程处理程序可以直接使用这个。
          // 为了简单起见，我们将传递它并让主进程决定。
          // 如果格式不是TXT，我们可能需要将这个文本转换为段落结构或显示错误。
          // 现在，我们将传递它并让主进程决定。
          segmentsToExport = [{ id: 'transcript', startTimeMs: 0, endTimeMs: 0, text: rawContentToExport }]; // 简化处理
        } else {
          console.warn('Transcript text selected for export, but no transcript available.');
          this.progressUpdates.push('❌ 错误：选择了转录文本导出，但未找到转录数据');
          return;
        }
        break;
      case 'transcript_segments':
        if (this.audioToTextResult && Array.isArray(this.audioToTextResult.segments)) {
          segmentsToExport = JSON.parse(JSON.stringify(this.audioToTextResult.segments));
        } else {
          console.warn('Transcript segments selected for export, but no segments available.');
          this.progressUpdates.push('❌ 错误：选择了转录片段导出，但未找到片段数据');
          return;
        }
        break;
      case 'translation_result':
        if (this.translatedSubtitles && this.translatedSubtitles.length > 0) {
          // 将translatedSubtitles格式转换为导出格式
          segmentsToExport = this.translatedSubtitles.map(segment => ({
            id: segment.id,
            text: segment.originalText || segment.text, // 原文
            translation: segment.translatedText, // 译文
            startTimeMs: segment.startTimeMs,
            endTimeMs: segment.endTimeMs,
            start_time_ms: segment.start_time_ms,
            end_time_ms: segment.end_time_ms
          }));
          this.progressUpdates.push('📝 使用翻译字幕进行导出');
        } else {
          console.warn('Translation result selected for export, but no translation available.');
          this.progressUpdates.push('⚠️ 警告：未找到翻译结果，使用当前编辑的字幕');
          segmentsToExport = JSON.parse(JSON.stringify(this.editableSegments));
        }
        break;
      case 'editable_segments':
      default:
        if (!this.editableSegments || this.editableSegments.length === 0) {
          console.warn('No segments to export.');
          this.progressUpdates.push('❌ 没有可导出的字幕');
          return;
        }
        // 检查可编辑段落是否有翻译数据
        segmentsToExport = this.editableSegments.map(segment => ({
          id: segment.id,
          text: segment.text,
          translation: segment.translatedText || segment.translation, // 支持两种字段名
          startTimeMs: segment.startTimeMs,
          endTimeMs: segment.endTimeMs,
          start_time_ms: segment.start_time_ms,
          end_time_ms: segment.end_time_ms
        }));
        break;
    }
    
    if (!segmentsToExport && !rawContentToExport) {
      console.warn('No content determined for export.');
      this.progressUpdates.push('Error: Could not determine content to export based on selection.');
      return;
    }

    this.isLoading = true;
    this.lastExportPath = null;
    
    const exportPayload = {
      segments: segmentsToExport, // 这在使用rawContentToExport进行TXT时可能为null
      rawContent: rawContentToExport, // 专门用于TXT或直接内容
      filename: options.filename || this.exportFilename || (this.uploadedFile ? this.uploadedFile.name.split('.').slice(0, -1).join('.') : 'subtitles'),
      format: this.exportFormat,
      layout: this.exportLayout,
      contentSource: this.exportContentSource,
      autoSaveToDefault: this.exportAutoSaveToDefault,
    };

    console.log('Exporting subtitles with payload:', exportPayload);
    this.progressUpdates.push(`Attempting to export as ${exportPayload.format.toUpperCase()}...`);

    try {
      // 首先尝试使用后端SaveSubtitle API
      try {
        this.progressUpdates.push('Using backend SaveSubtitle API...');

        // 为后端API准备段落（与现有处理程序兼容）
        const backendSegments = segmentsToExport.map(segment => ({
          start_time_ms: segment.startTimeMs || segment.start_time_ms || 0,
          end_time_ms: segment.endTimeMs || segment.end_time_ms || 0,
          original_text: segment.text || '',
          translated_text: segment.translation || segment.translatedText || ''
        }));

        const backendPayload = {
          segments: backendSegments,
          format: this.exportFormat,
          layout: this.exportLayout,
          fileNamePrefix: exportPayload.filename,
          auto_save_to_default: this.exportAutoSaveToDefault
        };

        const result = await window.electronAPI.invoke('subtitler-save-subtitle', backendPayload);

        if (result && result.file_path) {
          this.lastExportPath = result.file_path;
          this.progressUpdates.push(`✅ 字幕成功导出到：${result.file_path}`);
          console.log('Backend export successful:', result.file_path);
          return; // 成功，提前退出
        } else {
          throw new Error('Backend SaveSubtitle API returned invalid response');
        }
      } catch (backendError) {
        console.warn('Backend SaveSubtitle failed, using fallback:', backendError);
        this.progressUpdates.push('Backend export failed, using local fallback');
      }

      // 回退到本地导出
      // 通过创建干净的副本确保payload是可序列化的
      const cleanPayload = JSON.parse(JSON.stringify(exportPayload));
      const result = await window.electronAPI.invoke('subtitler:export-subtitles', cleanPayload);
      
      if (result.filePath) {
        this.lastExportPath = result.filePath;
        this.progressUpdates.push(`✅ 字幕成功导出到：${result.filePath}`);
        console.log('Export successful:', result.filePath);
      } else if (result.error) {
        console.error('Export failed:', result.error);
        this.progressUpdates.push(`❌ 导出失败：${result.error}`);
      } else if (result.cancelled) {
        console.log('Export was cancelled by the user.');
        this.progressUpdates.push('⏹️ 用户取消了导出操作');
      } else {
        console.warn('Export completed with an unknown status:', result);
        this.progressUpdates.push('❓ 导出状态未知');
      }
    } catch (error) {
      console.error('Error during exportSubtitles action:', error);
      this.progressUpdates.push(`❌ 导出过程中发生错误：${error.message}`);
    } finally {
      this.isLoading = false;
    }
  },

  // Multi-select export method
  async exportMultipleSubtitles(options = {}) {
    const { filename, contentSources, layouts, format, autoSave } = options;

    this.isLoading = true;
    this.exportResults = [];
    this.progressUpdates = [];

    try {
      let totalExports = contentSources.length * layouts.length;
      let completedExports = 0;

      this.progressUpdates.push(`开始批量导出：${totalExports} 个文件`);

      for (const contentSource of contentSources) {
        for (const layout of layouts) {
          try {
            // Set current export settings
            this.exportContentSource = contentSource;
            this.exportLayout = layout;
            this.exportFormat = format;
            this.exportAutoSaveToDefault = autoSave;

            // Generate filename with suffix
            const suffix = this._generateExportSuffix(contentSource, layout);
            const currentFilename = filename ? `${filename}_${suffix}` : `subtitles_${suffix}`;
            this.exportFilename = currentFilename;

            this.progressUpdates.push(`导出中: ${currentFilename} (${completedExports + 1}/${totalExports})`);

            // Call single export
            await this.exportSubtitles({ filename: currentFilename });

            if (this.lastExportPath) {
              this.exportResults.push({
                contentSource,
                layout,
                format,
                filename: currentFilename,
                path: this.lastExportPath
              });
              completedExports++;
              this.progressUpdates.push(`✅ 完成: ${currentFilename}`);
            }

          } catch (error) {
            console.error(`Export failed for ${contentSource} + ${layout}:`, error);
            this.progressUpdates.push(`❌ 失败: ${contentSource} + ${layout} - ${error.message}`);
          }
        }
      }

      this.progressUpdates.push(`🎉 批量导出完成！成功导出 ${completedExports}/${totalExports} 个文件`);

    } catch (error) {
      console.error('Multi-export error:', error);
      this.progressUpdates.push(`❌ 批量导出失败: ${error.message}`);
    } finally {
      this.isLoading = false;
    }
  },

  // Generate export filename suffix
  _generateExportSuffix(contentSource, layout) {
    const sourceMap = {
      'editable_segments': '编辑版',
      'transcript_text': '原始文本',
      'transcript_segments': '原始片段',
      'translation_result': '翻译版'
    };

    const layoutMap = {
      'original_top': '原文在上',
      'translation_top': '译文在上',
      'original_only': '仅原文',
      'translation_only': '仅译文'
    };

    const sourceLabel = sourceMap[contentSource] || contentSource;
    const layoutLabel = layoutMap[layout] || layout;

    return `${sourceLabel}_${layoutLabel}`;
  },
}; 