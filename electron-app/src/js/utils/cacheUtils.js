/**
 * 缓存控制工具模块
 * 提供全局的缓存控制状态管理
 */

const CACHE_SETTINGS_KEY = 'ai-cache-settings';

/**
 * 获取当前缓存设置
 * @returns {Object} 缓存设置对象
 */
export function getCacheSettings() {
  try {
    const saved = localStorage.getItem(CACHE_SETTINGS_KEY);
    if (saved) {
      return JSON.parse(saved);
    }
  } catch (error) {
    console.error('获取缓存设置失败:', error);
  }
  
  // 返回默认设置
  return {
    skipCacheNextRequest: false
  };
}

/**
 * 检查下次请求是否应该跳过缓存
 * @returns {boolean} 是否跳过缓存
 */
export function shouldSkipCacheNextRequest() {
  const settings = getCacheSettings();
  return settings.skipCacheNextRequest === true;
}

/**
 * 设置下次请求是否跳过缓存
 * @param {boolean} skip - 是否跳过缓存
 */
export function setSkipCacheNextRequest(skip) {
  try {
    const settings = getCacheSettings();
    settings.skipCacheNextRequest = skip;
    localStorage.setItem(CACHE_SETTINGS_KEY, JSON.stringify(settings));
    console.log('缓存设置已更新:', settings);
  } catch (error) {
    console.error('设置缓存设置失败:', error);
  }
}

/**
 * 获取请求参数，包含缓存控制信息
 * 这个函数会自动处理"一次性跳过缓存"的逻辑
 * @param {Object} baseParams - 基础请求参数
 * @returns {Object} 包含缓存控制的请求参数
 */
export function getRequestParamsWithCacheControl(baseParams = {}) {
  const skipCache = shouldSkipCacheNextRequest();
  
  const params = {
    ...baseParams,
    cache_control: {
      skip_cache: skipCache
    }
  };
  
  // 如果本次跳过缓存，使用后自动重置为不跳过
  if (skipCache) {
    setSkipCacheNextRequest(false);
    console.log('[缓存控制] 本次请求跳过缓存，设置已自动重置为使用缓存');
  } else {
    console.log('[缓存控制] 本次请求使用缓存');
  }
  
  return params;
}

/**
 * 监听缓存设置变化
 * @param {Function} callback - 变化回调函数
 * @returns {Function} 取消监听的函数
 */
export function watchCacheSettings(callback) {
  const handleStorageChange = (event) => {
    if (event.key === CACHE_SETTINGS_KEY) {
      const newSettings = getCacheSettings();
      callback(newSettings);
    }
  };
  
  window.addEventListener('storage', handleStorageChange);
  
  // 返回取消监听的函数
  return () => {
    window.removeEventListener('storage', handleStorageChange);
  };
} 