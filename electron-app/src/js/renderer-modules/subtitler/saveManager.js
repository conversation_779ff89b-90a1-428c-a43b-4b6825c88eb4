/**
 * 字幕保存管理器（简化版）
 * 专注于自动保存功能，支持Vue应用使用
 */
import { getFileNameWithoutExtension } from './utils.js';

class SubtitleSaveManager {
    constructor() {
        this.isInitialized = false;
        this.subtitlerClient = null;
    }

    initialize() {
        if (this.isInitialized) return;

        try {
            // 初始化gRPC客户端
            this.initializeGrpcClient();
            this.isInitialized = true;
            console.log('[SaveManager] 字幕保存管理器初始化完成');
        } catch (error) {
            console.error('[SaveManager] 初始化失败:', error);
        }
    }

    initializeGrpcClient() {
        // 使用IPC通信而不是直接的gRPC客户端
        if (window.electronAPI && window.electronAPI.invoke) {
            console.log('[SaveManager] 找到electronAPI，将使用IPC通信进行保存');
            this.subtitlerClient = {
                available: true,
                useIPC: true,
                electronAPI: window.electronAPI
            };
        } else {
            console.error('[SaveManager] electronAPI.invoke 不可用，无法进行保存操作');
            this.subtitlerClient = null;
        }
    }

    async performAutoSave(saveConfig) {
        if (!saveConfig) {
            console.error('[SaveManager-AutoSave] saveConfig is undefined.');
            this.showErrorMessage('自动保存失败：配置错误。');
            return;
        }

        // 确保初始化
        if (!this.isInitialized) {
            this.initialize();
        }

        // 确保 autoSaveToDefault 为 true，因为这是自动保存的预期行为
        const autoSaveRequest = { ...saveConfig, auto_save_to_default: true };

        console.log('[SaveManager-AutoSave] 开始自动保存字幕...', autoSaveRequest);
        try {
            if (!this.subtitlerClient || !this.subtitlerClient.available || !this.subtitlerClient.electronAPI || !this.subtitlerClient.electronAPI.invoke) {
                this.showErrorMessage('自动保存服务不可用。');
                throw new Error('自动保存服务不可用');
            }

            const response = await this.subtitlerClient.electronAPI.invoke('subtitler-save-subtitle', autoSaveRequest);
            console.log('[SaveManager-AutoSave] 自动保存响应:', response);

            if (response && response.saved_to_default && response.file_path) {
                this.showSuccessMessage(`自动保存成功！文件已保存到：${response.file_path}`);
            } else if (response && response.error_message) {
                this.showErrorMessage(`自动保存失败：${response.error_message}`);
            } else {
                this.showErrorMessage('自动保存失败：服务器响应无效或未保存到默认位置。');
            }
        } catch (error) {
            console.error('[SaveManager-AutoSave] 自动保存过程中发生错误:', error);
            this.showErrorMessage(`自动保存失败：${error.message || '未知错误'}`);
        }
    }

    showSuccessMessage(message) {
        console.log('[SaveManager] 成功:', message);
        
        // 可以在这里添加UI提示，比如toast通知
        if (window.showToast) {
            window.showToast(message, 'success');
        }
    }

    showErrorMessage(message) {
        console.error('[SaveManager] 错误:', message);
        
        // 可以在这里添加UI提示，比如toast通知
        if (window.showToast) {
            window.showToast(message, 'error');
        } else {
            // Vue环境下避免使用alert，使用console记录
            console.error('[SaveManager] Error (no toast available):', message);
        }
    }
}

// 创建单例实例
const subtitleSaveManagerInstance = new SubtitleSaveManager();

// 导出performAutoSave函数供apiClient.js使用
export const performAutoSave = (saveConfig) => {
    return subtitleSaveManagerInstance.performAutoSave(saveConfig);
};

// 导出其他可能需要的功能
export const initialize = () => subtitleSaveManagerInstance.initialize();
export const subtitleSaveManager = subtitleSaveManagerInstance;
export default subtitleSaveManagerInstance;

console.log("subtitler/saveManager.js (simplified version) loaded"); 