syntax = "proto3";

package monkeyfx.api.v1.subtitler;

import "google/protobuf/struct.proto"; // Added for ass_style_options
import "google/protobuf/any.proto"; // Added for flexible data payload
// import "common/common.proto"; // No direct usage of common.BaseResponse in current messages

option java_multiple_files = true;
option java_package = "com.monkeyfx.grpc.api.v1.subtitler";
option java_outer_classname = "SubtitlerProto";
option go_package = "github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/subtitler";

// 操作状态枚举
enum OperationStatus {
  OPERATION_STATUS_UNSPECIFIED = 0;
  OPERATION_STATUS_SUCCESS = 1;
  OPERATION_STATUS_FAILURE = 2;
  OPERATION_STATUS_PARTIAL_SUCCESS = 3;
  OPERATION_STATUS_IN_PROGRESS = 4;
}

// 错误详情
message ErrorDetail {
  string error_code = 1;
  string technical_message = 2;
  string user_message = 3;
  map<string, string> context = 4;
}

// 进度更新消息
message ProgressUpdate {
  string trace_id = 1;                    // 全链路追踪ID
  string stage_name = 2;                  // 细粒度阶段名
  int32 percentage = 3;                   // 0-100, 当前操作的进度
  string message = 4;                     // 人类可读的描述信息
  OperationStatus status = 5;             // 明确的状态
  ErrorDetail error_detail = 6;           // Optional
  google.protobuf.Any data = 7;           // Optional, for intermediate/final results
  
  // 兼容旧版本的字段，将逐步弃用
  bool is_error = 8 [deprecated = true];
  string error_message = 9 [deprecated = true];
  oneof final_result {
    VideoToAudioResponse video_to_audio_response = 10;
    AudioToTextResponse audio_to_text_response = 11;
    GenerateSubtitlesResponse generate_subtitles_response = 12;
    TranslateSubtitlesResponse translate_subtitles_response = 13;
    ProcessVideoToTranslatedSubtitlesResponse process_video_to_translated_subtitles_response = 14;
  }
}

service Subtitler {
  rpc VideoToAudio(VideoToAudioRequest) returns (stream ProgressUpdate);
  rpc AudioToText(AudioToTextRequest) returns (stream ProgressUpdate);
  rpc GenerateSubtitles(GenerateSubtitlesRequest) returns (stream ProgressUpdate);
  rpc TranslateSubtitles(TranslateSubtitlesRequest) returns (stream ProgressUpdate);
  rpc ProcessVideoToTranslatedSubtitles(ProcessVideoToTranslatedSubtitlesRequest) returns (stream ProgressUpdate);
  
  // 新增：字幕保存服务
  rpc SaveSubtitle(SaveSubtitleRequest) returns (SaveSubtitleResponse);
  rpc BatchSaveSubtitle(BatchSaveSubtitleRequest) returns (BatchSaveSubtitleResponse);
  
  // 新增：缓存管理服务
  rpc ClearCache(ClearCacheRequest) returns (ClearCacheResponse);
}

message VideoToAudioRequest {
  string video_path = 1;
  string trace_id = 2;  // 全链路追踪ID
}

message VideoToAudioResponse {
  string audio_path = 1;
  bytes audio_data = 2;
  string trace_id = 3;  // 全链路追踪ID
}

message AudioToTextRequest {
  string audio_path = 1;
  bytes audio_data = 2;
  bool request_word_timestamps = 3;
  bool skip_cache = 4;
  string trace_id = 5;  // 全链路追踪ID
}

message TimestampedTextSegment {
  string text = 1;
  int64 start_time_ms = 2; // 开始时间，单位毫秒
  int64 end_time_ms = 3;   // 结束时间，单位毫秒
  OperationStatus status = 4; // 该段落的处理状态
  ErrorDetail error_detail = 5; // 可选的错误详情
}

message AudioToTextResponse {
  repeated TimestampedTextSegment segments = 1;
  string trace_id = 2;  // 全链路追踪ID
  int32 total_segments_processed = 3;
  int32 successful_segments = 4;
  int32 failed_segments = 5;
}

message GenerateSubtitlesRequest {
  string text = 1;
  string audio_path = 2;
  bool skip_cache = 3;
  string trace_id = 4;  // 全链路追踪ID
}

message GenerateSubtitlesResponse {
  string srt_content = 1;
  string ass_content = 2;
  string trace_id = 3;  // 全链路追踪ID
}

message TranslateSubtitlesRequest {
  string subtitle_content = 1;
  string target_language = 2;
  bool skip_cache = 3;
  string trace_id = 4;  // 全链路追踪ID
}

// 翻译段落结果
message TranslatedSegmentResult {
  string segment_id = 1;
  string original_text = 2;
  string translated_text = 3;
  OperationStatus status = 4;
  ErrorDetail error_detail = 5;
}

message TranslateSubtitlesResponse {
  string translated_subtitle_content = 1;
  string trace_id = 2;  // 全链路追踪ID
  repeated TranslatedSegmentResult segment_results = 3;
  int32 total_segments_processed = 4;
  int32 successful_segments = 5;
  int32 failed_segments = 6;
}

message ProcessVideoToTranslatedSubtitlesRequest {
  string video_path = 1;
  string target_language = 2;
  string trace_id = 3;  // 全链路追踪ID
}

message ProcessVideoToTranslatedSubtitlesResponse {
  string translated_subtitle_content = 1;
  string trace_id = 2;  // 全链路追踪ID
}

// 新增：字幕保存请求
message SaveSubtitleRequest {
  string subtitle_content = 1;           // 字幕内容
  string format = 2;                     // 输出格式：srt, ass, txt, json, vtt
  string layout = 3;                     // 布局：原文在上, 译文在上, 仅原文, 仅译文, 双语并排
  string file_name = 4;                  // 文件名（不含扩展名）
  string original_content = 5;           // 原文内容（用于双语字幕）
  string translated_content = 6;         // 翻译内容（用于双语字幕）
  repeated SubtitleSegment segments = 7; // 字幕片段（带时间戳）
  bool auto_save_to_default = 8;         // 是否自动保存到默认目录
  optional google.protobuf.Struct ass_style_options = 9; // ASS字幕样式选项
  string trace_id = 10;  // 全链路追踪ID
}

message SaveSubtitleResponse {
  string file_path = 1;         // 保存的文件路径
  bytes file_data = 2;          // 文件内容的二进制数据（用于下载）
  string file_name = 3;         // 最终文件名
  int64 file_size = 4;          // 文件大小（字节）
  bool saved_to_default = 5;    // 是否已保存到默认目录
  string format = 6;            // 文件格式 (e.g., "srt", "txt")
  string layout = 7;            // 字幕布局 (e.g., "仅原文", "原文在上")
  string content_source = 8;    // 内容来源 (e.g., "原始字幕", "翻译字幕")
  string original_filename_or_title = 9; // 用于生成描述性文件名的基础标题或原始文件名
  string trace_id = 10;  // 全链路追踪ID
  OperationStatus status = 11;
  ErrorDetail error_detail = 12;
}

// 字幕片段定义
message SubtitleSegment {
  int32 start_time = 1;         // 开始时间（毫秒）
  int32 end_time = 2;           // 结束时间（毫秒）
  string original_text = 3;     // 原文
  string translated_text = 4;   // 翻译文本
  OperationStatus status = 5;   // 该段落的处理状态
  ErrorDetail error_detail = 6; // 可选的错误详情
}

// 批量保存字幕请求
message BatchSaveSubtitleRequest {
  repeated string formats = 1;           // 输出格式列表：srt, ass, txt, json, vtt
  repeated string layouts = 2;           // 布局列表：原文在上, 译文在上, 仅原文, 仅译文, 双语并排
  repeated string content_sources = 3;   // 内容来源列表：current-tab, transcript, segments, srt-ass, translation
  string file_name_prefix = 4;           // 文件名前缀
  string original_content = 5;           // 原文内容（用于双语字幕）
  string translated_content = 6;         // 翻译内容（用于双语字幕）
  repeated SubtitleSegment segments = 7; // 字幕片段（带时间戳）
  bool auto_save_to_default = 8;         // 是否自动保存到默认目录
  bool translation_requested = 9;        // 用户是否请求了翻译
  optional google.protobuf.Struct ass_style_options = 10; // ASS字幕样式选项
  string trace_id = 11;  // 全链路追踪ID
}

message BatchSaveSubtitleResponse {
  repeated SaveSubtitleResponse files = 1;  // 生成的文件列表
  string trace_id = 2;  // 全链路追踪ID
  OperationStatus status = 3;
  ErrorDetail error_detail = 4;
}

// 缓存管理相关消息
message ClearCacheRequest {
  string cache_type = 1; // 缓存类型：all, transcription, translation, subtitle
  string trace_id = 2;  // 全链路追踪ID
}

message ClearCacheResponse {
  bool success = 1;
  string message = 2;
  string trace_id = 3;  // 全链路追踪ID
  OperationStatus status = 4;
  ErrorDetail error_detail = 5;
}