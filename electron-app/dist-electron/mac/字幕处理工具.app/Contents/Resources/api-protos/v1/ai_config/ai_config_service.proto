syntax = "proto3";

package v1.ai_config;

option go_package = "github.com/monkeyfx/electron-python-grpc-pex-demo/gen/go/v1/ai_config;ai_config_pb";
option java_package = "com.example.grpc.v1.ai_config";
option java_multiple_files = true;

message AIProviderConfigProto {
  string provider_id = 1;
  string provider_type = 2;
  string display_name = 3;
  bool is_enabled = 4;
  map<string, string> credentials = 5;
  map<string, string> attributes = 6;
  map<string, string> metadata = 7;
}

message UpdateAIConfigurationsRequest {
  repeated AIProviderConfigProto configs = 1;
}

message UpdateAIConfigurationsResponse {
  bool success = 1;
  string message = 2;
}

service AIConfigurationService {
  rpc UpdateAIConfigurations (UpdateAIConfigurationsRequest) returns (UpdateAIConfigurationsResponse);
}