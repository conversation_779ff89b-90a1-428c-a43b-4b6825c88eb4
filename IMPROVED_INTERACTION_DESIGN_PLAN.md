# 前后端交互与多状态处理优化设计方案

## 1. 核心目标

1.  **清晰一致的状态定义与传递**: 确保从后端核心模块到前端UI，状态信息的结构和含义保持一致和明确。
2.  **高效的错误处理与反馈**: 能够清晰地报告整体错误和部分失败，并为前端提供足够的信息以优雅地展示给用户。
3.  **提升前端状态管理的直观性与响应性**: 使前端能够更直接地响应流式数据和中间结果，减少状态更新的间接性。
4.  **增强系统的可维护性与可调试性**: 通过引入Trace ID和更规范的日志记录，简化问题排查。
5.  **优化用户体验**: 提供更平滑、更及时的进度反馈，以及更明确的错误提示。

## 2. 后端改进

### 2.1. 统一核心模块回调 (例如 `translate.py`)

所有核心处理模块的 `update_callback` 统一接收以下结构的参数字典 `callback_payload`:

```python
{
  "trace_id": str,       # 全链路追踪ID
  "stage_name": str,     # 细粒度阶段名 (e.g., "TRANSLATE_CHUNK_SUCCESS")
  "percentage": int,     # 0-100, 当前操作的进度
  "message": str,        # 人类可读的描述信息
  "status": "SUCCESS" | "FAILURE" | "PARTIAL_SUCCESS" | "IN_PROGRESS", # 明确的状态
  "data": Optional[Dict],# 与回调相关的具体数据负载 (e.g., 成功处理的条目)
  "error": Optional[Dict] # 结构化错误信息 (e.g., 失败的条目及其原因)
}
```
**示例**: 对于部分翻译失败，`data` 可能包含 `{"successful_segments": [...]}`，`error` 可能包含 `{"failed_segments": [{"id": "s2", "original_text": "...", "error_code": "API_ERROR", ...}]}`。
**移除**: 不再在翻译结果字符串中嵌入错误标记如 `||TRANSLATION_ERROR`。

### 2.2. `TranscriptThread.py` 适配与增强

*   方法接收并传递 `trace_id`。
*   内部进度回调适配器直接消费上述统一的 `callback_payload`。
*   `_create_progress` 函数生成的字典与 `callback_payload` 结构保持一致。

### 2.3. Protobuf 定义 (`api_protos/v1/subtitler/subtitler.proto`)

*   **`trace_id`**: 在所有相关请求消息（`VideoToAudioRequest`, `AudioToTextRequest`, `TranslateSubtitlesRequest`等）和 `ProgressUpdate` 消息中添加 `string trace_id` 字段。
*   **`OperationStatus` 枚举**:
    ```protobuf
    enum OperationStatus {
      OPERATION_STATUS_UNSPECIFIED = 0;
      OPERATION_STATUS_SUCCESS = 1;
      OPERATION_STATUS_FAILURE = 2;
      OPERATION_STATUS_PARTIAL_SUCCESS = 3;
      OPERATION_STATUS_IN_PROGRESS = 4;
    }
    ```
*   **`ErrorDetail` 消息**:
    ```protobuf
    message ErrorDetail {
      string error_code = 1;
      string technical_message = 2;
      string user_message = 3;
      map<string, string> context = 4;
    }
    ```
*   **`ProgressUpdate` 消息**:
    ```protobuf
    message ProgressUpdate {
      string trace_id = 1;
      string stage_name = 2;
      int32 percentage = 3;
      string message = 4;
      OperationStatus status = 5;
      ErrorDetail error_detail = 6;         // Optional
      google.protobuf.Any data = 7;         // Optional, for intermediate/final results
    }
    ```
    (使用 `google.protobuf.Any` 或精心设计的 `oneof` 结构来承载 `data`)
*   **针对性响应消息 (例如 `TranslateSubtitlesResponse`)**:
    ```protobuf
    message TranslatedSegmentResult {
      string segment_id = 1;
      string original_text = 2;
      string translated_text = 3;
      OperationStatus status = 4;
      ErrorDetail error_detail = 5;       // Optional
    }

    message TranslateSubtitlesResponse {
      string trace_id = 1;
      repeated TranslatedSegmentResult segment_results = 2;
      int32 total_segments_processed = 3;
      int32 successful_segments = 4;
      int32 failed_segments = 5;
    }
    ```
    (`AudioToTextResponse` 等也可以采用类似包含 `segment_results` 的结构)

### 2.4. gRPC 服务实现 (`subtitle_service.py`)

*   从请求中获取 `trace_id` 并向下传递。
*   更新 `_convert_progress_dict_to_pb` 以将 `TranscriptThread` 的输出准确映射到新的 `ProgressUpdate` Protobuf 结构，包括处理 `trace_id`, `status`, `ErrorDetail` 和 `google.protobuf.Any data`。

## 3. Electron 主进程改进 (`subtitler-workflow-handlers.js`)

1.  **传递 `trace_id`**: 从 IPC 请求获取 `trace_id`，设置到 gRPC 请求中。
2.  **处理增强的 `ProgressUpdate`**:
    *   `callGrpcStreamStep` 提取 `trace_id`, `stage_name`, `percentage`, `message`, `status`, `error_detail` (转为JS对象), 以及 `data` (解包 `google.protobuf.Any` 或访问 `oneof` 内容)。
    *   将这些完整的结构化信息作为 `progress` 对象通过 `progress-update` IPC 事件发送给渲染进程。
3.  **最终结果**: `callGrpcStreamStep` 的 Promise 解析值应包含最终的 `data` 负载、`status` 和 `error_detail`。

## 4. 前端渲染进程改进 (`subtitlerStore.js`, Vue 组件)

### 4.1. Store Action 生成 `trace_id`

在发起操作的 action 开始时生成 `trace_id` (e.g., UUID) 并向下传递。

### 4.2. 集中的流处理器/协调器 (`subtitlerStore.js`)

*   实现核心的 `_handleWorkflowStream` action/函数：
    1.  接收 `trace_id` 和操作参数。
    2.  调用 `window.electronAPI.invoke(...)`，传递 `trace_id`。
    3.  注册 `progress-update` IPC 事件监听器 (可基于 `trace_id` 过滤)。
    4.  **监听器逻辑**:
        *   更新 `currentProcessState` 对象 (包含 `traceId`, `workflowType`, `currentStageName`, `overallPercentage`, `currentMessage`, `status`, `errorDetail`, `isActive`, `finalDataPayload`)。
        *   将收到的完整、结构化的 `progress` 对象推入 `progressUpdates` 数组。
        *   **处理 `progress.data`**: 若包含中间结果 (如部分翻译的 `segmentResults`)，则直接更新相关的细粒度 Store 状态 (如 `editableSegments` 中对应条目的 `translatedText`, `status`, `errorDetail`)。
    5.  处理 `invoke` Promise 的 `resolve` (更新 `currentProcessState.finalDataPayload`, `status`, `isActive`；更新最终的 `Result` 状态如 `translatedSubtitles`) 和 `reject` (更新 `currentProcessState.errorDetail`, `status`, `isActive`)。
    6.  操作完成或出错时移除监听器。

### 4.3. Store State 结构

*   **`currentProcessState`**: 对象，跟踪当前活动操作的宏观状态。
*   **`progressUpdates`**: 数组，存储结构化的进度对象 (每个都包含 `traceId`, `stageName`, `percentage`, `message`, `status`, `errorDetail`, `data` 负载)。
*   **`editableSegments`**: 数组，每个 segment 对象增加 `status: ('PENDING' | 'SUCCESS' | 'FAILURE')` 和 `errorDetail: (Object | null)` 字段。
*   **移除**: 冗余的独立文本/百分比进度状态。

### 4.4. Vue 组件

*   从 `currentProcessState` 获取总体进度、消息、状态。
*   `CollapsibleLog` 消费 `progressUpdates` 显示带 `traceId` 的结构化日志。
*   字幕编辑器等组件绑定到 `editableSegments`，其实时更新将反映流式中间结果（包括部分失败）。
*   错误展示组件消费 `currentProcessState.errorDetail` 和 `editableSegments` 中各条目的 `errorDetail`。

## 5. 并发控制与进度平滑性

*   **后端**: 考虑减小核心模块的批处理大小 (`batch_num`) 以获得更频繁的进度回调。
*   **前端**: 利用 `ProgressUpdate.data` 中的中间结果逐步更新UI，即使用户感知的进度更平滑。
*   **细化进度报告**: 后端 `stage_name` 更细化，`percentage` 准确反映当前阶段或总体进度。

## 6. 方案优势总结

*   **状态一致性**: 统一的状态结构贯穿始终。
*   **错误处理增强**: 结构化错误，明确处理部分失败。
*   **前端响应性提升**: 更直接地利用中间数据，UI更动态。
*   **可调试性**: 全链路 `trace_id`。
*   **可维护性**: 职责清晰，逻辑集中。

这个方案旨在构建一个更健壮、用户友好且易于维护的系统。